// Forum System Types
export interface ForumCategory {
  id: string
  course_id: string
  name: 'announcements' | 'general' | 'questions'
  type: 'announcement' | 'chat' | 'forum'
  description: string | null
  settings: {
    daily_limit?: number | null
    requires_approval?: boolean
    allow_images?: boolean
    max_message_length?: number
  }
  created_at: string
}

export interface ForumTopic {
  id: string
  category_id: string
  user_id: string
  title: string
  description: string | null
  is_solved: boolean
  is_pinned: boolean
  is_locked: boolean
  message_count: number
  participant_count: number
  last_message_at: string
  last_message_by: string | null
  created_at: string
  updated_at: string
  
  // Relations
  category?: ForumCategory
  user?: {
    id: string
    name: string
    username: string
  }
  last_message_user?: {
    id: string
    name: string
    username: string
  }
}

export interface ForumMessage {
  id: string
  category_id: string
  topic_id: string | null
  user_id: string
  parent_message_id: string | null
  content: string
  message_type: 'text' | 'image' | 'system'
  referenced_files: Array<{
    file_id: string
    file_name: string
  }>
  image_url: string | null
  reactions: Record<string, string[]> // {"👍": ["user1"], "❤️": ["user2"]}
  is_edited: boolean
  edited_at: string | null
  created_at: string
  
  // Relations
  user?: {
    id: string
    name: string
    username: string
    avatar_url?: string
  }
  parent_message?: ForumMessage
  replies?: ForumMessage[]
}



export interface ForumDailyLimit {
  user_id: string
  category_id: string
  post_date: string
  message_count: number
}

export interface ForumTopicParticipant {
  topic_id: string
  user_id: string
  joined_at: string
  notifications_enabled: boolean
  
  // Relations
  user?: {
    id: string
    name: string
    username: string
  }
}

// API Response Types
export interface ForumCategoryWithStats extends ForumCategory {
  message_count: number
  last_message?: {
    content: string
    created_at: string
    user: {
      name: string
      username: string
    }
  }
  online_users: number
}

export interface ForumTopicWithDetails extends ForumTopic {
  participants: ForumTopicParticipant[]
  recent_messages: ForumMessage[]
}

// UI State Types
export interface ForumUIState {
  selectedCategory: ForumCategory | null
  selectedTopic: ForumTopic | null
  isTyping: boolean
  messages: ForumMessage[]
  topics: ForumTopic[]
}

// Message Creation Types
export interface CreateMessageInput {
  content: string
  category_id: string
  topic_id?: string
  parent_message_id?: string
  referenced_files?: Array<{
    file_id: string
    file_name: string
  }>
  image_file?: File
  image_url?: string | null
}

export interface CreateTopicInput {
  title: string
  description?: string
  category_id: string
  initial_message: string
}

// Real-time Event Types
export interface ForumRealtimeEvent {
  type: 'message' | 'typing' | 'presence' | 'topic_update'
  payload: any
  user_id: string
  timestamp: string
} 