"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { fileOperations } from "@/lib/supabase"
import { cn } from "@/lib/utils"
import { 
  Heart, Download, Eye, Plus, Upload, 
  FileText, BookOpen, Target, GraduationCap, 
  MoreVertical, Star, Image as ImageIcon,
  Loader2, FolderOpen
} from "lucide-react"

interface LectureNotesTabProps {
  courseId: string
  user: any
  isEnrolled: boolean
}

interface FileRecord {
  id: string
  name: string
  type: string
  size: number
  category: string
  description?: string
  page_count?: number
  like_count: number
  download_count: number
  user_liked: boolean
  created_at: string
  uploader: {
    name: string
    username: string
    is_username_public: boolean
  }
  storage_path: string
}

export function LectureNotesTab({ courseId, user, isEnrolled }: LectureNotesTabProps) {
  const [files, setFiles] = useState<FileRecord[]>([])
  const [activeFilter, setActiveFilter] = useState('all')
  const [selectedFile, setSelectedFile] = useState<FileRecord | null>(null)
  const [showUpload, setShowUpload] = useState(false)
  const [loading, setLoading] = useState(true)

  const categories = [
    { id: 'all', label: 'All Files', icon: FolderOpen, color: 'text-white/80' },
    { id: 'top_rated', label: 'Top Rated', icon: Star, color: 'text-yellow-400' },
    { id: 'lecture_notes', label: 'Lecture Notes', icon: BookOpen, color: 'text-blue-400' },
    { id: 'summaries', label: 'Summaries', icon: FileText, color: 'text-green-400' },
    { id: 'practice_materials', label: 'Practice Materials', icon: Target, color: 'text-purple-400' },
    { id: 'exams', label: 'Exams', icon: GraduationCap, color: 'text-red-400' },
    { id: 'others', label: 'Others', icon: MoreVertical, color: 'text-gray-400' }
  ]

  useEffect(() => {
    loadFiles()
  }, [courseId, activeFilter])

  const loadFiles = async () => {
    setLoading(true)
    try {
      const { data, error } = await fileOperations.getCourseFiles(courseId, activeFilter)
      if (!error && data) {
        setFiles(data)
      } else {
        console.error('Error loading files:', error)
        setFiles([])
      }
    } catch (error) {
      console.error('Error loading files:', error)
      setFiles([])
    }
    setLoading(false)
  }

  const handleLike = async (fileId: string) => {
    try {
      const { error } = await fileOperations.toggleFileLike(fileId)
      if (!error) {
        await loadFiles() // Refresh to update like counts
      }
    } catch (error) {
      console.error('Error toggling like:', error)
    }
  }

  const handleAddToLearning = async (fileId: string) => {
    try {
      const { error } = await fileOperations.addToLearning(fileId)
      if (!error) {
        // Show success message or update UI
        console.log('Added to learning path successfully')
      }
    } catch (error) {
      console.error('Error adding to learning:', error)
    }
  }

  const handleDownload = async (fileId: string) => {
    try {
      const { data, error } = await fileOperations.downloadFile(fileId)
      if (!error && data) {
        // Create download link
        const link = document.createElement('a')
        link.href = data.url
        link.download = data.fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        // Refresh files to update download count
        await loadFiles()
      }
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(c => c.id === category)
    const Icon = categoryData?.icon || FileText
    return <Icon className="h-4 w-4" />
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      lecture_notes: 'bg-blue-500/10 text-blue-400 border-blue-400/20',
      summaries: 'bg-green-500/10 text-green-400 border-green-400/20',
      practice_materials: 'bg-purple-500/10 text-purple-400 border-purple-400/20',
      exams: 'bg-red-500/10 text-red-400 border-red-400/20',
      others: 'bg-gray-500/10 text-gray-400 border-gray-400/20'
    }
    return colors[category as keyof typeof colors] || colors.others
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileTypeIcon = (type: string) => {
    if (type.includes('pdf')) return <FileText className="h-6 w-6 text-red-400" />
    if (type.includes('word') || type.includes('document')) return <FileText className="h-6 w-6 text-blue-400" />
    if (type.includes('image')) return <ImageIcon className="h-6 w-6 text-green-400" />
    return <FileText className="h-6 w-6 text-gray-400" />
  }

  const getDisplayName = (uploader: FileRecord['uploader']) => {
    if (uploader.is_username_public && uploader.username) {
      return uploader.username
    }
    return uploader.name || 'Anonymous'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-purple-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white">Lecture Notes & Materials</h3>
          <p className="text-white/70 text-sm">Browse and share course materials (PDF, Word, Images)</p>
        </div>
        {isEnrolled && (
          <Button 
            onClick={() => setShowUpload(true)}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload Material
          </Button>
        )}
      </div>

      {/* Category Filters */}
      <div className="flex gap-2 overflow-x-auto pb-2">
        {categories.map((category) => {
          const isActive = activeFilter === category.id
          return (
            <Button
              key={category.id}
              variant={isActive ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveFilter(category.id)}
              className={cn(
                "whitespace-nowrap transition-all duration-200",
                isActive
                  ? "bg-purple-500 text-white scale-105 shadow-lg"
                  : "border-white/20 text-white/80 hover:bg-white/10 hover:scale-105"
              )}
            >
              <category.icon className={cn("h-3 w-3 mr-1", category.color)} />
              {category.label}
            </Button>
          )
        })}
      </div>

      {/* Files Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {files.map((file) => (
          <Card 
            key={file.id} 
            className="bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-200 group cursor-pointer hover:scale-[1.02]"
            onClick={() => setSelectedFile(file)}
          >
            <CardContent className="p-4">
              {/* File Preview */}
              <div className="aspect-[4/3] bg-white/10 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden">
                <div className="text-white/60 flex flex-col items-center gap-2">
                  {getFileTypeIcon(file.type)}
                  <span className="text-xs text-white/40 uppercase font-medium">
                    {file.type.split('/')[1] || 'File'}
                  </span>
                </div>
                
                {/* Overlay with actions */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="border-white/40 text-white hover:bg-white/20"
                    onClick={(e) => {
                      e.stopPropagation()
                      setSelectedFile(file)
                    }}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  {isEnrolled && (
                    <>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="border-white/40 text-white hover:bg-white/20"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleAddToLearning(file.id)
                        }}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="border-white/40 text-white hover:bg-white/20"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDownload(file.id)
                        }}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </>
                  )}
                </div>
              </div>

              {/* File Info */}
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <h4 className="text-white font-medium text-sm line-clamp-2 flex-1">
                    {file.name}
                  </h4>
                </div>

                {file.description && (
                  <p className="text-white/70 text-xs line-clamp-2">
                    {file.description}
                  </p>
                )}

                {/* Metadata */}
                <div className="flex items-center gap-2 text-xs">
                  <Badge variant="outline" className={getCategoryColor(file.category)}>
                    {getCategoryIcon(file.category)}
                    <span className="ml-1 capitalize">{file.category.replace('_', ' ')}</span>
                  </Badge>
                  {file.page_count && (
                    <span className="text-white/60">{file.page_count} pages</span>
                  )}
                </div>

                <div className="text-xs text-white/60">
                  <span>{formatFileSize(file.size)}</span>
                </div>

                {/* Stats and actions */}
                <div className="flex items-center justify-between pt-2 border-t border-white/10">
                  <div className="flex items-center gap-3 text-xs text-white/60">
                    <span className="flex items-center gap-1">
                      <Heart className="w-3 h-3" />
                      {file.like_count}
                    </span>
                    <span className="flex items-center gap-1">
                      <Download className="w-3 h-3" />
                      {file.download_count || 0}
                    </span>
                  </div>
                  
                  {isEnrolled && (
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="h-6 w-6 p-0 text-white/60 hover:text-red-400"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleLike(file.id)
                      }}
                    >
                      <Heart className={cn("w-4 h-4", file.user_liked ? 'fill-current text-red-400' : '')} />
                    </Button>
                  )}
                </div>

                {/* Upload info */}
                <p className="text-white/50 text-xs">
                  By {getDisplayName(file.uploader)} • {new Date(file.created_at).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {files.length === 0 && !loading && (
        <Card className="bg-white/5 backdrop-blur-xl border-white/10 border-dashed">
          <CardContent className="p-8 text-center">
            <FolderOpen className="w-12 h-12 text-white/30 mx-auto mb-4" />
            <h4 className="text-white font-medium mb-2">
              {activeFilter === 'all' ? 'No materials yet' : `No ${activeFilter.replace('_', ' ')} found`}
            </h4>
            <p className="text-white/70 text-sm mb-4">
              {activeFilter === 'all' 
                ? 'Be the first to share course materials with your classmates'
                : `No files in the ${activeFilter.replace('_', ' ')} category yet`
              }
            </p>
            {isEnrolled && activeFilter === 'all' && (
              <Button onClick={() => setShowUpload(true)}>
                <Upload className="w-4 h-4 mr-2" />
                Upload First Material
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Upload Modal - We'll create this component next */}
      {showUpload && (
        <UploadModal 
          courseId={courseId}
          onClose={() => setShowUpload(false)}
          onUploadComplete={() => {
            setShowUpload(false)
            loadFiles()
          }}
        />
      )}

      {/* File Preview Modal - We'll create this component next */}
      {selectedFile && (
        <FilePreviewModal 
          file={selectedFile}
          onClose={() => setSelectedFile(null)}
          onAddToLearning={handleAddToLearning}
          onLike={handleLike}
          onDownload={handleDownload}
          isEnrolled={isEnrolled}
        />
      )}
    </div>
  )
}

// Import the actual components
import { EnhancedUploadModal } from "./enhanced-upload-modal"
import { FilePreviewModal } from "./file-preview-modal"

// Wrapper for the upload modal
function UploadModal({ courseId, onClose, onUploadComplete }: {
  courseId: string
  onClose: () => void
  onUploadComplete: () => void
}) {
  return (
    <EnhancedUploadModal 
      courseId={courseId}
      onClose={onClose}
      onUploadComplete={onUploadComplete}
    />
  )
} 