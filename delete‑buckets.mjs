// delete‑buckets.mjs
import { createClient } from '@supabase/supabase-js'

const KEEP = ['course-files', 'forum-images']          // the two you want to keep
const url  = 'https://xnlouvugoogeouvnntdc.supabase.co'             // e.g. https://xyz.supabase.co
const key  = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhubG91dnVnb29nZW91dm5udGRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjAyMjIsImV4cCI6MjA2ODE5NjIyMn0.80_0WbfCwMBiPrhTE66EUXXes8IHii4qorQhJWHN7vE'         // service‑role key

const supabase = createClient(url, key)

const { data: buckets, error } = await supabase.storage.listBuckets()
if (error) throw error

for (const b of buckets) {
  if (KEEP.includes(b.id)) continue          // skip the two you’re keeping

  console.log(`🧹  Emptying ${b.id} …`)
  await supabase.storage.emptyBucket(b.id)

  console.log(`🗑  Deleting ${b.id} …`)
  await supabase.storage.deleteBucket(b.id)
}
console.log('✅  Done')
