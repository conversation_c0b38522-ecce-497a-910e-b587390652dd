"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { 
  Brain,
  Send,
  Sparkles,
  FileText,
  Lightbulb,
  MessageSquare,
  Zap,
  Maximize2,
  RefreshCw,
  Copy,
  ThumbsUp,
  ThumbsDown,
  BookOpen,
  Upload,
  Mic,
  Image,
  PlusCircle
} from "lucide-react"

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  sources?: string[]
}

interface QuickAction {
  id: string
  label: string
  icon: React.ReactNode
  action: string
}

interface AIChatSidebarProps {
  onExpand?: () => void
}

export function AIChatSidebar({ onExpand }: AIChatSidebarProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "assistant",
      content: "Hi! I'm your AI study assistant. How can I help you today?",
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  ])
  
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const quickActions: QuickAction[] = [
    {
      id: "explain",
      label: "Explain concept",
      icon: <Brain className="h-3 w-3" />,
      action: "explain"
    },
    {
      id: "summarize",
      label: "Summarize",
      icon: <FileText className="h-3 w-3" />,
      action: "summarize"
    },
    {
      id: "practice",
      label: "Practice quiz",
      icon: <Lightbulb className="h-3 w-3" />,
      action: "practice"
    },
    {
      id: "flashcards",
      label: "Create flashcards",
      icon: <Sparkles className="h-3 w-3" />,
      action: "flashcards"
    }
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: generateAIResponse(inputValue),
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        sources: ["Lecture Notes.pdf", "Chapter 3.docx"]
      }
      
      setMessages(prev => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const generateAIResponse = (query: string): string => {
    // Simple response generation for demo
    if (query.toLowerCase().includes('explain')) {
      return `I'd be happy to explain that concept! Based on your course materials, here's a clear explanation with relevant examples and context.`
    } else if (query.toLowerCase().includes('summarize')) {
      return `Here's a concise summary of the key points from your materials, highlighting the most important concepts and takeaways.`
    } else {
      return `Great question! Let me help you with that based on your uploaded course materials and study notes.`
    }
  }

  const handleQuickAction = (action: string) => {
    const actionPrompts = {
      explain: "Explain the main concept from my latest uploaded material",
      summarize: "Summarize the key points from today's lecture notes",
      practice: "Create 5 practice questions from my recent study materials",
      flashcards: "Generate flashcards from my uploaded documents"
    }
    
    setInputValue(actionPrompts[action as keyof typeof actionPrompts] || action)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          <div className="flex-1 text-center">
            <h3 className="text-sm font-semibold text-white">AI Assistant</h3>
            <p className="text-xs text-white/60">Always ready to help</p>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white/60 hover:text-white hover:bg-white/10"
              onClick={() => setMessages([messages[0]])}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white/60 hover:text-white hover:bg-white/10"
              onClick={onExpand}
            >
              <Maximize2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-3 border-b border-white/10">
        <h4 className="text-xs font-medium text-white/80 mb-2">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className="h-8 text-xs text-white/70 hover:text-white hover:bg-white/10 justify-start p-2"
              onClick={() => handleQuickAction(action.action)}
            >
              {action.icon}
              <span className="ml-1 truncate">{action.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-3 space-y-3">
        {messages.map((message) => (
          <div key={message.id} className={cn(
            "flex gap-2",
            message.type === 'user' ? "justify-end" : "justify-start"
          )}>
            {message.type === 'assistant' && (
              <Avatar className="w-6 h-6 mt-0.5">
                <AvatarFallback className="bg-purple-600 text-white text-xs">
                  AI
                </AvatarFallback>
              </Avatar>
            )}
            
            <div className={cn(
              "max-w-[80%] rounded-lg p-2 text-xs",
              message.type === 'user'
                ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                : "bg-slate-800/80 text-white border border-white/10"
            )}>
              <p className="text-xs leading-relaxed">{message.content}</p>
              
              {/* Sources */}
              {message.sources && message.sources.length > 0 && (
                <div className="mt-2 pt-2 border-t border-white/20">
                  <div className="flex flex-wrap gap-1">
                    {message.sources.slice(0, 2).map((source, index) => (
                      <Badge 
                        key={index} 
                        variant="secondary" 
                        className="text-xs px-1 py-0 h-4 bg-white/20 text-white/80"
                      >
                        <FileText className="h-2 w-2 mr-1" />
                        {source.split('.')[0].slice(0, 8)}...
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex items-center justify-between mt-1">
                <span className="text-xs text-white/60">{message.timestamp}</span>
                {message.type === 'assistant' && (
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0 text-white/60 hover:text-white">
                      <Copy className="h-2 w-2" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0 text-white/60 hover:text-white">
                      <ThumbsUp className="h-2 w-2" />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {message.type === 'user' && (
              <Avatar className="w-6 h-6 mt-0.5">
                <AvatarFallback className="bg-blue-600 text-white text-xs">
                  U
                </AvatarFallback>
              </Avatar>
            )}
          </div>
        ))}
        
        {isLoading && (
          <div className="flex gap-2">
            <Avatar className="w-6 h-6 mt-0.5">
              <AvatarFallback className="bg-purple-600 text-white text-xs">
                AI
              </AvatarFallback>
            </Avatar>
            <div className="bg-slate-800/80 rounded-lg p-2 border border-white/10">
              <div className="flex items-center gap-1">
                <div className="w-1 h-1 bg-white/60 rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <span className="text-xs text-white/60 ml-2">Thinking...</span>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Usage Stats */}
      <div className="p-3 border-t border-white/10">
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="text-center">
            <p className="text-white/60">Today</p>
            <p className="text-white font-medium">23 queries</p>
          </div>
          <div className="text-center">
            <p className="text-white/60">Accuracy</p>
            <p className="text-green-400 font-medium">94%</p>
          </div>
        </div>
      </div>

      {/* Input Area */}
      <div className="p-3 border-t border-white/10">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Ask anything..."
              className="text-xs h-8 bg-slate-800/50 border-white/20 text-white placeholder:text-white/50 pr-8"
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1 h-6 w-6 text-white/60 hover:text-white"
            >
              <Mic className="h-3 w-3" />
            </Button>
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            size="icon"
            className="h-8 w-8 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          >
            <Send className="h-3 w-3" />
          </Button>
        </div>
        
        <p className="text-xs text-white/60 mt-2 text-center">
          AI responses based on your materials
        </p>
      </div>
    </div>
  )
} 