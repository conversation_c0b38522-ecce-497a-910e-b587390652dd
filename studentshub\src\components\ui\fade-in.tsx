"use client"

import { useEffect, useRef, ReactNode } from "react"
import { cn } from "@/lib/utils"

interface FadeInProps {
  children: ReactNode
  className?: string
  delay?: number
  duration?: number
  direction?: 'up' | 'down' | 'left' | 'right' | 'none'
  distance?: number
}

export function FadeIn({ 
  children, 
  className, 
  delay = 0, 
  duration = 0.6,
  direction = 'up',
  distance = 30
}: FadeInProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    // Set initial state
    element.style.opacity = '0'
    element.style.transform = getInitialTransform(direction, distance)
    element.style.transition = `opacity ${duration}s ease-out, transform ${duration}s ease-out`

    // Create intersection observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              element.style.opacity = '1'
              element.style.transform = 'translate3d(0, 0, 0) scale(1)'
            }, delay * 1000)
            observer.unobserve(element)
          }
        })
      },
      { threshold: 0.1 }
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [delay, duration, direction, distance])

  return (
    <div ref={ref} className={cn("will-change-transform", className)}>
      {children}
    </div>
  )
}

function getInitialTransform(direction: string, distance: number): string {
  switch (direction) {
    case 'up':
      return `translate3d(0, ${distance}px, 0)`
    case 'down':
      return `translate3d(0, -${distance}px, 0)`
    case 'left':
      return `translate3d(${distance}px, 0, 0)`
    case 'right':
      return `translate3d(-${distance}px, 0, 0)`
    default:
      return 'translate3d(0, 0, 0) scale(0.9)'
  }
}

// Staggered children animation
interface StaggeredFadeInProps {
  children: ReactNode[]
  className?: string
  staggerDelay?: number
  initialDelay?: number
}

export function StaggeredFadeIn({ 
  children, 
  className, 
  staggerDelay = 0.1, 
  initialDelay = 0 
}: StaggeredFadeInProps) {
  return (
    <div className={className}>
      {children.map((child, index) => (
        <FadeIn 
          key={index} 
          delay={initialDelay + (index * staggerDelay)}
        >
          {child}
        </FadeIn>
      ))}
    </div>
  )
} 