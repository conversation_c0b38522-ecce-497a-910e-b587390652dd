-- Database Verification and Cleanup for StudentsHub Lecture Notes
-- This script ensures the files table is properly set up for the lecture notes feature

-- Your files table already has the correct structure!
-- This script just adds any missing indexes and ensures data consistency

-- Update size column to handle larger files (current: integer, should be: bigint)
-- Integer max is ~2GB, but we allow 50MB files, so integer should be fine
-- But let's make it bigint for future-proofing
ALTER TABLE files ALTER COLUMN size TYPE bigint;

-- Ensure all required indexes exist for optimal performance
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at);
CREATE INDEX IF NOT EXISTS idx_files_type ON files(type);
CREATE INDEX IF NOT EXISTS idx_files_name ON files(name);
CREATE INDEX IF NOT EXISTS idx_files_storage_path ON files(storage_path);

-- Ensure any existing files have valid default values
UPDATE files
SET category = 'others'
WHERE category IS NULL OR category = '';

UPDATE files
SET like_count = 0
WHERE like_count IS NULL;

UPDATE files
SET download_count = 0
WHERE download_count IS NULL;

UPDATE files
SET is_featured = false
WHERE is_featured IS NULL;

UPDATE files
SET processed = false
WHERE processed IS NULL;

UPDATE files
SET processing_status = 'pending'
WHERE processing_status IS NULL;

-- Ensure created_at and updated_at are set for any existing records
UPDATE files
SET created_at = NOW()
WHERE created_at IS NULL;

UPDATE files
SET updated_at = NOW()
WHERE updated_at IS NULL;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
