-- Additional Database Field Fixes for StudentsHub Lecture Notes
-- Run this AFTER running database-fixes.sql

-- Ensure the files table has the correct field names that match the code
-- The code expects: name, type, size, storage_path
-- But the original schema might have: original_name, file_type, file_size

-- Add missing columns if they don't exist (with correct names)
ALTER TABLE files 
ADD COLUMN IF NOT EXISTS name TEXT;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS type TEXT;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS size BIGINT;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS storage_path TEXT;

-- Update existing data to use the new column names if the old ones exist
-- This handles cases where the table already had data with different field names
UPDATE files 
SET name = COALESCE(name, original_name)
WHERE name IS NULL AND original_name IS NOT NULL;

UPDATE files 
SET type = COALESCE(type, file_type)
WHERE type IS NULL AND file_type IS NOT NULL;

UPDATE files 
SET size = COALESCE(size, file_size)
WHERE size IS NULL AND file_size IS NOT NULL;

-- Ensure created_at column exists (code expects this)
ALTER TABLE files 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update created_at from upload_date if it exists
UPDATE files 
SET created_at = COALESCE(created_at, upload_date)
WHERE created_at IS NULL AND upload_date IS NOT NULL;

-- Make sure required fields are not null
UPDATE files SET name = 'Unknown File' WHERE name IS NULL;
UPDATE files SET type = 'application/octet-stream' WHERE type IS NULL;
UPDATE files SET size = 0 WHERE size IS NULL;
UPDATE files SET storage_path = '' WHERE storage_path IS NULL;

-- Add NOT NULL constraints for required fields
ALTER TABLE files ALTER COLUMN name SET NOT NULL;
ALTER TABLE files ALTER COLUMN type SET NOT NULL;
ALTER TABLE files ALTER COLUMN size SET NOT NULL;
ALTER TABLE files ALTER COLUMN storage_path SET NOT NULL;
ALTER TABLE files ALTER COLUMN course_id SET NOT NULL;
ALTER TABLE files ALTER COLUMN user_id SET NOT NULL;

-- Ensure indexes exist for performance
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at);
CREATE INDEX IF NOT EXISTS idx_files_type ON files(type);
CREATE INDEX IF NOT EXISTS idx_files_name ON files(name);

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
