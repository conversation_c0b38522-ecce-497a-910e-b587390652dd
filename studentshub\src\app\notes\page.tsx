"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/layout/header"
import {
  PlusCircle,
  FileText,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Share,
  Download,
  BookOpen,
  Tag,
  Calendar,
  Brain,
  Star,
  Pin,
  Copy,
  Archive,
  Sparkles,
  Clock,
  Eye
} from "lucide-react"

interface Note {
  id: string
  title: string
  content: string
  course: string
  courseColor: string
  tags: string[]
  isAiGenerated: boolean
  isPinned: boolean
  isStarred: boolean
  createdAt: string
  updatedAt: string
  summary?: string
}

export default function NotesPage() {
  const [notes] = useState<Note[]>([
    {
      id: "1",
      title: "Neural Network Fundamentals",
      content: "Neural networks are computing systems inspired by biological neural networks. They consist of interconnected nodes (neurons) that process information using connectionist approaches...",
      course: "Machine Learning Basics",
      courseColor: "bg-green-500",
      tags: ["neural-networks", "deep-learning", "ai"],
      isAiGenerated: true,
      isPinned: true,
      isStarred: false,
      createdAt: "2024-01-15",
      updatedAt: "2024-01-15",
      summary: "Overview of neural network architecture and basic concepts"
    },
    {
      id: "2", 
      title: "Marketing Automation Strategies",
      content: "Marketing automation refers to software platforms and technologies designed for marketing departments to more effectively market on multiple channels online...",
      course: "Digital Marketing Strategy",
      courseColor: "bg-purple-500",
      tags: ["automation", "marketing", "strategy"],
      isAiGenerated: false,
      isPinned: false,
      isStarred: true,
      createdAt: "2024-01-14",
      updatedAt: "2024-01-14",
      summary: "Key strategies for implementing marketing automation"
    },
    {
      id: "3",
      title: "Data Structures Review",
      content: "Arrays: Fixed-size sequential collection of elements. Linked Lists: Dynamic data structure with nodes containing data and pointers...",
      course: "Computer Science Fundamentals", 
      courseColor: "bg-blue-500",
      tags: ["data-structures", "algorithms", "programming"],
      isAiGenerated: false,
      isPinned: false,
      isStarred: false,
      createdAt: "2024-01-12",
      updatedAt: "2024-01-13",
      summary: "Review of fundamental data structures and their applications"
    }
  ])

  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    switch (selectedFilter) {
      case "pinned":
        return matchesSearch && note.isPinned
      case "starred":
        return matchesSearch && note.isStarred
      case "ai-generated":
        return matchesSearch && note.isAiGenerated
      default:
        return matchesSearch
    }
  })

  const getNoteIcon = (note: Note) => {
    if (note.isAiGenerated) return <Brain className="h-4 w-4 text-purple-500" />
    return <FileText className="h-4 w-4 text-muted-foreground" />
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container px-4 py-8">
        {/* Header Section */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Study Notes</h1>
            <p className="text-muted-foreground mt-2">
              Organize your knowledge and create AI-powered study materials
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline">
              <Brain className="h-4 w-4 mr-2" />
              Generate with AI
            </Button>
            <Button>
              <PlusCircle className="h-4 w-4 mr-2" />
              New Note
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Notes</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{notes.length}</div>
              <p className="text-xs text-muted-foreground">
                +2 from last week
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI Generated</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{notes.filter(n => n.isAiGenerated).length}</div>
              <p className="text-xs text-muted-foreground">
                33% of total notes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Courses Covered</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                Across all subjects
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Study Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12.5h</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-8 lg:grid-cols-4">
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Search and Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Search & Filter</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search notes..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium">Filter by:</p>
                  <div className="grid gap-2">
                    {[
                      { value: "all", label: "All Notes", count: notes.length },
                      { value: "pinned", label: "Pinned", count: notes.filter(n => n.isPinned).length },
                      { value: "starred", label: "Starred", count: notes.filter(n => n.isStarred).length },
                      { value: "ai-generated", label: "AI Generated", count: notes.filter(n => n.isAiGenerated).length }
                    ].map((filter) => (
                      <Button
                        key={filter.value}
                        variant={selectedFilter === filter.value ? "default" : "ghost"}
                        className="justify-between h-auto p-3"
                        onClick={() => setSelectedFilter(filter.value)}
                      >
                        <span className="text-sm">{filter.label}</span>
                        <span className="text-xs">{filter.count}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Sparkles className="h-4 w-4 mr-2" />
                  AI Study Guide
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  Export All Notes
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Archive className="h-4 w-4 mr-2" />
                  Archive Old Notes
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Tag className="h-4 w-4 mr-2" />
                  Manage Tags
                </Button>
              </CardContent>
            </Card>

            {/* Tags Cloud */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Popular Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {Array.from(new Set(notes.flatMap(note => note.tags))).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs cursor-pointer hover:bg-primary hover:text-primary-foreground">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Toolbar */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <Button 
                  variant={viewMode === "grid" ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  Grid
                </Button>
                <Button 
                  variant={viewMode === "list" ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  List
                </Button>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {filteredNotes.length} notes
                </span>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Sort
                </Button>
              </div>
            </div>

            {/* Notes Grid/List */}
            {viewMode === "grid" ? (
              <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
                {filteredNotes.map((note) => (
                  <Card key={note.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getNoteIcon(note)}
                          <div className={`w-3 h-3 ${note.courseColor} rounded-full`} />
                        </div>
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          {note.isPinned && <Pin className="h-4 w-4 text-muted-foreground" />}
                          {note.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      
                      <div>
                        <CardTitle className="text-lg leading-tight">{note.title}</CardTitle>
                        <CardDescription className="text-xs mt-1">
                          {note.course} • {note.createdAt}
                        </CardDescription>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <p className="text-sm text-muted-foreground line-clamp-3 mb-3">
                        {note.summary || note.content}
                      </p>
                      
                      <div className="flex flex-wrap gap-1 mb-3">
                        {note.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {note.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{note.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm" className="h-8 px-2">
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 px-2">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                        {note.isAiGenerated && (
                          <Badge variant="outline" className="text-xs">
                            <Brain className="h-3 w-3 mr-1" />
                            AI
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                {/* Create Note Card */}
                <Card className="border-dashed border-2 hover:border-primary/50 transition-colors cursor-pointer">
                  <CardContent className="flex flex-col items-center justify-center h-full min-h-[300px] text-center">
                    <div className="rounded-full bg-primary/10 p-4 mb-4">
                      <PlusCircle className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="font-semibold mb-2">Create New Note</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Start writing or generate notes with AI assistance
                    </p>
                    <div className="flex gap-2">
                      <Button size="sm">Write Note</Button>
                      <Button size="sm" variant="outline">
                        <Brain className="h-4 w-4 mr-1" />
                        AI Generate
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              /* List View */
              <div className="space-y-4">
                {filteredNotes.map((note) => (
                  <Card key={note.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="flex flex-col items-center gap-2 pt-1">
                          {getNoteIcon(note)}
                          <div className={`w-3 h-3 ${note.courseColor} rounded-full`} />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h3 className="font-semibold text-lg">{note.title}</h3>
                              <p className="text-sm text-muted-foreground">
                                {note.course} • Created {note.createdAt}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {note.isPinned && <Pin className="h-4 w-4 text-muted-foreground" />}
                              {note.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                              {note.isAiGenerated && (
                                <Badge variant="outline" className="text-xs">
                                  <Brain className="h-3 w-3 mr-1" />
                                  AI
                                </Badge>
                              )}
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {note.summary || note.content}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {note.tags.map((tag) => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4 mr-1" />
                                Edit
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Share className="h-4 w-4 mr-1" />
                                Share
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
} 