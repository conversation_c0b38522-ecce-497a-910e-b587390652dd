# StudentsHub - Technical Context

## Technology Stack Overview

### Frontend Stack
```typescript
Framework: Next.js 14 (App Router)
Language: TypeScript (Strict mode)
Styling: Tailwind CSS + Shadcn/ui components
State Management: Zustand
File Handling: React Dropzone + Native File API
Rich Text Editor: ContentEditable with HTML formatting
Image Processing: Blob URLs + Object URL management
Charts & Visualizations: Recharts
Authentication: Supabase Auth Client
UI Components: Shadcn/ui + Custom Tooltip system
Testing: Jest + React Testing Library
Build Tool: Turbopack (Next.js default)
Package Manager: npm (with package-lock.json)
```

### Backend & Infrastructure
```yaml
Database: Supabase (PostgreSQL + pgvector extension)
Authentication: Supabase Auth
File Storage: Supabase Storage (S3-compatible)
API Layer: Supabase Edge Functions + REST API
Real-time: Supabase Realtime subscriptions
Vector Search: pgvector for semantic search
Deployment: Vercel (Frontend) + Supabase (Backend)
CDN: Vercel Edge Network
```

### AI & ML Infrastructure
```python
Primary LLM: Google Gemini API
Embedding Models: Google Text Embedding API
Document Processing: Unstructured.io / LangChain
Vector Database: pgvector in Supabase PostgreSQL
AI Orchestration: Custom TypeScript layer
Future Providers: OpenAI, Anthropic, Cohere, Local models
```

### Forum System Architecture
```typescript
Rich Text Editor: ContentEditable with document.execCommand formatting
Image Storage: Supabase Storage with 'forum-images' bucket
File References: Course file linking without duplication
Emoji System: 80+ Unicode emojis with grid picker interface
Real-time Features: Supabase Realtime for live messaging
Database Tables: 6 forum tables with full RLS policies
Layout System: Three-tier horizontal layout (Categories → Topics → Chat)
Mobile Support: Responsive design with touch-optimized controls
Error Handling: Blob URL cleanup and upload failure recovery
```

## Development Environment Setup

### Prerequisites
```bash
Node.js 18+ (LTS recommended)
npm 9+
Git
VS Code (recommended IDE)
Supabase CLI
TypeScript 5+
```

### Environment Variables
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI Provider APIs
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key  # Future use

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
DATABASE_URL=your_postgres_connection_string
```

### Local Development Commands
```bash
# Project setup
npm install
npm run dev              # Start development server
npm run build           # Build for production
npm run test            # Run test suite
npm run lint            # ESLint checking
npm run type-check      # TypeScript checking

# Database operations
npx supabase start      # Start local Supabase
npx supabase migrate up # Run migrations
npx supabase gen types  # Generate TypeScript types
```

## Database Architecture

### Core Schema Design
```sql
-- Users and Authentication (handled by Supabase Auth)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  university TEXT,
  year_of_study INTEGER,
  major TEXT,
  bio TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Course Management
CREATE TABLE courses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  color TEXT DEFAULT '#3B82F6',
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- File Storage and Management
CREATE TABLE files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  original_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  storage_path TEXT NOT NULL,
  processed BOOLEAN DEFAULT FALSE,
  processing_status TEXT DEFAULT 'pending',
  processing_error TEXT,
  upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vector Embeddings for RAG
CREATE TABLE embeddings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES files(id) ON DELETE CASCADE,
  chunk_text TEXT NOT NULL,
  chunk_index INTEGER NOT NULL,
  embedding VECTOR(768), -- Adjust dimension based on model
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Notes
CREATE TABLE notes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT,
  tags TEXT[],
  is_ai_generated BOOLEAN DEFAULT FALSE,
  source_files UUID[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Conversation History
CREATE TABLE ai_conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  query TEXT NOT NULL,
  response TEXT NOT NULL,
  sources JSONB, -- Array of source documents/chunks
  model_used TEXT NOT NULL,
  tokens_used INTEGER,
  response_time INTEGER, -- milliseconds
  satisfaction_rating INTEGER, -- 1-5 scale
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Course Exercises and Learning Paths
CREATE TABLE exercises (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  exercise_type TEXT NOT NULL CHECK (exercise_type IN ('multiple_choice', 'true_false', 'open_question', 'fill_blank', 'matching')),
  content JSONB NOT NULL, -- Exercise data (questions, options, answers)
  difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
  time_limit_minutes INTEGER,
  max_attempts INTEGER DEFAULT 3,
  is_public BOOLEAN DEFAULT TRUE,
  is_ai_generated BOOLEAN DEFAULT FALSE,
  source_files UUID[],
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE exercise_attempts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  exercise_id UUID REFERENCES exercises(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  answers JSONB NOT NULL,
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  time_taken_minutes INTEGER,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personal Learning Paths
CREATE TABLE learning_paths (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  color TEXT DEFAULT '#8B5CF6',
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE learning_path_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  learning_path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
  item_type TEXT NOT NULL CHECK (item_type IN ('file', 'exercise', 'note', 'external_link')),
  item_id UUID, -- References files.id, exercises.id, notes.id
  external_url TEXT,
  title TEXT,
  description TEXT,
  order_index INTEGER DEFAULT 0,
  progress_status TEXT DEFAULT 'not_started' CHECK (progress_status IN ('not_started', 'in_progress', 'completed', 'skipped')),
  completion_date TIMESTAMP WITH TIME ZONE,
  time_spent INTEGER DEFAULT 0,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE learning_path_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  learning_path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  total_items INTEGER DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  progress_percentage INTEGER DEFAULT 0,
  total_time_spent INTEGER DEFAULT 0,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(learning_path_id, user_id)
);

-- Career Tools
CREATE TABLE cvs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content JSONB NOT NULL, -- Structured CV data
  template_id TEXT,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE files ENABLE ROW LEVEL SECURITY;
-- ... (repeat for all tables)

-- Policies for data isolation
CREATE POLICY "Users can only see own profile" ON profiles
  FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can only see own courses" ON courses
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can see own files and public course files" ON files
  FOR SELECT USING (
    auth.uid() = (SELECT user_id FROM courses WHERE id = course_id)
    OR (SELECT is_public FROM courses WHERE id = course_id) = TRUE
  );
```

## API Architecture

### Supabase Edge Functions Structure
```typescript
// File: supabase/functions/process-document/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

serve(async (req) => {
  // Handle document processing pipeline
  // 1. Extract text from uploaded file
  // 2. Chunk text into semantic pieces
  // 3. Generate embeddings
  // 4. Store in vector database
})

// File: supabase/functions/ai-query/index.ts
serve(async (req) => {
  // Handle AI query pipeline
  // 1. Generate query embedding
  // 2. Search vector database
  // 3. Retrieve relevant context
  // 4. Send to LLM with context
  // 5. Return structured response
})
```

### Frontend API Client
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

export const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// lib/ai-client.ts
export class AIClient {
  async queryDocuments(query: string, courseId: string): Promise<AIResponse> {
    // Implementation for AI queries
  }
  
  async generateSummary(fileId: string): Promise<string> {
    // Implementation for document summarization
  }
}
```

## Performance Considerations

### Vector Search Optimization
```sql
-- Create indexes for optimal vector search performance
CREATE INDEX ON embeddings USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX ON embeddings (file_id);
CREATE INDEX ON embeddings (chunk_index);

-- Optimize vector search queries
SET ivfflat.probes = 10; -- Adjust based on dataset size
```

### Caching Strategy
```typescript
// Next.js caching configuration
export const revalidate = 300 // Cache API responses for 5 minutes

// Client-side caching with React Query/SWR
const { data: courses, error } = useSWR(
  ['courses', userId], 
  () => fetchUserCourses(userId),
  { revalidateOnFocus: false, dedupingInterval: 60000 }
)
```

### File Upload Optimization
```typescript
// Chunked file upload for large files
const uploadLargeFile = async (file: File) => {
  const chunkSize = 5 * 1024 * 1024 // 5MB chunks
  const chunks = Math.ceil(file.size / chunkSize)
  
  for (let i = 0; i < chunks; i++) {
    const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize)
    await uploadChunk(chunk, i)
  }
}
```

## Security Implementation

### Input Sanitization
```typescript
import DOMPurify from 'dompurify'
import { z } from 'zod'

// Schema validation for API inputs
const CourseSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).optional()
})

// Sanitize user content
const sanitizeContent = (content: string) => {
  return DOMPurify.sanitize(content, { 
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: []
  })
}
```

### Rate Limiting
```typescript
// Implement rate limiting for AI endpoints
import { Ratelimit } from "@upstash/ratelimit"

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, "1 h"), // 10 requests per hour
})

export async function POST(req: Request) {
  const { success } = await ratelimit.limit("ai-query")
  if (!success) {
    return new Response("Rate limit exceeded", { status: 429 })
  }
  // ... continue with AI query
}
```

## Monitoring & Analytics

### Error Tracking
```typescript
// Error tracking setup
import * as Sentry from "@sentry/nextjs"

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
})

// Custom error logging
export const logError = (error: Error, context: Record<string, any>) => {
  Sentry.withScope((scope) => {
    scope.setContext("additional_context", context)
    Sentry.captureException(error)
  })
}
```

### Performance Monitoring
```typescript
// API response time tracking
export const trackAPIPerformance = (endpoint: string, startTime: number) => {
  const duration = Date.now() - startTime
  
  // Log to analytics service
  analytics.track('api_performance', {
    endpoint,
    duration,
    timestamp: new Date().toISOString()
  })
}
```

## Testing Strategy

### Unit Testing Setup
```typescript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/components/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
}

module.exports = createJestConfig(customJestConfig)
```

### Integration Testing
```typescript
// __tests__/api/courses.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/pages/api/courses'

describe('/api/courses', () => {
  it('creates a course successfully', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: { title: 'Test Course', description: 'Test Description' }
    })
    
    await handler(req, res)
    
    expect(res._getStatusCode()).toBe(201)
    expect(JSON.parse(res._getData())).toMatchObject({
      title: 'Test Course'
    })
  })
})
```

## Advanced Forum Capabilities

### Rich Text Editor Implementation
```typescript
// ContentEditable-based editor with HTML persistence
const editorRef = useRef<HTMLDivElement>(null)

// Formatting commands using document.execCommand
const applyFormatting = (command: string, value?: string) => {
  document.execCommand(command, false, value)
  updateContent() // Sync with React state
}

// Auto-resize based on content with image support
const autoResizeEditor = () => {
  const hasImages = editorRef.current?.querySelector('img')
  const maxHeight = hasImages ? 200 : 120
  const newHeight = Math.min(scrollHeight, maxHeight)
  editorRef.current.style.height = `${Math.max(newHeight, 44)}px`
}
```

### Image Storage & Blob Management
```typescript
// Sophisticated blob URL lifecycle management
const uploadImageToSupabase = async (file: File): Promise<string | null> => {
  const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36)}.${ext}`
  
  const { data, error } = await supabase.storage
    .from('forum-images')
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: false
    })
    
  return supabase.storage
    .from('forum-images')
    .getPublicUrl(fileName).data.publicUrl
}

// Memory leak prevention with automatic cleanup
useEffect(() => {
  return () => {
    pendingImages.forEach(({ localUrl }) => {
      URL.revokeObjectURL(localUrl) // Critical for memory management
    })
  }
}, [])
```

### Professional UX Features
```typescript
// Tooltip system integration
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Loading states with visual feedback
const [isSending, setIsSending] = useState(false)

// Accessibility-focused design
className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
```

## Deployment Configuration

### Vercel Configuration
```json
// vercel.json
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key"
  },
  "build": {
    "env": {
      "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key"
    }
  }
}
```

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy to Vercel
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

This technical context provides the foundation for implementing the StudentsHub platform with modern, scalable technologies. 