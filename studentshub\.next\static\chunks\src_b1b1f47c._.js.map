{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\r\n\r\n// Create a single supabase client for interacting with your database\r\nexport const supabase = createClient(\r\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n)\r\n\r\n// Auth helper functions\r\nexport const auth = {\r\n  signUp: async (email: string, password: string, userData?: any) => {\r\n    const { data, error } = await supabase.auth.signUp({\r\n      email,\r\n      password,\r\n      options: {\r\n        data: userData\r\n      }\r\n    })\r\n    return { data, error }\r\n  },\r\n\r\n  signIn: async (email: string, password: string) => {\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n      email,\r\n      password\r\n    })\r\n    return { data, error }\r\n  },\r\n\r\n  signOut: async () => {\r\n    const { error } = await supabase.auth.signOut()\r\n    return { error }\r\n  },\r\n\r\n  getUser: async () => {\r\n    const { data: { user }, error } = await supabase.auth.getUser()\r\n    return { user, error }\r\n  },\r\n\r\n  getSession: async () => {\r\n    const { data: { session }, error } = await supabase.auth.getSession()\r\n    return { session, error }\r\n  },\r\n\r\n  signInWithGoogle: async () => {\r\n    const { data, error } = await supabase.auth.signInWithOAuth({\r\n      provider: 'google',\r\n      options: {\r\n        redirectTo: `${window.location.origin}/dashboard`\r\n      }\r\n    })\r\n    return { data, error }\r\n  }\r\n}\r\n\r\n// Database helper functions\r\nexport const db = {\r\n  // Users\r\n  createProfile: async (userId: string, profileData: any) => {\r\n    const { data, error } = await supabase\r\n      .from('profiles')\r\n      .insert([{ id: userId, ...profileData }])\r\n    return { data, error }\r\n  },\r\n\r\n  getProfile: async (userId: string) => {\r\n    const { data, error } = await supabase\r\n      .from('profiles')\r\n      .select('*')\r\n      .eq('id', userId)\r\n      .single()\r\n    return { data, error }\r\n  },\r\n\r\n  updateProfile: async (userId: string, profileData: any) => {\r\n    const { data, error } = await supabase\r\n      .from('profiles')\r\n      .upsert({ id: userId, ...profileData })\r\n      .select()\r\n      .single()\r\n    return { data, error }\r\n  },\r\n\r\n  checkUsernameAvailable: async (username: string) => {\r\n    const { data, error } = await supabase\r\n      .from('profiles')\r\n      .select('id')\r\n      .eq('username', username)\r\n      .single()\r\n    \r\n    // If no data found, username is available\r\n    // If data is found, username is taken\r\n    // If there's an error (usually \"no rows\" error), username is available\r\n    const available = !data || (error && (error as any).code === 'PGRST116')\r\n    return { available, error: available ? null : error }\r\n  },\r\n\r\n  // Courses\r\n  createCourse: async (courseData: any) => {\r\n    const { data, error } = await supabase\r\n      .from('courses')\r\n      .insert([courseData])\r\n      .select()\r\n      .single()\r\n    return { data, error }\r\n  },\r\n\r\n  getCourses: async (userId: string) => {\r\n    const { data, error } = await supabase\r\n      .from('courses')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .order('created_at', { ascending: false })\r\n    return { data, error }\r\n  },\r\n\r\n  getEnrolledCourses: async (userId: string) => {\r\n    // Get user's enrollments\r\n    const { data: enrollments, error: enrollmentError } = await supabase\r\n      .from('course_enrollments')\r\n      .select('course_id')\r\n      .eq('user_id', userId)\r\n\r\n    if (enrollmentError || !enrollments || enrollments.length === 0) {\r\n      return { data: [], error: enrollmentError }\r\n    }\r\n\r\n    // Get course details for enrolled courses\r\n    const courseIds = enrollments.map(e => e.course_id)\r\n    const { data: courses, error: coursesError } = await supabase\r\n      .from('courses')\r\n      .select('*')\r\n      .in('id', courseIds)\r\n      .order('created_at', { ascending: false })\r\n\r\n    if (coursesError || !courses) {\r\n      return { data: [], error: coursesError }\r\n    }\r\n\r\n    // Get enrollment counts for these courses\r\n    const { data: allEnrollments } = await supabase\r\n      .from('course_enrollments')\r\n      .select('course_id')\r\n      .in('course_id', courseIds)\r\n\r\n    // Count enrollments per course\r\n    const countMap: { [key: string]: number } = {}\r\n    allEnrollments?.forEach(enrollment => {\r\n      countMap[enrollment.course_id] = (countMap[enrollment.course_id] || 0) + 1\r\n    })\r\n\r\n    // Add enrollment counts to courses\r\n    const processedData = courses.map(course => ({\r\n      ...course,\r\n      enrollment_count: countMap[course.id] || 0\r\n    }))\r\n    \r\n    return { \r\n      data: processedData,\r\n      error: null \r\n    }\r\n  },\r\n\r\n  getCourse: async (courseId: string) => {\r\n    // First get the course\r\n    const { data: course, error: courseError } = await supabase\r\n      .from('courses')\r\n      .select('*')\r\n      .eq('id', courseId)\r\n      .single()\r\n    \r\n    if (courseError || !course) {\r\n      return { data: null, error: courseError }\r\n    }\r\n\r\n    // Then get enrollments with profiles separately\r\n    const { data: enrollments, error: enrollmentError } = await supabase\r\n      .from('course_enrollments')\r\n      .select(`\r\n        id,\r\n        user_id,\r\n        is_tutor,\r\n        enrolled_at\r\n      `)\r\n      .eq('course_id', courseId)\r\n\r\n    // Get profiles for enrolled users\r\n    const userIds = enrollments?.map(e => e.user_id) || []\r\n    let profiles: any[] = []\r\n    if (userIds.length > 0) {\r\n      const { data: profileData } = await supabase\r\n        .from('profiles')\r\n        .select('id, name, username, is_username_public')\r\n        .in('id', userIds)\r\n      profiles = profileData || []\r\n    }\r\n\r\n    // Combine the data\r\n    const enrollmentsWithProfiles = enrollments?.map(enrollment => ({\r\n      ...enrollment,\r\n      profiles: profiles.find(p => p.id === enrollment.user_id) || null\r\n    })) || []\r\n\r\n    const data = {\r\n      ...course,\r\n      course_enrollments: enrollmentsWithProfiles\r\n    }\r\n\r\n    return { data, error: null }\r\n  },\r\n\r\n  getPublicCourses: async (university: string = 'TU-Darmstadt') => {\r\n    // Get courses\r\n    const { data: courses, error: coursesError } = await supabase\r\n      .from('courses')\r\n      .select('*')\r\n      .eq('is_public', true)\r\n      .eq('university', university)\r\n      .order('created_at', { ascending: false })\r\n\r\n    if (coursesError || !courses) {\r\n      return { data: [], error: coursesError }\r\n    }\r\n\r\n    // Get enrollment counts for each course\r\n    const courseIds = courses.map(c => c.id)\r\n    const { data: enrollmentCounts } = await supabase\r\n      .from('course_enrollments')\r\n      .select('course_id')\r\n      .in('course_id', courseIds)\r\n\r\n    // Count enrollments per course\r\n    const countMap: { [key: string]: number } = {}\r\n    enrollmentCounts?.forEach(enrollment => {\r\n      countMap[enrollment.course_id] = (countMap[enrollment.course_id] || 0) + 1\r\n    })\r\n\r\n    // Add enrollment counts to courses\r\n    const processedData = courses.map(course => ({\r\n      ...course,\r\n      enrollment_count: countMap[course.id] || 0\r\n    }))\r\n    \r\n    return { data: processedData, error: null }\r\n  },\r\n\r\n  checkCourseExists: async (title: string, courseCode: string, university: string = 'TU-Darmstadt') => {\r\n    const { data, error } = await supabase\r\n      .from('courses')\r\n      .select('id, title, course_code')\r\n      .eq('is_public', true)\r\n      .eq('university', university)\r\n      .or(`title.eq.${title},course_code.eq.${courseCode}`)\r\n    \r\n    return { \r\n      exists: data && data.length > 0, \r\n      conflictingCourses: data || [],\r\n      error \r\n    }\r\n  },\r\n\r\n  searchCourses: async (searchTerm: string, university: string = 'TU-Darmstadt') => {\r\n    const { data, error } = await supabase\r\n      .from('courses')\r\n      .select(`\r\n        *,\r\n        course_enrollments(count)\r\n      `)\r\n      .eq('is_public', true)\r\n      .eq('university', university)\r\n      .or(`title.ilike.%${searchTerm}%,course_code.ilike.%${searchTerm}%,professor_name.ilike.%${searchTerm}%`)\r\n      .order('enrollment_count', { ascending: false })\r\n    return { data, error }\r\n  },\r\n\r\n  enrollInCourse: async (courseId: string, userId: string) => {\r\n    const { data, error } = await supabase\r\n      .from('course_enrollments')\r\n      .insert([{ course_id: courseId, user_id: userId }])\r\n      .select()\r\n    return { data, error }\r\n  },\r\n\r\n  unenrollFromCourse: async (courseId: string, userId: string) => {\r\n    const { error } = await supabase\r\n      .from('course_enrollments')\r\n      .delete()\r\n      .eq('course_id', courseId)\r\n      .eq('user_id', userId)\r\n    return { error }\r\n  },\r\n\r\n  isUserEnrolled: async (courseId: string, userId: string) => {\r\n    const { data, error } = await supabase\r\n      .from('course_enrollments')\r\n      .select('id')\r\n      .eq('course_id', courseId)\r\n      .eq('user_id', userId)\r\n      .single()\r\n    \r\n    return { \r\n      enrolled: !error && data !== null,\r\n      error: error?.code === 'PGRST116' ? null : error // Ignore \"no rows\" error\r\n    }\r\n  },\r\n\r\n  getUserEnrollments: async (userId: string) => {\r\n    const { data, error } = await supabase\r\n      .from('course_enrollments')\r\n      .select('course_id')\r\n      .eq('user_id', userId)\r\n    \r\n    return { \r\n      enrollments: data?.map(e => e.course_id) || [],\r\n      error \r\n    }\r\n  },\r\n\r\n  getCourseEnrollments: async (courseId: string) => {\r\n    // Get enrollments\r\n    const { data: enrollments, error: enrollmentError } = await supabase\r\n      .from('course_enrollments')\r\n      .select('*')\r\n      .eq('course_id', courseId)\r\n\r\n    if (enrollmentError || !enrollments || enrollments.length === 0) {\r\n      return { data: [], error: enrollmentError }\r\n    }\r\n\r\n    // Get profiles for enrolled users\r\n    const userIds = enrollments.map(e => e.user_id)\r\n    const { data: profiles, error: profileError } = await supabase\r\n      .from('profiles')\r\n      .select('id, name, username, is_username_public')\r\n      .in('id', userIds)\r\n\r\n    // Combine data\r\n    const enrollmentsWithProfiles = enrollments.map(enrollment => ({\r\n      ...enrollment,\r\n      profiles: profiles?.find(p => p.id === enrollment.user_id) || null\r\n    }))\r\n\r\n    return { data: enrollmentsWithProfiles, error: null }\r\n  },\r\n\r\n  getOnlineUsers: async (courseId: string) => {\r\n    // Get online sessions\r\n    const { data: sessions, error: sessionError } = await supabase\r\n      .from('user_sessions')\r\n      .select('*')\r\n      .eq('course_id', courseId)\r\n      .eq('is_online', true)\r\n      .gte('last_seen', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes\r\n\r\n    if (sessionError || !sessions || sessions.length === 0) {\r\n      return { data: [], error: sessionError }\r\n    }\r\n\r\n    // Get profiles for online users\r\n    const userIds = sessions.map(s => s.user_id)\r\n    const { data: profiles, error: profileError } = await supabase\r\n      .from('profiles')\r\n      .select('id, name, username, is_username_public')\r\n      .in('id', userIds)\r\n\r\n    // Combine data\r\n    const sessionsWithProfiles = sessions.map(session => ({\r\n      ...session,\r\n      profiles: profiles?.find(p => p.id === session.user_id) || null\r\n    }))\r\n\r\n    return { data: sessionsWithProfiles, error: null }\r\n  },\r\n\r\n  updateUserSession: async (userId: string, courseId?: string, isOnline: boolean = true) => {\r\n    const { data, error } = await supabase\r\n      .from('user_sessions')\r\n      .upsert({\r\n        user_id: userId,\r\n        course_id: courseId,\r\n        last_seen: new Date().toISOString(),\r\n        is_online: isOnline\r\n      })\r\n    return { data, error }\r\n  },\r\n\r\n  // Files\r\n  uploadFile: async (file: File, bucket: string, path: string) => {\r\n    const { data, error } = await supabase.storage\r\n      .from(bucket)\r\n      .upload(path, file)\r\n    return { data, error }\r\n  },\r\n\r\n  createFileRecord: async (fileData: any) => {\r\n    const { data, error } = await supabase\r\n      .from('files')\r\n      .insert([fileData])\r\n    return { data, error }\r\n  },\r\n\r\n  getFiles: async (courseId: string) => {\r\n    const { data, error } = await supabase\r\n      .from('files')\r\n      .select('*')\r\n      .eq('course_id', courseId)\r\n      .order('created_at', { ascending: false })\r\n    return { data, error }\r\n  },\r\n\r\n  // Notes\r\n  createNote: async (noteData: any) => {\r\n    const { data, error } = await supabase\r\n      .from('notes')\r\n      .insert([noteData])\r\n    return { data, error }\r\n  },\r\n\r\n  getNotes: async (userId: string, courseId?: string) => {\r\n    let query = supabase\r\n      .from('notes')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n\r\n    if (courseId) {\r\n      query = query.eq('course_id', courseId)\r\n    }\r\n\r\n    const { data, error } = await query.order('created_at', { ascending: false })\r\n    return { data, error }\r\n  }\r\n}\r\n\r\n// Enhanced file operations for lecture notes\r\nexport const fileOperations = {\r\n  // Upload file with category and metadata using proper folder structure\r\n  uploadFile: async (courseId: string, file: File, metadata: {\r\n    category: string;\r\n    description?: string;\r\n    page_count?: number;\r\n    displayName?: string;\r\n  }) => {\r\n    try {\r\n      // Get current user\r\n      const { user } = await auth.getUser()\r\n      if (!user) throw new Error('Not authenticated')\r\n\r\n      // Get user profile for university\r\n      const { data: profile } = await db.getProfile(user.id)\r\n      if (!profile) throw new Error('Profile not found')\r\n\r\n      // Get course details for folder structure\r\n      const { data: course } = await db.getCourse(courseId)\r\n      if (!course) throw new Error('Course not found')\r\n\r\n      // Create folder structure: course-files/{university}/{course-title}/{filename}\r\n      const sanitizeName = (name: string) => name.replace(/[^a-zA-Z0-9-_]/g, '_')\r\n      const university = sanitizeName(profile.university || 'Unknown')\r\n      const courseTitle = sanitizeName(course.title)\r\n      const timestamp = Date.now()\r\n      const fileName = `${timestamp}-${sanitizeName(file.name)}`\r\n      const filePath = `${university}/${courseTitle}/${fileName}`\r\n      \r\n      // Upload to storage\r\n      const { data: uploadData, error: uploadError } = await supabase.storage\r\n        .from('course-files')\r\n        .upload(filePath, file, {\r\n          cacheControl: '3600',\r\n          upsert: false\r\n        })\r\n      \r\n      if (uploadError) throw uploadError\r\n\r\n      // Get file URL\r\n      const { data: { publicUrl } } = supabase.storage\r\n        .from('course-files')\r\n        .getPublicUrl(filePath)\r\n\r\n      // Create database record\r\n      const { data, error } = await supabase\r\n        .from('files')\r\n        .insert([{\r\n          course_id: courseId,\r\n          user_id: user.id,\r\n          name: metadata.displayName || file.name,\r\n          type: file.type,\r\n          size: file.size,\r\n          storage_path: filePath,\r\n          category: metadata.category,\r\n          description: metadata.description,\r\n          page_count: metadata.page_count,\r\n          processed: false,\r\n          processing_status: 'pending'\r\n        }])\r\n        .select()\r\n        .single()\r\n      \r\n      return { data, error, publicUrl }\r\n    } catch (error) {\r\n      return { data: null, error, publicUrl: null }\r\n    }\r\n  },\r\n\r\n  // Get files with like status for user\r\n  getCourseFiles: async (courseId: string, category?: string) => {\r\n    try {\r\n      const { user } = await auth.getUser()\r\n      const userId = user?.id\r\n      \r\n      let query = supabase\r\n        .from('files')\r\n        .select(`\r\n          *,\r\n          uploader:profiles!user_id(full_name, email),\r\n          user_liked:file_likes!left(user_id)\r\n        `)\r\n        .eq('course_id', courseId)\r\n        .order('created_at', { ascending: false })\r\n\r\n      // Apply category filter\r\n      if (category && category !== 'all') {\r\n        if (category === 'top_rated') {\r\n          query = query.gte('like_count', 1).order('like_count', { ascending: false })\r\n        } else {\r\n          query = query.eq('category', category)\r\n        }\r\n      }\r\n\r\n      const { data, error } = await query\r\n      \r\n      if (error) return { data: null, error }\r\n\r\n      // Process data to include user_liked boolean and filter user's own likes\r\n      const processedData = data?.map(file => ({\r\n        ...file,\r\n        user_liked: userId ? file.user_liked?.some((like: any) => like.user_id === userId) : false\r\n      }))\r\n\r\n      return { data: processedData, error: null }\r\n    } catch (error) {\r\n      return { data: null, error }\r\n    }\r\n  },\r\n\r\n  // Toggle file like\r\n  toggleFileLike: async (fileId: string) => {\r\n    try {\r\n      const { user } = await auth.getUser()\r\n      if (!user) throw new Error('Not authenticated')\r\n\r\n      // Check if already liked\r\n      const { data: existingLike } = await supabase\r\n        .from('file_likes')\r\n        .select('id')\r\n        .eq('file_id', fileId)\r\n        .eq('user_id', user.id)\r\n        .single()\r\n\r\n      if (existingLike) {\r\n        // Unlike - delete the like\r\n        const { error } = await supabase\r\n          .from('file_likes')\r\n          .delete()\r\n          .eq('id', existingLike.id)\r\n        \r\n        return { data: { liked: false }, error }\r\n      } else {\r\n        // Like - insert new like\r\n        const { error } = await supabase\r\n          .from('file_likes')\r\n          .insert([{ file_id: fileId, user_id: user.id }])\r\n        \r\n        return { data: { liked: true }, error }\r\n      }\r\n    } catch (error) {\r\n      return { data: null, error }\r\n    }\r\n  },\r\n\r\n  // Download file and increment download count\r\n  downloadFile: async (fileId: string) => {\r\n    try {\r\n      // Get file details\r\n      const { data: file, error: fileError } = await supabase\r\n        .from('files')\r\n        .select('storage_path, name')\r\n        .eq('id', fileId)\r\n        .single()\r\n\r\n      if (fileError || !file) throw fileError || new Error('File not found')\r\n\r\n      // Get download URL\r\n      const { data: { publicUrl } } = supabase.storage\r\n        .from('course-files')\r\n        .getPublicUrl(file.storage_path)\r\n\r\n      // Increment download count\r\n      await supabase.rpc('increment_download_count', { file_id: fileId })\r\n\r\n      return { data: { url: publicUrl, fileName: file.name }, error: null }\r\n    } catch (error) {\r\n      return { data: null, error }\r\n    }\r\n  },\r\n\r\n  // Add to learning path\r\n  addToLearning: async (fileId: string, learningPathId?: string) => {\r\n    try {\r\n      const { user } = await auth.getUser()\r\n      if (!user) throw new Error('Not authenticated')\r\n\r\n      // If no learning path specified, use default\r\n      if (!learningPathId) {\r\n        // Get file to determine course\r\n        const { data: file } = await supabase\r\n          .from('files')\r\n          .select('course_id')\r\n          .eq('id', fileId)\r\n          .single()\r\n\r\n        if (!file) throw new Error('File not found')\r\n\r\n        // Get or create default learning path for this course\r\n        let { data: learningPath } = await supabase\r\n          .from('learning_paths')\r\n          .select('id')\r\n          .eq('user_id', user.id)\r\n          .eq('course_id', file.course_id)\r\n          .eq('is_default', true)\r\n          .single()\r\n\r\n        if (!learningPath) {\r\n          // Create default learning path\r\n          const { data: newPath } = await supabase\r\n            .from('learning_paths')\r\n            .insert([{\r\n              user_id: user.id,\r\n              course_id: file.course_id,\r\n              title: 'My Learning Path',\r\n              is_default: true\r\n            }])\r\n            .select('id')\r\n            .single()\r\n          \r\n          learningPath = newPath\r\n        }\r\n\r\n        learningPathId = learningPath?.id\r\n      }\r\n\r\n      if (!learningPathId) throw new Error('Could not determine learning path')\r\n\r\n      // Get current max order index\r\n      const { data: maxOrder } = await supabase\r\n        .from('learning_path_items')\r\n        .select('order_index')\r\n        .eq('learning_path_id', learningPathId)\r\n        .order('order_index', { ascending: false })\r\n        .limit(1)\r\n        .single()\r\n\r\n      const nextOrderIndex = (maxOrder?.order_index || 0) + 1\r\n\r\n      // Add to learning path\r\n      const { data, error } = await supabase\r\n        .from('learning_path_items')\r\n        .insert([{\r\n          learning_path_id: learningPathId,\r\n          item_type: 'file',\r\n          item_id: fileId,\r\n          order_index: nextOrderIndex\r\n        }])\r\n        .select()\r\n        .single()\r\n\r\n      return { data, error }\r\n    } catch (error) {\r\n      return { data: null, error }\r\n    }\r\n  },\r\n\r\n  // Get file details with full metadata\r\n  getFileDetails: async (fileId: string) => {\r\n    try {\r\n      const { user } = await auth.getUser()\r\n      const userId = user?.id\r\n\r\n      const { data, error } = await supabase\r\n        .from('files')\r\n        .select(`\r\n          *,\r\n          uploader:profiles!user_id(full_name, email),\r\n          user_liked:file_likes!left(user_id),\r\n          course:courses(title)\r\n        `)\r\n        .eq('id', fileId)\r\n        .single()\r\n\r\n      if (error) return { data: null, error }\r\n\r\n      // Process user_liked status\r\n      const processedData = {\r\n        ...data,\r\n        user_liked: userId ? data.user_liked?.some((like: any) => like.user_id === userId) : false\r\n      }\r\n\r\n      return { data: processedData, error: null }\r\n    } catch (error) {\r\n      return { data: null, error }\r\n    }\r\n  }\r\n}\r\n\r\n// Types for our database schema\r\nexport interface Profile {\r\n  id: string\r\n  email: string\r\n  full_name: string | null\r\n  avatar_url: string | null\r\n  university: string | null\r\n  year_of_study: number | null\r\n  major: string | null\r\n  bio: string | null\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n\r\nexport interface Course {\r\n  id: string\r\n  user_id: string\r\n  title: string\r\n  description: string | null\r\n  color: string\r\n  is_public: boolean\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n\r\nexport interface FileRecord {\r\n  id: string\r\n  course_id: string\r\n  name: string\r\n  original_name: string\r\n  file_type: string\r\n  file_size: number\r\n  storage_path: string\r\n  processed: boolean\r\n  processing_status: string\r\n  processing_error: string | null\r\n  upload_date: string\r\n}\r\n\r\nexport interface Note {\r\n  id: string\r\n  user_id: string\r\n  course_id: string\r\n  title: string\r\n  content: string | null\r\n  tags: string[]\r\n  is_ai_generated: boolean\r\n  source_files: string[]\r\n  created_at: string\r\n  updated_at: string\r\n} "], "names": [], "mappings": ";;;;;;AAIE;AAJF;;AAGO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD;AAM5B,MAAM,OAAO;IAClB,QAAQ,OAAO,OAAe,UAAkB;QAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,QAAQ,OAAO,OAAe;QAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,SAAS;QACP,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE;QAAM;IACjB;IAEA,SAAS;QACP,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,YAAY;QACV,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QACnE,OAAO;YAAE;YAAS;QAAM;IAC1B;IAEA,kBAAkB;QAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;YAC1D,UAAU;YACV,SAAS;gBACP,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;YACxC;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,MAAM,KAAK;IAChB,QAAQ;IACR,eAAe,OAAO,QAAgB;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;gBAAE,IAAI;gBAAQ,GAAG,WAAW;YAAC;SAAE;QAC1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,YAAY,OAAO;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QACT,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,eAAe,OAAO,QAAgB;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,IAAI;YAAQ,GAAG,WAAW;QAAC,GACpC,MAAM,GACN,MAAM;QACT,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,wBAAwB,OAAO;QAC7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY,UACf,MAAM;QAET,0CAA0C;QAC1C,sCAAsC;QACtC,uEAAuE;QACvE,MAAM,YAAY,CAAC,QAAS,SAAS,AAAC,MAAc,IAAI,KAAK;QAC7D,OAAO;YAAE;YAAW,OAAO,YAAY,OAAO;QAAM;IACtD;IAEA,UAAU;IACV,cAAc,OAAO;QACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QACT,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,YAAY,OAAO;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAC1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,oBAAoB,OAAO;QACzB,yBAAyB;QACzB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,sBACL,MAAM,CAAC,aACP,EAAE,CAAC,WAAW;QAEjB,IAAI,mBAAmB,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC/D,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAgB;QAC5C;QAEA,0CAA0C;QAC1C,MAAM,YAAY,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS;QAClD,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAa;QACzC;QAEA,0CAA0C;QAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,sBACL,MAAM,CAAC,aACP,EAAE,CAAC,aAAa;QAEnB,+BAA+B;QAC/B,MAAM,WAAsC,CAAC;QAC7C,2BAAA,qCAAA,eAAgB,OAAO,CAAC,CAAA;YACtB,QAAQ,CAAC,WAAW,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,SAAS,CAAC,IAAI,CAAC,IAAI;QAC3E;QAEA,mCAAmC;QACnC,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC3C,GAAG,MAAM;gBACT,kBAAkB,QAAQ,CAAC,OAAO,EAAE,CAAC,IAAI;YAC3C,CAAC;QAED,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;IAEA,WAAW,OAAO;QAChB,uBAAuB;QACvB,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,QAAQ;YAC1B,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAY;QAC1C;QAEA,gDAAgD;QAChD,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,sBACL,MAAM,CAAE,mFAMR,EAAE,CAAC,aAAa;QAEnB,kCAAkC;QAClC,MAAM,UAAU,CAAA,wBAAA,kCAAA,YAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,MAAK,EAAE;QACtD,IAAI,WAAkB,EAAE;QACxB,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,YACL,MAAM,CAAC,0CACP,EAAE,CAAC,MAAM;YACZ,WAAW,eAAe,EAAE;QAC9B;QAEA,mBAAmB;QACnB,MAAM,0BAA0B,CAAA,wBAAA,kCAAA,YAAa,GAAG,CAAC,CAAA,aAAc,CAAC;gBAC9D,GAAG,UAAU;gBACb,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,OAAO,KAAK;YAC/D,CAAC,OAAM,EAAE;QAET,MAAM,OAAO;YACX,GAAG,MAAM;YACT,oBAAoB;QACtB;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B;IAEA,kBAAkB;YAAO,8EAAqB;QAC5C,cAAc;QACd,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,cAAc,YACjB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAa;QACzC;QAEA,wCAAwC;QACxC,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QACvC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,SACtC,IAAI,CAAC,sBACL,MAAM,CAAC,aACP,EAAE,CAAC,aAAa;QAEnB,+BAA+B;QAC/B,MAAM,WAAsC,CAAC;QAC7C,6BAAA,uCAAA,iBAAkB,OAAO,CAAC,CAAA;YACxB,QAAQ,CAAC,WAAW,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,SAAS,CAAC,IAAI,CAAC,IAAI;QAC3E;QAEA,mCAAmC;QACnC,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC3C,GAAG,MAAM;gBACT,kBAAkB,QAAQ,CAAC,OAAO,EAAE,CAAC,IAAI;YAC3C,CAAC;QAED,OAAO;YAAE,MAAM;YAAe,OAAO;QAAK;IAC5C;IAEA,mBAAmB,eAAO,OAAe;YAAoB,8EAAqB;QAChF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,0BACP,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,cAAc,YACjB,EAAE,CAAC,AAAC,YAAmC,OAAxB,OAAM,oBAA6B,OAAX;QAE1C,OAAO;YACL,QAAQ,QAAQ,KAAK,MAAM,GAAG;YAC9B,oBAAoB,QAAQ,EAAE;YAC9B;QACF;IACF;IAEA,eAAe,eAAO;YAAoB,8EAAqB;QAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAE,2DAIR,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,cAAc,YACjB,EAAE,CAAC,AAAC,gBAAiD,OAAlC,YAAW,yBAA4D,OAArC,YAAW,4BAAqC,OAAX,YAAW,MACrG,KAAK,CAAC,oBAAoB;YAAE,WAAW;QAAM;QAChD,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,gBAAgB,OAAO,UAAkB;QACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;YAAC;gBAAE,WAAW;gBAAU,SAAS;YAAO;SAAE,EACjD,MAAM;QACT,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,oBAAoB,OAAO,UAAkB;QAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,aAAa,UAChB,EAAE,CAAC,WAAW;QACjB,OAAO;YAAE;QAAM;IACjB;IAEA,gBAAgB,OAAO,UAAkB;QACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,MACP,EAAE,CAAC,aAAa,UAChB,EAAE,CAAC,WAAW,QACd,MAAM;QAET,OAAO;YACL,UAAU,CAAC,SAAS,SAAS;YAC7B,OAAO,CAAA,kBAAA,4BAAA,MAAO,IAAI,MAAK,aAAa,OAAO,MAAM,yBAAyB;QAC5E;IACF;IAEA,oBAAoB,OAAO;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,aACP,EAAE,CAAC,WAAW;QAEjB,OAAO;YACL,aAAa,CAAA,iBAAA,2BAAA,KAAM,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS,MAAK,EAAE;YAC9C;QACF;IACF;IAEA,sBAAsB,OAAO;QAC3B,kBAAkB;QAClB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa;QAEnB,IAAI,mBAAmB,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC/D,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAgB;QAC5C;QAEA,kCAAkC;QAClC,MAAM,UAAU,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAC9C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,YACL,MAAM,CAAC,0CACP,EAAE,CAAC,MAAM;QAEZ,eAAe;QACf,MAAM,0BAA0B,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBAC7D,GAAG,UAAU;gBACb,UAAU,CAAA,qBAAA,+BAAA,SAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,OAAO,MAAK;YAChE,CAAC;QAED,OAAO;YAAE,MAAM;YAAyB,OAAO;QAAK;IACtD;IAEA,gBAAgB,OAAO;QACrB,sBAAsB;QACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,EAAE,CAAC,aAAa,MAChB,GAAG,CAAC,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW,IAAI,iBAAiB;;QAEzF,IAAI,gBAAgB,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtD,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAa;QACzC;QAEA,gCAAgC;QAChC,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,YACL,MAAM,CAAC,0CACP,EAAE,CAAC,MAAM;QAEZ,eAAe;QACf,MAAM,uBAAuB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBACpD,GAAG,OAAO;gBACV,UAAU,CAAA,qBAAA,+BAAA,SAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,OAAO,MAAK;YAC7D,CAAC;QAED,OAAO;YAAE,MAAM;YAAsB,OAAO;QAAK;IACnD;IAEA,mBAAmB,eAAO,QAAgB;YAAmB,4EAAoB;QAC/E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,SAAS;YACT,WAAW;YACX,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW;QACb;QACF,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,QAAQ;IACR,YAAY,OAAO,MAAY,QAAgB;QAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,QACL,MAAM,CAAC,MAAM;QAChB,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,kBAAkB,OAAO;QACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;SAAS;QACpB,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,UAAU,OAAO;QACf,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAC1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,QAAQ;IACR,YAAY,OAAO;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;SAAS;QACpB,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,UAAU,OAAO,QAAgB;QAC/B,IAAI,QAAQ,SACT,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW;QAEjB,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAC3E,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,MAAM,iBAAiB;IAC5B,uEAAuE;IACvE,YAAY,OAAO,UAAkB,MAAY;QAM/C,IAAI;YACF,mBAAmB;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YACnC,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,kCAAkC;YAClC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE;YACrD,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;YAE9B,0CAA0C;YAC1C,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,MAAM,GAAG,SAAS,CAAC;YAC5C,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;YAE7B,+EAA+E;YAC/E,MAAM,eAAe,CAAC,OAAiB,KAAK,OAAO,CAAC,mBAAmB;YACvE,MAAM,aAAa,aAAa,QAAQ,UAAU,IAAI;YACtD,MAAM,cAAc,aAAa,OAAO,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,WAAW,AAAC,GAAe,OAAb,WAAU,KAA2B,OAAxB,aAAa,KAAK,IAAI;YACvD,MAAM,WAAW,AAAC,GAAgB,OAAd,YAAW,KAAkB,OAAf,aAAY,KAAY,OAAT;YAEjD,oBAAoB;YACpB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CACpE,IAAI,CAAC,gBACL,MAAM,CAAC,UAAU,MAAM;gBACtB,cAAc;gBACd,QAAQ;YACV;YAEF,IAAI,aAAa,MAAM;YAEvB,eAAe;YACf,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,gBACL,YAAY,CAAC;YAEhB,yBAAyB;YACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC;gBAAC;oBACP,WAAW;oBACX,SAAS,KAAK,EAAE;oBAChB,MAAM,SAAS,WAAW,IAAI,KAAK,IAAI;oBACvC,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,cAAc;oBACd,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;oBACjC,YAAY,SAAS,UAAU;oBAC/B,WAAW;oBACX,mBAAmB;gBACrB;aAAE,EACD,MAAM,GACN,MAAM;YAET,OAAO;gBAAE;gBAAM;gBAAO;YAAU;QAClC,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM;gBAAO,WAAW;YAAK;QAC9C;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO,UAAkB;QACvC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YACnC,MAAM,SAAS,iBAAA,2BAAA,KAAM,EAAE;YAEvB,IAAI,QAAQ,SACT,IAAI,CAAC,SACL,MAAM,CAAE,mIAKR,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,wBAAwB;YACxB,IAAI,YAAY,aAAa,OAAO;gBAClC,IAAI,aAAa,aAAa;oBAC5B,QAAQ,MAAM,GAAG,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM;gBAC5E,OAAO;oBACL,QAAQ,MAAM,EAAE,CAAC,YAAY;gBAC/B;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;YAE9B,IAAI,OAAO,OAAO;gBAAE,MAAM;gBAAM;YAAM;YAEtC,yEAAyE;YACzE,MAAM,gBAAgB,iBAAA,2BAAA,KAAM,GAAG,CAAC,CAAA;oBAET;uBAFkB;oBACvC,GAAG,IAAI;oBACP,YAAY,UAAS,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC,CAAC,OAAc,KAAK,OAAO,KAAK,UAAU;gBACvF;;YAEA,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAK;QAC5C,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;IACF;IAEA,mBAAmB;IACnB,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YACnC,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,yBAAyB;YACzB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,cACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;YAET,IAAI,cAAc;gBAChB,2BAA2B;gBAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM,aAAa,EAAE;gBAE3B,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;oBAAG;gBAAM;YACzC,OAAO;gBACL,yBAAyB;gBACzB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,CAAC;oBAAC;wBAAE,SAAS;wBAAQ,SAAS,KAAK,EAAE;oBAAC;iBAAE;gBAEjD,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAK;oBAAG;gBAAM;YACxC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;IACF;IAEA,6CAA6C;IAC7C,cAAc,OAAO;QACnB,IAAI;YACF,mBAAmB;YACnB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,sBACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,aAAa,CAAC,MAAM,MAAM,aAAa,IAAI,MAAM;YAErD,mBAAmB;YACnB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,gBACL,YAAY,CAAC,KAAK,YAAY;YAEjC,2BAA2B;YAC3B,MAAM,SAAS,GAAG,CAAC,4BAA4B;gBAAE,SAAS;YAAO;YAEjE,OAAO;gBAAE,MAAM;oBAAE,KAAK;oBAAW,UAAU,KAAK,IAAI;gBAAC;gBAAG,OAAO;YAAK;QACtE,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;IACF;IAEA,uBAAuB;IACvB,eAAe,OAAO,QAAgB;QACpC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YACnC,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,6CAA6C;YAC7C,IAAI,CAAC,gBAAgB;gBACnB,+BAA+B;gBAC/B,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,SAC1B,IAAI,CAAC,SACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,QACT,MAAM;gBAET,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,sDAAsD;gBACtD,IAAI,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,aAAa,KAAK,SAAS,EAC9B,EAAE,CAAC,cAAc,MACjB,MAAM;gBAET,IAAI,CAAC,cAAc;oBACjB,+BAA+B;oBAC/B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,kBACL,MAAM,CAAC;wBAAC;4BACP,SAAS,KAAK,EAAE;4BAChB,WAAW,KAAK,SAAS;4BACzB,OAAO;4BACP,YAAY;wBACd;qBAAE,EACD,MAAM,CAAC,MACP,MAAM;oBAET,eAAe;gBACjB;gBAEA,iBAAiB,yBAAA,mCAAA,aAAc,EAAE;YACnC;YAEA,IAAI,CAAC,gBAAgB,MAAM,IAAI,MAAM;YAErC,8BAA8B;YAC9B,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,uBACL,MAAM,CAAC,eACP,EAAE,CAAC,oBAAoB,gBACvB,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM,GACxC,KAAK,CAAC,GACN,MAAM;YAET,MAAM,iBAAiB,CAAC,CAAA,qBAAA,+BAAA,SAAU,WAAW,KAAI,CAAC,IAAI;YAEtD,uBAAuB;YACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,uBACL,MAAM,CAAC;gBAAC;oBACP,kBAAkB;oBAClB,WAAW;oBACX,SAAS;oBACT,aAAa;gBACf;aAAE,EACD,MAAM,GACN,MAAM;YAET,OAAO;gBAAE;gBAAM;YAAM;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO;QACrB,IAAI;gBAoBqB;YAnBvB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YACnC,MAAM,SAAS,iBAAA,2BAAA,KAAM,EAAE;YAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAE,qKAMR,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,OAAO;gBAAE,MAAM;gBAAM;YAAM;YAEtC,4BAA4B;YAC5B,MAAM,gBAAgB;gBACpB,GAAG,IAAI;gBACP,YAAY,UAAS,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC,CAAC,OAAc,KAAK,OAAO,KAAK,UAAU;YACvF;YAEA,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAK;QAC5C,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { cn } from \"@/lib/utils\"\nimport { \n  ChevronLeft, \n  ChevronRight,\n  Menu,\n  MessageCircle,\n  X\n} from \"lucide-react\"\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  leftSidebar: React.ReactNode\n  rightSidebar: React.ReactNode\n}\n\nexport function DashboardLayout({ children, leftSidebar, rightSidebar }: DashboardLayoutProps) {\n  const [isLeftSidebarOpen, setIsLeftSidebarOpen] = useState(true)\n  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(true)\n  const [isMobile, setIsMobile] = useState(false)\n\n  // Check if mobile on mount and resize\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth < 1024\n      setIsMobile(mobile)\n      \n      // Auto-collapse sidebars on mobile\n      if (mobile) {\n        setIsLeftSidebarOpen(false)\n        setIsRightSidebarOpen(false)\n      } else {\n        setIsLeftSidebarOpen(true)\n        setIsRightSidebarOpen(true)\n      }\n    }\n\n    checkMobile()\n    window.addEventListener('resize', checkMobile)\n    return () => window.removeEventListener('resize', checkMobile)\n  }, [])\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex overflow-hidden\">\n      {/* Mobile Overlay */}\n      {isMobile && (isLeftSidebarOpen || isRightSidebarOpen) && (\n        <div \n          className=\"fixed inset-0 bg-black/50 z-40\"\n          onClick={() => {\n            setIsLeftSidebarOpen(false)\n            setIsRightSidebarOpen(false)\n          }}\n        />\n      )}\n\n      {/* Left Sidebar */}\n      <div className={cn(\n        \"transition-all duration-300 ease-in-out flex-shrink-0 relative\",\n        isLeftSidebarOpen ? \"w-64\" : \"w-0\",\n        isMobile && isLeftSidebarOpen && \"fixed inset-y-0 left-0 z-50 w-64\"\n      )}>\n        <div className={cn(\n          \"h-full bg-slate-900/95 backdrop-blur-xl border-r border-white/10 overflow-hidden\",\n          \"transition-all duration-300 ease-in-out\",\n          isLeftSidebarOpen ? \"w-64\" : \"w-0\"\n        )}>\n          {isLeftSidebarOpen && (\n            <>\n              {leftSidebar}\n              {/* Left Sidebar Close Button */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"absolute top-4 right-2 z-10 h-8 w-8 bg-slate-800/50 hover:bg-slate-700/50 text-white/60 hover:text-white transition-all duration-300\"\n                onClick={() => setIsLeftSidebarOpen(false)}\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      <div className={cn(\n        \"flex-1 flex flex-col min-w-0 relative transition-all duration-300 ease-in-out h-full\",\n        // Add margins when sidebars are closed\n        !isLeftSidebarOpen && !isMobile && \"ml-4\",\n        !isRightSidebarOpen && !isMobile && \"mr-4\"\n      )}>\n        {/* Main Content - Only this should scroll */}\n        <div className=\"flex-1 overflow-y-auto overflow-x-hidden\">\n          <div className={cn(\n            \"min-h-full\",\n            // Add padding to prevent overlap with floating buttons\n            !isLeftSidebarOpen && \"pl-8\"\n          )}>\n            {children}\n          </div>\n        </div>\n      </div>\n\n      {/* Right Sidebar */}\n      <div className={cn(\n        \"transition-all duration-300 ease-in-out flex-shrink-0 relative\",\n        isRightSidebarOpen ? \"w-96\" : \"w-0\",\n        isMobile && isRightSidebarOpen && \"fixed inset-y-0 right-0 z-50 w-96\"\n      )}>\n        <div className={cn(\n          \"h-full bg-slate-900/95 backdrop-blur-xl border-l border-white/10 overflow-hidden\",\n          \"transition-all duration-300 ease-in-out\",\n          isRightSidebarOpen ? \"w-96\" : \"w-0\"\n        )}>\n          {isRightSidebarOpen && (\n            <>\n              {rightSidebar}\n              {/* Right Sidebar Close Button */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"absolute top-4 left-2 z-10 h-8 w-8 bg-slate-800/50 hover:bg-slate-700/50 text-white/60 hover:text-white transition-all duration-300\"\n                onClick={() => setIsRightSidebarOpen(false)}\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Floating Toggle Buttons - Bottom Left */}\n      <div className=\"fixed bottom-6 left-6 z-30 flex flex-col gap-3\">\n        {/* Navigation Sidebar Toggle */}\n        {!isLeftSidebarOpen && (\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className={cn(\n              \"h-12 w-12 rounded-full shadow-lg backdrop-blur-xl transition-all duration-300\",\n              \"bg-slate-900/90 border-white/20 text-white hover:bg-slate-800/90\",\n              \"hover:scale-110 hover:shadow-xl hover:border-white/30\",\n              \"group\"\n            )}\n            onClick={() => setIsLeftSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5 transition-transform group-hover:scale-110\" />\n          </Button>\n        )}\n        \n        {/* AI Chat Sidebar Toggle */}\n        {!isRightSidebarOpen && (\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className={cn(\n              \"h-12 w-12 rounded-full shadow-lg backdrop-blur-xl transition-all duration-300\",\n              \"bg-gradient-to-r from-purple-600/90 to-pink-600/90 border-purple-400/30 text-white\",\n              \"hover:from-purple-500/90 hover:to-pink-500/90 hover:border-purple-300/40\",\n              \"hover:scale-110 hover:shadow-xl\",\n              \"group\"\n            )}\n            onClick={() => setIsRightSidebarOpen(true)}\n          >\n            <MessageCircle className=\"h-5 w-5 transition-transform group-hover:scale-110\" />\n          </Button>\n        )}\n      </div>\n    </div>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAmBO,SAAS,gBAAgB,KAA6D;QAA7D,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAwB,GAA7D;;IAC9B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;yDAAc;oBAClB,MAAM,SAAS,OAAO,UAAU,GAAG;oBACnC,YAAY;oBAEZ,mCAAmC;oBACnC,IAAI,QAAQ;wBACV,qBAAqB;wBACrB,sBAAsB;oBACxB,OAAO;wBACL,qBAAqB;wBACrB,sBAAsB;oBACxB;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;YAEZ,YAAY,CAAC,qBAAqB,kBAAkB,mBACnD,6LAAC;gBACC,WAAU;gBACV,SAAS;oBACP,qBAAqB;oBACrB,sBAAsB;gBACxB;;;;;;0BAKJ,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kEACA,oBAAoB,SAAS,OAC7B,YAAY,qBAAqB;0BAEjC,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oFACA,2CACA,oBAAoB,SAAS;8BAE5B,mCACC;;4BACG;0CAED,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,qBAAqB;0CAEpC,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQjC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wFACA,uCAAuC;gBACvC,CAAC,qBAAqB,CAAC,YAAY,QACnC,CAAC,sBAAsB,CAAC,YAAY;0BAGpC,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,cACA,uDAAuD;wBACvD,CAAC,qBAAqB;kCAErB;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kEACA,qBAAqB,SAAS,OAC9B,YAAY,sBAAsB;0BAElC,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oFACA,2CACA,qBAAqB,SAAS;8BAE7B,oCACC;;4BACG;0CAED,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,sBAAsB;0CAErC,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC;gBAAI,WAAU;;oBAEZ,CAAC,mCACA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iFACA,oEACA,yDACA;wBAEF,SAAS,IAAM,qBAAqB;kCAEpC,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;oBAKnB,CAAC,oCACA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iFACA,sFACA,4EACA,mCACA;wBAEF,SAAS,IAAM,sBAAsB;kCAErC,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMrC;GAzJgB;KAAA", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAGgC;QAHhC,EACtB,SAAS,EACT,GAAG,OACmD,GAHhC;IAItB,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/layout/navigation-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { useRouter, usePathname } from \"next/navigation\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { \r\n  BookOpen,\r\n  Brain,\r\n  FileText,\r\n  FolderOpen,\r\n  Briefcase,\r\n  Settings,\r\n  LogOut,\r\n  Home,\r\n  Star,\r\n  TrendingUp,\r\n  Users,\r\n  Calendar,\r\n  Bell,\r\n  Search,\r\n  Plus,\r\n  GraduationCap,\r\n  MessageCircle\r\n} from \"lucide-react\"\r\n\r\ninterface NavigationItem {\r\n  id: string\r\n  label: string\r\n  icon: React.ReactNode\r\n  href: string\r\n  badge?: string\r\n  isActive?: boolean\r\n  subItems?: NavigationItem[]\r\n}\r\n\r\ninterface NavigationSidebarProps {\r\n  user?: any\r\n  profile?: any\r\n  onSignOut?: () => void\r\n}\r\n\r\nexport function NavigationSidebar({ user, profile, onSignOut }: NavigationSidebarProps) {\r\n  const router = useRouter()\r\n  const pathname = usePathname()\r\n  const [expandedItems, setExpandedItems] = useState<string[]>([])\r\n\r\n  const navigationItems: NavigationItem[] = [\r\n    {\r\n      id: \"dashboard\",\r\n      label: \"Dashboard\",\r\n      icon: <Home className=\"h-5 w-5\" />,\r\n      href: \"/dashboard\",\r\n      isActive: pathname === \"/dashboard\"\r\n    },\r\n    {\r\n      id: \"courses\",\r\n      label: \"My Courses\",\r\n      icon: <BookOpen className=\"h-5 w-5\" />,\r\n      href: \"/courses\",\r\n      badge: \"3\",\r\n      isActive: pathname === \"/courses\"\r\n    },\r\n    {\r\n      id: \"ai-assistant\",\r\n      label: \"AI Assistant\",\r\n      icon: <Brain className=\"h-5 w-5\" />,\r\n      href: \"/ai-assistant\",\r\n      isActive: pathname === \"/ai-assistant\"\r\n    },\r\n    {\r\n      id: \"notes\",\r\n      label: \"My Notes\",\r\n      icon: <FileText className=\"h-5 w-5\" />,\r\n      href: \"/notes\",\r\n      badge: \"12\",\r\n      isActive: pathname === \"/notes\"\r\n    },\r\n    {\r\n      id: \"files\",\r\n      label: \"Files\",\r\n      icon: <FolderOpen className=\"h-5 w-5\" />,\r\n      href: \"/files\",\r\n      isActive: pathname === \"/files\"\r\n    },\r\n    {\r\n      id: \"messages\",\r\n      label: \"Messages\",\r\n      icon: <MessageCircle className=\"h-5 w-5\" />,\r\n      href: \"/messages\",\r\n      badge: \"2\",\r\n      isActive: pathname === \"/messages\"\r\n    },\r\n    {\r\n      id: \"career\",\r\n      label: \"Career Tools\",\r\n      icon: <Briefcase className=\"h-5 w-5\" />,\r\n      href: \"/career\",\r\n      badge: \"New\",\r\n      isActive: pathname.startsWith(\"/career\")\r\n    }\r\n  ]\r\n\r\n  const quickActions = [\r\n    {\r\n      id: \"new-course\",\r\n      label: \"New Course\",\r\n      icon: <Plus className=\"h-4 w-4\" />,\r\n      onClick: () => router.push(\"/courses?create=true\")\r\n    },\r\n    {\r\n      id: \"quick-note\",\r\n      label: \"Quick Note\",\r\n      icon: <FileText className=\"h-4 w-4\" />,\r\n      onClick: () => router.push(\"/notes?create=true\")\r\n    },\r\n    {\r\n      id: \"manage-files\",\r\n      label: \"Manage Files\",\r\n      icon: <FolderOpen className=\"h-4 w-4\" />,\r\n      onClick: () => router.push(\"/files\")\r\n    }\r\n  ]\r\n\r\n  const handleNavigation = (href: string) => {\r\n    router.push(href)\r\n  }\r\n\r\n  const toggleExpanded = (itemId: string) => {\r\n    setExpandedItems(prev =>\r\n      prev.includes(itemId)\r\n        ? prev.filter(id => id !== itemId)\r\n        : [...prev, itemId]\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      {/* User Profile Section */}\r\n      <div className=\"p-4 border-b border-white/10\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <Avatar className=\"w-10 h-10\">\r\n            <AvatarImage src={profile?.avatar_url} />\r\n            <AvatarFallback className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white\">\r\n              {profile?.name ? profile.name.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase() || 'U'}\r\n            </AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"flex-1 min-w-0\">\r\n            <p className=\"text-sm font-medium text-white truncate\">\r\n              {profile?.name || user?.email || 'User'}\r\n            </p>\r\n            <p className=\"text-xs text-white/60 truncate\">\r\n              {profile?.university || 'University Student'}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quick Actions */}\r\n      <div className=\"p-4 border-b border-white/10\">\r\n        <h3 className=\"text-xs font-semibold text-white/80 uppercase tracking-wider mb-3\">\r\n          Quick Actions\r\n        </h3>\r\n        <div className=\"space-y-2\">\r\n          {quickActions.map((action) => (\r\n            <Button\r\n              key={action.id}\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full justify-start h-8 text-white/80 hover:text-white hover:bg-white/10\"\r\n              onClick={action.onClick}\r\n            >\r\n              {action.icon}\r\n              <span className=\"ml-2 text-xs\">{action.label}</span>\r\n            </Button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation Menu */}\r\n      <div className=\"flex-1 p-4 overflow-y-auto\">\r\n        <nav className=\"space-y-2\">\r\n          {navigationItems.map((item) => (\r\n            <div key={item.id}>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className={cn(\r\n                  \"w-full justify-start h-11 transition-all duration-200\",\r\n                  item.isActive\r\n                    ? \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border border-purple-400/30\"\r\n                    : \"text-white/80 hover:text-white hover:bg-white/10\"\r\n                )}\r\n                onClick={() => handleNavigation(item.href)}\r\n              >\r\n                <div className=\"flex items-center justify-between w-full\">\r\n                  <div className=\"flex items-center\">\r\n                    {item.icon}\r\n                    <span className=\"ml-3 font-medium\">{item.label}</span>\r\n                  </div>\r\n                  {item.badge && (\r\n                    <Badge \r\n                      variant=\"secondary\" \r\n                      className={cn(\r\n                        \"text-xs px-2 py-0.5\",\r\n                        item.badge === \"New\" \r\n                          ? \"bg-green-500/20 text-green-300 border-green-400/30\"\r\n                          : \"bg-purple-500/20 text-purple-300 border-purple-400/30\"\r\n                      )}\r\n                    >\r\n                      {item.badge}\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </Button>\r\n            </div>\r\n          ))}\r\n        </nav>\r\n\r\n        <Separator className=\"my-6 border-white/10\" />\r\n\r\n        {/* Study Stats */}\r\n        <div className=\"space-y-4\">\r\n          <h3 className=\"text-xs font-semibold text-white/80 uppercase tracking-wider\">\r\n            Today's Progress\r\n          </h3>\r\n          \r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <TrendingUp className=\"h-4 w-4 text-green-400\" />\r\n                <span className=\"text-sm text-white/80\">Study Time</span>\r\n              </div>\r\n              <span className=\"text-sm font-medium text-white\">2.5h</span>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Brain className=\"h-4 w-4 text-purple-400\" />\r\n                <span className=\"text-sm text-white/80\">AI Queries</span>\r\n              </div>\r\n              <span className=\"text-sm font-medium text-white\">23</span>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <FileText className=\"h-4 w-4 text-blue-400\" />\r\n                <span className=\"text-sm text-white/80\">Notes Created</span>\r\n              </div>\r\n              <span className=\"text-sm font-medium text-white\">5</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Footer Actions */}\r\n      <div className=\"p-4 border-t border-white/10 space-y-2\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          className=\"w-full justify-start text-white/80 hover:text-white hover:bg-white/10\"\r\n          onClick={() => router.push(\"/settings\")}\r\n        >\r\n          <Settings className=\"h-5 w-5\" />\r\n          <span className=\"ml-3\">Settings</span>\r\n        </Button>\r\n        \r\n        <Button\r\n          variant=\"ghost\"\r\n          className=\"w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-500/10\"\r\n          onClick={onSignOut}\r\n        >\r\n          <LogOut className=\"h-5 w-5\" />\r\n          <span className=\"ml-3\">Sign Out</span>\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AA6CO,SAAS,kBAAkB,KAAoD;QAApD,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAA0B,GAApD;QAsGoC;;IArGpE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;YACN,UAAU,aAAa;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM;YACN,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,MAAM;YACN,UAAU,aAAa;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM;YACN,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,MAAM;YACN,UAAU,aAAa;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,MAAM;YACN,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,MAAM;YACN,OAAO;YACP,UAAU,SAAS,UAAU,CAAC;QAChC;KACD;IAED,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,6LAAC,qIAAA,CAAA,cAAW;oCAAC,GAAG,EAAE,oBAAA,8BAAA,QAAS,UAAU;;;;;;8CACrC,6LAAC,qIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,CAAA,oBAAA,8BAAA,QAAS,IAAI,IAAG,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,CAAA,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,MAAM,CAAC,GAAG,WAAW,OAAM;;;;;;;;;;;;sCAGpG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,CAAA,oBAAA,8BAAA,QAAS,IAAI,MAAI,iBAAA,2BAAA,KAAM,KAAK,KAAI;;;;;;8CAEnC,6LAAC;oCAAE,WAAU;8CACV,CAAA,oBAAA,8BAAA,QAAS,UAAU,KAAI;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoE;;;;;;kCAGlF,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,OAAO,OAAO;;oCAEtB,OAAO,IAAI;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB,OAAO,KAAK;;;;;;;+BAPvC,OAAO,EAAE;;;;;;;;;;;;;;;;0BActB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;0CACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA,KAAK,QAAQ,GACT,8FACA;oCAEN,SAAS,IAAM,iBAAiB,KAAK,IAAI;8CAEzC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;kEACV,6LAAC;wDAAK,WAAU;kEAAoB,KAAK,KAAK;;;;;;;;;;;;4CAE/C,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uBACA,KAAK,KAAK,KAAK,QACX,uDACA;0DAGL,KAAK,KAAK;;;;;;;;;;;;;;;;;+BA1BX,KAAK,EAAE;;;;;;;;;;kCAmCrB,6LAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAI7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,OAAO,IAAI,CAAC;;0CAE3B,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;kCAGzB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;;0CAET,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;AAKjC;GA1OgB;;QACC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/layout/ai-chat-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useRef, useEffect } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { \r\n  Brain,\r\n  Send,\r\n  Sparkles,\r\n  FileText,\r\n  Lightbulb,\r\n  MessageSquare,\r\n  Zap,\r\n  Maximize2,\r\n  RefreshCw,\r\n  Copy,\r\n  ThumbsUp,\r\n  ThumbsDown,\r\n  BookOpen,\r\n  Upload,\r\n  Mic,\r\n  Image,\r\n  PlusCircle\r\n} from \"lucide-react\"\r\n\r\ninterface Message {\r\n  id: string\r\n  type: 'user' | 'assistant'\r\n  content: string\r\n  timestamp: string\r\n  sources?: string[]\r\n}\r\n\r\ninterface QuickAction {\r\n  id: string\r\n  label: string\r\n  icon: React.ReactNode\r\n  action: string\r\n}\r\n\r\ninterface AIChatSidebarProps {\r\n  onExpand?: () => void\r\n}\r\n\r\nexport function AIChatSidebar({ onExpand }: AIChatSidebarProps) {\r\n  const [messages, setMessages] = useState<Message[]>([\r\n    {\r\n      id: \"1\",\r\n      type: \"assistant\",\r\n      content: \"Hi! I'm your AI study assistant. How can I help you today?\",\r\n      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    }\r\n  ])\r\n  \r\n  const [inputValue, setInputValue] = useState(\"\")\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const messagesEndRef = useRef<HTMLDivElement>(null)\r\n\r\n  const quickActions: QuickAction[] = [\r\n    {\r\n      id: \"explain\",\r\n      label: \"Explain concept\",\r\n      icon: <Brain className=\"h-3 w-3\" />,\r\n      action: \"explain\"\r\n    },\r\n    {\r\n      id: \"summarize\",\r\n      label: \"Summarize\",\r\n      icon: <FileText className=\"h-3 w-3\" />,\r\n      action: \"summarize\"\r\n    },\r\n    {\r\n      id: \"practice\",\r\n      label: \"Practice quiz\",\r\n      icon: <Lightbulb className=\"h-3 w-3\" />,\r\n      action: \"practice\"\r\n    },\r\n    {\r\n      id: \"flashcards\",\r\n      label: \"Create flashcards\",\r\n      icon: <Sparkles className=\"h-3 w-3\" />,\r\n      action: \"flashcards\"\r\n    }\r\n  ]\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" })\r\n  }\r\n\r\n  useEffect(() => {\r\n    scrollToBottom()\r\n  }, [messages])\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputValue.trim()) return\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      type: \"user\",\r\n      content: inputValue,\r\n      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    }\r\n\r\n    setMessages(prev => [...prev, userMessage])\r\n    setInputValue(\"\")\r\n    setIsLoading(true)\r\n\r\n    // Simulate AI response\r\n    setTimeout(() => {\r\n      const aiResponse: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        type: \"assistant\",\r\n        content: generateAIResponse(inputValue),\r\n        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),\r\n        sources: [\"Lecture Notes.pdf\", \"Chapter 3.docx\"]\r\n      }\r\n      \r\n      setMessages(prev => [...prev, aiResponse])\r\n      setIsLoading(false)\r\n    }, 1500)\r\n  }\r\n\r\n  const generateAIResponse = (query: string): string => {\r\n    // Simple response generation for demo\r\n    if (query.toLowerCase().includes('explain')) {\r\n      return `I'd be happy to explain that concept! Based on your course materials, here's a clear explanation with relevant examples and context.`\r\n    } else if (query.toLowerCase().includes('summarize')) {\r\n      return `Here's a concise summary of the key points from your materials, highlighting the most important concepts and takeaways.`\r\n    } else {\r\n      return `Great question! Let me help you with that based on your uploaded course materials and study notes.`\r\n    }\r\n  }\r\n\r\n  const handleQuickAction = (action: string) => {\r\n    const actionPrompts = {\r\n      explain: \"Explain the main concept from my latest uploaded material\",\r\n      summarize: \"Summarize the key points from today's lecture notes\",\r\n      practice: \"Create 5 practice questions from my recent study materials\",\r\n      flashcards: \"Generate flashcards from my uploaded documents\"\r\n    }\r\n    \r\n    setInputValue(actionPrompts[action as keyof typeof actionPrompts] || action)\r\n  }\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault()\r\n      handleSendMessage()\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      {/* Header */}\r\n      <div className=\"p-4 border-b border-white/10\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex-1 text-center\">\r\n            <h3 className=\"text-sm font-semibold text-white\">AI Assistant</h3>\r\n            <p className=\"text-xs text-white/60\">Always ready to help</p>\r\n          </div>\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-8 w-8 text-white/60 hover:text-white hover:bg-white/10\"\r\n              onClick={() => setMessages([messages[0]])}\r\n            >\r\n              <RefreshCw className=\"h-3 w-3\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-8 w-8 text-white/60 hover:text-white hover:bg-white/10\"\r\n              onClick={onExpand}\r\n            >\r\n              <Maximize2 className=\"h-3 w-3\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quick Actions */}\r\n      <div className=\"p-3 border-b border-white/10\">\r\n        <h4 className=\"text-xs font-medium text-white/80 mb-2\">Quick Actions</h4>\r\n        <div className=\"grid grid-cols-2 gap-2\">\r\n          {quickActions.map((action) => (\r\n            <Button\r\n              key={action.id}\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 text-xs text-white/70 hover:text-white hover:bg-white/10 justify-start p-2\"\r\n              onClick={() => handleQuickAction(action.action)}\r\n            >\r\n              {action.icon}\r\n              <span className=\"ml-1 truncate\">{action.label}</span>\r\n            </Button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chat Messages */}\r\n      <div className=\"flex-1 overflow-y-auto p-3 space-y-3\">\r\n        {messages.map((message) => (\r\n          <div key={message.id} className={cn(\r\n            \"flex gap-2\",\r\n            message.type === 'user' ? \"justify-end\" : \"justify-start\"\r\n          )}>\r\n            {message.type === 'assistant' && (\r\n              <Avatar className=\"w-6 h-6 mt-0.5\">\r\n                <AvatarFallback className=\"bg-purple-600 text-white text-xs\">\r\n                  AI\r\n                </AvatarFallback>\r\n              </Avatar>\r\n            )}\r\n            \r\n            <div className={cn(\r\n              \"max-w-[80%] rounded-lg p-2 text-xs\",\r\n              message.type === 'user'\r\n                ? \"bg-gradient-to-r from-purple-500 to-pink-500 text-white\"\r\n                : \"bg-slate-800/80 text-white border border-white/10\"\r\n            )}>\r\n              <p className=\"text-xs leading-relaxed\">{message.content}</p>\r\n              \r\n              {/* Sources */}\r\n              {message.sources && message.sources.length > 0 && (\r\n                <div className=\"mt-2 pt-2 border-t border-white/20\">\r\n                  <div className=\"flex flex-wrap gap-1\">\r\n                    {message.sources.slice(0, 2).map((source, index) => (\r\n                      <Badge \r\n                        key={index} \r\n                        variant=\"secondary\" \r\n                        className=\"text-xs px-1 py-0 h-4 bg-white/20 text-white/80\"\r\n                      >\r\n                        <FileText className=\"h-2 w-2 mr-1\" />\r\n                        {source.split('.')[0].slice(0, 8)}...\r\n                      </Badge>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              <div className=\"flex items-center justify-between mt-1\">\r\n                <span className=\"text-xs text-white/60\">{message.timestamp}</span>\r\n                {message.type === 'assistant' && (\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Button variant=\"ghost\" size=\"icon\" className=\"h-4 w-4 p-0 text-white/60 hover:text-white\">\r\n                      <Copy className=\"h-2 w-2\" />\r\n                    </Button>\r\n                    <Button variant=\"ghost\" size=\"icon\" className=\"h-4 w-4 p-0 text-white/60 hover:text-white\">\r\n                      <ThumbsUp className=\"h-2 w-2\" />\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {message.type === 'user' && (\r\n              <Avatar className=\"w-6 h-6 mt-0.5\">\r\n                <AvatarFallback className=\"bg-blue-600 text-white text-xs\">\r\n                  U\r\n                </AvatarFallback>\r\n              </Avatar>\r\n            )}\r\n          </div>\r\n        ))}\r\n        \r\n        {isLoading && (\r\n          <div className=\"flex gap-2\">\r\n            <Avatar className=\"w-6 h-6 mt-0.5\">\r\n              <AvatarFallback className=\"bg-purple-600 text-white text-xs\">\r\n                AI\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"bg-slate-800/80 rounded-lg p-2 border border-white/10\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <div className=\"w-1 h-1 bg-white/60 rounded-full animate-bounce\"></div>\r\n                <div className=\"w-1 h-1 bg-white/60 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                <div className=\"w-1 h-1 bg-white/60 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                <span className=\"text-xs text-white/60 ml-2\">Thinking...</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {/* Usage Stats */}\r\n      <div className=\"p-3 border-t border-white/10\">\r\n        <div className=\"grid grid-cols-2 gap-3 text-xs\">\r\n          <div className=\"text-center\">\r\n            <p className=\"text-white/60\">Today</p>\r\n            <p className=\"text-white font-medium\">23 queries</p>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <p className=\"text-white/60\">Accuracy</p>\r\n            <p className=\"text-green-400 font-medium\">94%</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Input Area */}\r\n      <div className=\"p-3 border-t border-white/10\">\r\n        <div className=\"flex gap-2\">\r\n          <div className=\"flex-1 relative\">\r\n            <Input\r\n              value={inputValue}\r\n              onChange={(e) => setInputValue(e.target.value)}\r\n              onKeyDown={handleKeyPress}\r\n              placeholder=\"Ask anything...\"\r\n              className=\"text-xs h-8 bg-slate-800/50 border-white/20 text-white placeholder:text-white/50 pr-8\"\r\n            />\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"absolute right-1 top-1 h-6 w-6 text-white/60 hover:text-white\"\r\n            >\r\n              <Mic className=\"h-3 w-3\" />\r\n            </Button>\r\n          </div>\r\n          <Button\r\n            onClick={handleSendMessage}\r\n            disabled={!inputValue.trim() || isLoading}\r\n            size=\"icon\"\r\n            className=\"h-8 w-8 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600\"\r\n          >\r\n            <Send className=\"h-3 w-3\" />\r\n          </Button>\r\n        </div>\r\n        \r\n        <p className=\"text-xs text-white/60 mt-2 text-center\">\r\n          AI responses based on your materials\r\n        </p>\r\n      </div>\r\n    </div>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAgDO,SAAS,cAAc,KAAgC;QAAhC,EAAE,QAAQ,EAAsB,GAAhC;;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QACpF;KACD;IAED,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,eAA8B;QAClC;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,QAAQ;QACV;KACD;IAED,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QACpF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,aAAsB;gBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,mBAAmB;gBAC5B,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;oBAAE,MAAM;oBAAW,QAAQ;gBAAU;gBAClF,SAAS;oBAAC;oBAAqB;iBAAiB;YAClD;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,qBAAqB,CAAC;QAC1B,sCAAsC;QACtC,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY;YAC3C,OAAQ;QACV,OAAO,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc;YACpD,OAAQ;QACV,OAAO;YACL,OAAQ;QACV;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB;YACpB,SAAS;YACT,WAAW;YACX,UAAU;YACV,YAAY;QACd;QAEA,cAAc,aAAa,CAAC,OAAqC,IAAI;IACvE;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY;4CAAC,QAAQ,CAAC,EAAE;yCAAC;8CAExC,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB,OAAO,MAAM;;oCAE7C,OAAO,IAAI;kDACZ,6LAAC;wCAAK,WAAU;kDAAiB,OAAO,KAAK;;;;;;;+BAPxC,OAAO,EAAE;;;;;;;;;;;;;;;;0BActB,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4BAAqB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChC,cACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;;gCAEzC,QAAQ,IAAI,KAAK,6BAChB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wCAAC,WAAU;kDAAmC;;;;;;;;;;;8CAMjE,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,sCACA,QAAQ,IAAI,KAAK,SACb,4DACA;;sDAEJ,6LAAC;4CAAE,WAAU;sDAA2B,QAAQ,OAAO;;;;;;wCAGtD,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACxC,6LAAC,oIAAA,CAAA,QAAK;wDAEJ,SAAQ;wDACR,WAAU;;0EAEV,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG;4DAAG;;uDAL7B;;;;;;;;;;;;;;;sDAYf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyB,QAAQ,SAAS;;;;;;gDACzD,QAAQ,IAAI,KAAK,6BAChB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAO,WAAU;sEAC5C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAO,WAAU;sEAC5C,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAO7B,QAAQ,IAAI,KAAK,wBAChB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wCAAC,WAAU;kDAAiC;;;;;;;;;;;;2BAvDvD,QAAQ,EAAE;;;;;oBA+DrB,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;0CAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oCAAC,WAAU;8CAAmC;;;;;;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;sCAExC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAMhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW;wCACX,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGnB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI,MAAM;gCAChC,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIpB,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;AAM9D;GAnSgB;KAAA", "debugId": null}}, {"offset": {"line": 2457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 2611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 2712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/lib/forum-api.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport type { \n  ForumCategory, \n  ForumTopic, \n  ForumMessage,\n  CreateMessageInput,\n  CreateTopicInput,\n  ForumCategoryWithStats,\n  ForumTopicWithDetails\n} from '@/types/forum'\n\n// =========================================\n// FORUM CATEGORIES\n// =========================================\n\nexport async function getForumCategories(courseId: string): Promise<{ data: ForumCategoryWithStats[] | null; error: any }> {\n  try {\n    // Simplified query to avoid complex nested relationships\n    const { data, error } = await supabase\n      .from('forum_categories')\n      .select('*')\n      .eq('course_id', courseId)\n      .order('created_at', { ascending: true })\n\n    if (error) throw error\n\n    // Process the data to add basic stats\n    const categoriesWithStats = data?.map(category => ({\n      ...category,\n      message_count: 0, // Will be populated separately if needed\n      online_users: 0   // Will be populated separately if needed\n    })) || []\n\n    return { data: categoriesWithStats, error: null }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\n// =========================================\n// FORUM TOPICS\n// =========================================\n\nexport async function getForumTopics(categoryId: string): Promise<{ data: ForumTopic[] | null; error: any }> {\n  try {\n    const { data, error } = await supabase\n      .from('forum_topics')\n      .select(`\n        *,\n        profiles:user_id(name, username),\n        last_message_user:last_message_by(name, username)\n      `)\n      .eq('category_id', categoryId)\n      .order('is_pinned', { ascending: false })\n      .order('last_message_at', { ascending: false })\n\n    return { data, error }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\nexport async function getForumTopic(topicId: string): Promise<{ data: ForumTopicWithDetails | null; error: any }> {\n  try {\n    const { data, error } = await supabase\n      .from('forum_topics')\n      .select(`\n        *,\n        profiles:user_id(name, username),\n        forum_topic_participants(\n          user_id,\n          joined_at,\n          notifications_enabled,\n          profiles(name, username)\n        ),\n        forum_messages(\n          id,\n          content,\n          created_at,\n          user_id,\n          profiles(name, username, avatar_url)\n        )\n      `)\n      .eq('id', topicId)\n      .single()\n\n    return { data, error }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\nexport async function createForumTopic(input: CreateTopicInput): Promise<{ data: ForumTopic | null; error: any }> {\n  try {\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    // Create the topic\n    const { data: topic, error: topicError } = await supabase\n      .from('forum_topics')\n      .insert({\n        category_id: input.category_id,\n        user_id: user.id,\n        title: input.title,\n        description: input.description\n      })\n      .select()\n      .single()\n\n    if (topicError) throw topicError\n\n    // Create the initial message\n    const { error: messageError } = await supabase\n      .from('forum_messages')\n      .insert({\n        category_id: input.category_id,\n        topic_id: topic.id,\n        user_id: user.id,\n        content: input.initial_message,\n        message_type: 'text'\n      })\n\n    if (messageError) throw messageError\n\n    return { data: topic, error: null }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\n// =========================================\n// FORUM MESSAGES\n// =========================================\n\nexport async function getForumMessages(\n  categoryId: string, \n  topicId?: string, \n  limit = 50,\n  offset = 0\n): Promise<{ data: ForumMessage[] | null; error: any }> {\n  try {\n    let query = supabase\n      .from('forum_messages')\n      .select(`\n        *,\n        profiles:user_id(name, username, avatar_url),\n        parent_message:parent_message_id(\n          id,\n          content,\n          profiles:user_id(name, username)\n        )\n      `)\n      .eq('category_id', categoryId)\n\n    if (topicId) {\n      query = query.eq('topic_id', topicId)\n    } else {\n      query = query.is('topic_id', null)\n    }\n\n    const { data, error } = await query\n      .order('created_at', { ascending: true })\n      .range(offset, offset + limit - 1)\n\n    return { data, error }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\nexport async function createForumMessage(input: CreateMessageInput): Promise<{ data: ForumMessage | null; error: any }> {\n  try {\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    let imageUrl = null\n\n    // Handle image upload if provided\n    if (input.image_file) {\n      const fileExt = input.image_file.name.split('.').pop()\n      const fileName = `${user.id}/${Date.now()}.${fileExt}`\n      \n      const { data: uploadData, error: uploadError } = await supabase.storage\n        .from('forum-images')\n        .upload(fileName, input.image_file)\n\n      if (uploadError) throw uploadError\n\n      const { data: { publicUrl } } = supabase.storage\n        .from('forum-images')\n        .getPublicUrl(uploadData.path)\n\n      imageUrl = publicUrl\n    }\n\n    // Check daily limit for announcements\n    if (input.category_id) {\n      const { data: category } = await supabase\n        .from('forum_categories')\n        .select('settings')\n        .eq('id', input.category_id)\n        .single()\n\n      if (category?.settings?.daily_limit) {\n        const today = new Date().toISOString().split('T')[0]\n        \n        const { data: dailyLimit } = await supabase\n          .from('forum_daily_limits')\n          .select('message_count')\n          .eq('user_id', user.id)\n          .eq('category_id', input.category_id)\n          .eq('post_date', today)\n          .single()\n\n        if (dailyLimit && dailyLimit.message_count >= category.settings.daily_limit) {\n          throw new Error('Daily posting limit reached for this category')\n        }\n      }\n    }\n\n    // Use the directly passed image URL if available\n    const finalImageUrl = imageUrl || input.image_url || null\n\n    // Determine message type\n    const messageType = finalImageUrl ? 'image' : 'text'\n\n    // Create the message\n    const { data, error } = await supabase\n      .from('forum_messages')\n      .insert({\n        category_id: input.category_id,\n        topic_id: input.topic_id || null,\n        user_id: user.id,\n        parent_message_id: input.parent_message_id || null,\n        content: input.content,\n        message_type: messageType,\n        referenced_files: input.referenced_files || [],\n        image_url: finalImageUrl\n      })\n      .select(`\n        *,\n        profiles:user_id(name, username, avatar_url)\n      `)\n      .single()\n\n    return { data, error }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\nexport async function updateForumMessage(messageId: string, content: string): Promise<{ data: ForumMessage | null; error: any }> {\n  try {\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    // Get the current message to check for images that might be removed\n    const { data: currentMessage, error: fetchError } = await supabase\n      .from('forum_messages')\n      .select('content, image_url, user_id')\n      .eq('id', messageId)\n      .single()\n\n    if (fetchError) throw fetchError\n\n    // Check if user owns the message\n    if (currentMessage.user_id !== user.id) {\n      throw new Error('You can only edit your own messages')\n    }\n\n    // If the message had an image but the new content doesn't contain image references,\n    // we should delete the image from storage\n    if (currentMessage.image_url && !content.includes('<img')) {\n      try {\n        // Extract file path from Supabase Storage URL\n        const url = new URL(currentMessage.image_url)\n        const pathParts = url.pathname.split('/')\n        const bucketIndex = pathParts.indexOf('forum-images')\n        \n        if (bucketIndex !== -1 && bucketIndex + 1 < pathParts.length) {\n          const filePath = pathParts.slice(bucketIndex + 1).join('/')\n          console.log('Deleting removed image from edit:', filePath)\n          \n          const { error: storageError } = await supabase.storage\n            .from('forum-images')\n            .remove([filePath])\n\n          if (storageError) {\n            console.error('Error deleting image from storage during edit:', storageError)\n          } else {\n            console.log('Successfully deleted removed image from storage')\n          }\n        }\n      } catch (error) {\n        console.error('Error deleting image during edit:', error)\n      }\n    }\n\n    // Update the message with edited flag\n    const updateData: any = { \n      content,\n      is_edited: true,\n      edited_at: new Date().toISOString()\n    }\n\n    // If image was removed, clear the image_url field\n    if (currentMessage.image_url && !content.includes('<img')) {\n      updateData.image_url = null\n      updateData.message_type = 'text'\n    }\n\n    const { data, error } = await supabase\n      .from('forum_messages')\n      .update(updateData)\n      .eq('id', messageId)\n      .eq('user_id', user.id) // Ensure user can only edit their own messages\n      .select(`\n        *,\n        profiles:user_id(name, username, avatar_url)\n      `)\n      .single()\n\n    return { data, error }\n  } catch (error) {\n    return { data: null, error }\n  }\n}\n\nexport async function deleteForumMessage(messageId: string): Promise<{ error: any }> {\n  try {\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    // First get the message to check if it has an image\n    const { data: message, error: fetchError } = await supabase\n      .from('forum_messages')\n      .select('image_url, user_id')\n      .eq('id', messageId)\n      .single()\n\n    if (fetchError) throw fetchError\n\n    // Check if user owns the message\n    if (message.user_id !== user.id) {\n      throw new Error('You can only delete your own messages')\n    }\n\n    // Delete image from storage if it exists\n    if (message.image_url) {\n      try {\n        // Extract file path from Supabase Storage URL\n        // URL format: https://[project].supabase.co/storage/v1/object/public/forum-images/[user_id]/[filename]\n        const url = new URL(message.image_url)\n        const pathParts = url.pathname.split('/')\n        const bucketIndex = pathParts.indexOf('forum-images')\n        \n        if (bucketIndex !== -1 && bucketIndex + 1 < pathParts.length) {\n          // Get the path after 'forum-images/'\n          const filePath = pathParts.slice(bucketIndex + 1).join('/')\n          console.log('Attempting to delete file:', filePath)\n          \n          const { error: storageError } = await supabase.storage\n            .from('forum-images')\n            .remove([filePath])\n\n          if (storageError) {\n            console.error('Error deleting image from storage:', storageError)\n          } else {\n            console.log('Successfully deleted image from storage:', filePath)\n          }\n        } else {\n          console.error('Could not extract file path from URL:', message.image_url)\n        }\n      } catch (error) {\n        console.error('Error parsing image URL:', error)\n      }\n    }\n\n    // Delete the message\n    const { error } = await supabase\n      .from('forum_messages')\n      .delete()\n      .eq('id', messageId)\n      .eq('user_id', user.id) // Double-check user ownership\n\n    return { error }\n  } catch (error) {\n    return { error }\n  }\n}\n\n\n\n// =========================================\n// REACTIONS\n// =========================================\n\nexport async function addReaction(messageId: string, emoji: string): Promise<{ error: any }> {\n  try {\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    // Get current reactions\n    const { data: message, error: fetchError } = await supabase\n      .from('forum_messages')\n      .select('reactions')\n      .eq('id', messageId)\n      .single()\n\n    if (fetchError) throw fetchError\n\n    const reactions = message.reactions || {}\n    \n    // Add user to emoji reaction\n    if (!reactions[emoji]) {\n      reactions[emoji] = []\n    }\n    \n    if (!reactions[emoji].includes(user.id)) {\n      reactions[emoji].push(user.id)\n    }\n\n    // Update the message\n    const { error } = await supabase\n      .from('forum_messages')\n      .update({ reactions })\n      .eq('id', messageId)\n\n    return { error }\n  } catch (error) {\n    return { error }\n  }\n}\n\nexport async function removeReaction(messageId: string, emoji: string): Promise<{ error: any }> {\n  try {\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    // Get current reactions\n    const { data: message, error: fetchError } = await supabase\n      .from('forum_messages')\n      .select('reactions')\n      .eq('id', messageId)\n      .single()\n\n    if (fetchError) throw fetchError\n\n    const reactions = message.reactions || {}\n    \n    // Remove user from emoji reaction\n    if (reactions[emoji]) {\n      reactions[emoji] = reactions[emoji].filter((userId: string) => userId !== user.id)\n      \n      // Remove emoji if no users\n      if (reactions[emoji].length === 0) {\n        delete reactions[emoji]\n      }\n    }\n\n    // Update the message\n    const { error } = await supabase\n      .from('forum_messages')\n      .update({ reactions })\n      .eq('id', messageId)\n\n    return { error }\n  } catch (error) {\n    return { error }\n  }\n}\n\n// =========================================\n// REAL-TIME SUBSCRIPTIONS\n// =========================================\n\nexport function subscribeToForumMessages(\n  categoryId: string,\n  topicId: string | null,\n  callback: (payload: any) => void\n) {\n  const filter = topicId \n    ? `category_id=eq.${categoryId} AND topic_id=eq.${topicId}`\n    : `category_id=eq.${categoryId} AND topic_id=is.null`\n\n  return supabase\n    .channel(`forum:${categoryId}:${topicId || 'general'}`)\n    .on(\n      'postgres_changes',\n      {\n        event: '*',\n        schema: 'public',\n        table: 'forum_messages',\n        filter\n      },\n      callback\n    )\n    .subscribe()\n}\n\n "], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAeO,eAAe,mBAAmB,QAAgB;IACvD,IAAI;QACF,yDAAyD;QACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,OAAO,MAAM;QAEjB,sCAAsC;QACtC,MAAM,sBAAsB,CAAA,iBAAA,2BAAA,KAAM,GAAG,CAAC,CAAA,WAAY,CAAC;gBACjD,GAAG,QAAQ;gBACX,eAAe;gBACf,cAAc,EAAI,yCAAyC;YAC7D,CAAC,OAAM,EAAE;QAET,OAAO;YAAE,MAAM;YAAqB,OAAO;QAAK;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAMO,eAAe,eAAe,UAAkB;IACrD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,gBACL,MAAM,CAAE,8HAKR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC,mBAAmB;YAAE,WAAW;QAAM;QAE/C,OAAO;YAAE;YAAM;QAAM;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAEO,eAAe,cAAc,OAAe;IACjD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,gBACL,MAAM,CAAE,oYAiBR,EAAE,CAAC,MAAM,SACT,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAEO,eAAe,iBAAiB,KAAuB;IAC5D,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,mBAAmB;QACnB,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtD,IAAI,CAAC,gBACL,MAAM,CAAC;YACN,aAAa,MAAM,WAAW;YAC9B,SAAS,KAAK,EAAE;YAChB,OAAO,MAAM,KAAK;YAClB,aAAa,MAAM,WAAW;QAChC,GACC,MAAM,GACN,MAAM;QAET,IAAI,YAAY,MAAM;QAEtB,6BAA6B;QAC7B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,kBACL,MAAM,CAAC;YACN,aAAa,MAAM,WAAW;YAC9B,UAAU,MAAM,EAAE;YAClB,SAAS,KAAK,EAAE;YAChB,SAAS,MAAM,eAAe;YAC9B,cAAc;QAChB;QAEF,IAAI,cAAc,MAAM;QAExB,OAAO;YAAE,MAAM;YAAO,OAAO;QAAK;IACpC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAMO,eAAe,iBACpB,UAAkB,EAClB,OAAgB;QAChB,QAAA,iEAAQ,IACR,SAAA,iEAAS;IAET,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,kBACL,MAAM,CAAE,oNASR,EAAE,CAAC,eAAe;QAErB,IAAI,SAAS;YACX,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B,OAAO;YACL,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAC3B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK,GACtC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,OAAO;YAAE;YAAM;QAAM;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAEO,eAAe,mBAAmB,KAAyB;IAChE,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,IAAI,WAAW;QAEf,kCAAkC;QAClC,IAAI,MAAM,UAAU,EAAE;YACpB,MAAM,UAAU,MAAM,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACpD,MAAM,WAAW,AAAC,GAAa,OAAX,KAAK,EAAE,EAAC,KAAiB,OAAd,KAAK,GAAG,IAAG,KAAW,OAAR;YAE7C,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,OAAO,CACpE,IAAI,CAAC,gBACL,MAAM,CAAC,UAAU,MAAM,UAAU;YAEpC,IAAI,aAAa,MAAM;YAEvB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAC7C,IAAI,CAAC,gBACL,YAAY,CAAC,WAAW,IAAI;YAE/B,WAAW;QACb;QAEA,sCAAsC;QACtC,IAAI,MAAM,WAAW,EAAE;gBAOjB;YANJ,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,MAAM,WAAW,EAC1B,MAAM;YAET,IAAI,qBAAA,gCAAA,qBAAA,SAAU,QAAQ,cAAlB,yCAAA,mBAAoB,WAAW,EAAE;gBACnC,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAEpD,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC,iBACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,eAAe,MAAM,WAAW,EACnC,EAAE,CAAC,aAAa,OAChB,MAAM;gBAET,IAAI,cAAc,WAAW,aAAa,IAAI,SAAS,QAAQ,CAAC,WAAW,EAAE;oBAC3E,MAAM,IAAI,MAAM;gBAClB;YACF;QACF;QAEA,iDAAiD;QACjD,MAAM,gBAAgB,YAAY,MAAM,SAAS,IAAI;QAErD,yBAAyB;QACzB,MAAM,cAAc,gBAAgB,UAAU;QAE9C,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC;YACN,aAAa,MAAM,WAAW;YAC9B,UAAU,MAAM,QAAQ,IAAI;YAC5B,SAAS,KAAK,EAAE;YAChB,mBAAmB,MAAM,iBAAiB,IAAI;YAC9C,SAAS,MAAM,OAAO;YACtB,cAAc;YACd,kBAAkB,MAAM,gBAAgB,IAAI,EAAE;YAC9C,WAAW;QACb,GACC,MAAM,CAAE,8EAIR,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAEO,eAAe,mBAAmB,SAAiB,EAAE,OAAe;IACzE,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,oEAAoE;QACpE,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC/D,IAAI,CAAC,kBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,YAAY,MAAM;QAEtB,iCAAiC;QACjC,IAAI,eAAe,OAAO,KAAK,KAAK,EAAE,EAAE;YACtC,MAAM,IAAI,MAAM;QAClB;QAEA,oFAAoF;QACpF,0CAA0C;QAC1C,IAAI,eAAe,SAAS,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;YACzD,IAAI;gBACF,8CAA8C;gBAC9C,MAAM,MAAM,IAAI,IAAI,eAAe,SAAS;gBAC5C,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC;gBACrC,MAAM,cAAc,UAAU,OAAO,CAAC;gBAEtC,IAAI,gBAAgB,CAAC,KAAK,cAAc,IAAI,UAAU,MAAM,EAAE;oBAC5D,MAAM,WAAW,UAAU,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;oBACvD,QAAQ,GAAG,CAAC,qCAAqC;oBAEjD,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,OAAO,CACnD,IAAI,CAAC,gBACL,MAAM,CAAC;wBAAC;qBAAS;oBAEpB,IAAI,cAAc;wBAChB,QAAQ,KAAK,CAAC,kDAAkD;oBAClE,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;QAEA,sCAAsC;QACtC,MAAM,aAAkB;YACtB;YACA,WAAW;YACX,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,kDAAkD;QAClD,IAAI,eAAe,SAAS,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;YACzD,WAAW,SAAS,GAAG;YACvB,WAAW,YAAY,GAAG;QAC5B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EAAE,+CAA+C;SACtE,MAAM,CAAE,8EAIR,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAEO,eAAe,mBAAmB,SAAiB;IACxD,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,oDAAoD;QACpD,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,kBACL,MAAM,CAAC,sBACP,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,YAAY,MAAM;QAEtB,iCAAiC;QACjC,IAAI,QAAQ,OAAO,KAAK,KAAK,EAAE,EAAE;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,IAAI,QAAQ,SAAS,EAAE;YACrB,IAAI;gBACF,8CAA8C;gBAC9C,uGAAuG;gBACvG,MAAM,MAAM,IAAI,IAAI,QAAQ,SAAS;gBACrC,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC;gBACrC,MAAM,cAAc,UAAU,OAAO,CAAC;gBAEtC,IAAI,gBAAgB,CAAC,KAAK,cAAc,IAAI,UAAU,MAAM,EAAE;oBAC5D,qCAAqC;oBACrC,MAAM,WAAW,UAAU,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;oBACvD,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,OAAO,CACnD,IAAI,CAAC,gBACL,MAAM,CAAC;wBAAC;qBAAS;oBAEpB,IAAI,cAAc;wBAChB,QAAQ,KAAK,CAAC,sCAAsC;oBACtD,OAAO;wBACL,QAAQ,GAAG,CAAC,4CAA4C;oBAC1D;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,yCAAyC,QAAQ,SAAS;gBAC1E;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;QAEA,qBAAqB;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EAAE,8BAA8B;;QAExD,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE;QAAM;IACjB;AACF;AAQO,eAAe,YAAY,SAAiB,EAAE,KAAa;IAChE,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,wBAAwB;QACxB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,kBACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,YAAY,MAAM;QAEtB,MAAM,YAAY,QAAQ,SAAS,IAAI,CAAC;QAExC,6BAA6B;QAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACrB,SAAS,CAAC,MAAM,GAAG,EAAE;QACvB;QAEA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG;YACvC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;QAC/B;QAEA,qBAAqB;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,CAAC;YAAE;QAAU,GACnB,EAAE,CAAC,MAAM;QAEZ,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE;QAAM;IACjB;AACF;AAEO,eAAe,eAAe,SAAiB,EAAE,KAAa;IACnE,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,wBAAwB;QACxB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,kBACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,YAAY,MAAM;QAEtB,MAAM,YAAY,QAAQ,SAAS,IAAI,CAAC;QAExC,kCAAkC;QAClC,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAmB,WAAW,KAAK,EAAE;YAEjF,2BAA2B;YAC3B,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;gBACjC,OAAO,SAAS,CAAC,MAAM;YACzB;QACF;QAEA,qBAAqB;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,CAAC;YAAE;QAAU,GACnB,EAAE,CAAC,MAAM;QAEZ,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE;QAAM;IACjB;AACF;AAMO,SAAS,yBACd,UAAkB,EAClB,OAAsB,EACtB,QAAgC;IAEhC,MAAM,SAAS,UACX,AAAC,kBAA+C,OAA9B,YAAW,qBAA2B,OAAR,WAChD,AAAC,kBAA4B,OAAX,YAAW;IAEjC,OAAO,yHAAA,CAAA,WAAQ,CACZ,OAAO,CAAC,AAAC,SAAsB,OAAd,YAAW,KAAwB,OAArB,WAAW,YAC1C,EAAE,CACD,oBACA;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP;IACF,GACA,UAED,SAAS;AACd", "debugId": null}}, {"offset": {"line": 3074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/course/forum-interface.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\"\nimport { cn } from \"@/lib/utils\"\nimport { \n  Send, \n  Search, \n  Plus, \n  Image as ImageIcon,\n  FileText,\n  Pin,\n  Lock,\n  CheckCircle,\n  Eye,\n  EyeOff,\n  Bold,\n  Italic,\n  Underline,\n  Smile,\n  List,\n  ListOrdered,\n  Quote,\n  Type,\n  ChevronDown,\n  Edit2,\n  Trash2,\n  Heart,\n  MoreHorizontal\n} from \"lucide-react\"\nimport type { \n  ForumCategory, \n  ForumTopic, \n  ForumMessage,\n  CreateMessageInput,\n  CreateTopicInput \n} from \"@/types/forum\"\nimport {\n  getForumCategories,\n  getForumTopics,\n  getForumMessages,\n  createForumMessage,\n  createForumTopic,\n  updateForumMessage,\n  deleteForumMessage,\n  addReaction,\n  removeReaction\n} from \"@/lib/forum-api\"\nimport { supabase } from \"@/lib/supabase\"\n\ninterface ForumInterfaceProps {\n  courseId: string\n  user: any\n}\n\nexport function ForumInterface({ courseId, user }: ForumInterfaceProps) {\n  // State Management\n  const [categories, setCategories] = useState<ForumCategory[]>([])\n  const [selectedCategory, setSelectedCategory] = useState<ForumCategory | null>(null)\n  const [topics, setTopics] = useState<ForumTopic[]>([])\n  const [selectedTopic, setSelectedTopic] = useState<ForumTopic | null>(null)\n  const [messages, setMessages] = useState<ForumMessage[]>([])\n\n  \n  // UI State\n  const [messageContent, setMessageContent] = useState(\"\")\n  const [isTopicsVisible, setIsTopicsVisible] = useState(true)\n  const [isCreatingTopic, setIsCreatingTopic] = useState(false)\n  const [newTopicTitle, setNewTopicTitle] = useState(\"\")\n  const [newTopicDescription, setNewTopicDescription] = useState(\"\")\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [showFormatting, setShowFormatting] = useState(false)\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false)\n  const [showFileDialog, setShowFileDialog] = useState(false)\n  const [pendingImages, setPendingImages] = useState<{file: File, localUrl: string}[]>([])\n  const [isSending, setIsSending] = useState(false)\n  \n  // Message Actions State\n  const [editingMessageId, setEditingMessageId] = useState<string | null>(null)\n  const [editingContent, setEditingContent] = useState(\"\")\n  const [hoveredMessageId, setHoveredMessageId] = useState<string | null>(null)\n  const [showReactionPicker, setShowReactionPicker] = useState<string | null>(null)\n  \n  // Refs\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const editorRef = useRef<HTMLDivElement>(null)\n  const editEditorRef = useRef<HTMLDivElement>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  // Data Loading Functions\n  const loadCategories = async () => {\n    if (!courseId) return\n    const { data, error } = await getForumCategories(courseId)\n    if (data && !error) {\n      setCategories(data)\n    }\n  }\n\n  const loadTopics = async (categoryId: string) => {\n    const { data, error } = await getForumTopics(categoryId)\n    if (data && !error) {\n      setTopics(data)\n    }\n  }\n\n  const loadMessages = async (categoryId: string, topicId?: string) => {\n    const { data, error } = await getForumMessages(categoryId, topicId)\n    if (data && !error) {\n      setMessages(data)\n    }\n  }\n\n\n\n  // Load forum categories on mount\n  useEffect(() => {\n    if (courseId) {\n      loadCategories()\n    }\n  }, [courseId])\n\n  // Auto-select first category\n  useEffect(() => {\n    if (categories.length > 0 && !selectedCategory) {\n      setSelectedCategory(categories[0])\n    }\n  }, [categories, selectedCategory])\n\n  // Load topics when category changes\n  useEffect(() => {\n    if (selectedCategory) {\n      if (selectedCategory.type === 'forum') {\n        loadTopics(selectedCategory.id)\n      } else {\n        setTopics([])\n        setSelectedTopic(null)\n        loadMessages(selectedCategory.id)\n      }\n    }\n  }, [selectedCategory])\n\n  // Load messages when topic changes\n  useEffect(() => {\n    if (selectedTopic) {\n      loadMessages(selectedCategory!.id, selectedTopic.id)\n    }\n  }, [selectedTopic])\n\n  // Smart scroll to bottom - only within the messages container\n  const [shouldAutoScroll, setShouldAutoScroll] = useState(false)\n  const [isNearBottom, setIsNearBottom] = useState(true)\n  const [isFirstLoad, setIsFirstLoad] = useState(true)\n  const messagesContainerRef = useRef<HTMLDivElement>(null)\n\n  // Check if user is near bottom of messages\n  const checkScrollPosition = () => {\n    if (messagesContainerRef.current) {\n      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current\n      const isNear = scrollHeight - scrollTop - clientHeight < 100 // Within 100px of bottom\n      setIsNearBottom(isNear)\n    }\n  }\n\n  // Scroll to bottom within the messages container only\n  const scrollToBottomInContainer = () => {\n    if (messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight\n    }\n  }\n\n  // Auto-scroll logic for messages\n  useEffect(() => {\n    if (messages.length > 0) {\n      if (isFirstLoad || shouldAutoScroll || isNearBottom) {\n        // Small delay to ensure DOM is updated\n        setTimeout(() => {\n          scrollToBottomInContainer()\n          if (isFirstLoad) {\n            setIsFirstLoad(false)\n          }\n          if (shouldAutoScroll) {\n            setShouldAutoScroll(false)\n          }\n        }, 100)\n      }\n    }\n  }, [messages, shouldAutoScroll, isNearBottom, isFirstLoad])\n\n  // Reset first load when switching categories/topics\n  useEffect(() => {\n    setIsFirstLoad(true)\n  }, [selectedCategory, selectedTopic])\n\n  // Set cursor to end of content\n  const setCursorToEnd = (element: HTMLElement) => {\n    const range = document.createRange()\n    const selection = window.getSelection()\n    \n    // Set range to end of element\n    range.selectNodeContents(element)\n    range.collapse(false)\n    \n    // Clear existing selection and add new range\n    selection?.removeAllRanges()\n    selection?.addRange(range)\n  }\n\n  // Set initial content when editing starts (only once!)\n  useEffect(() => {\n    if (editingMessageId && editEditorRef.current) {\n      // Set content only once when editing starts\n      editEditorRef.current.innerHTML = editingContent\n      \n      // Focus the editor and set cursor to end\n      setTimeout(() => {\n        if (editEditorRef.current) {\n          editEditorRef.current.focus()\n          setCursorToEnd(editEditorRef.current)\n        }\n      }, 50) // Slightly longer delay\n    }\n  }, [editingMessageId]) // Only depend on editingMessageId, NOT editingContent!\n\n  // Auto-resize editor when content changes\n  useEffect(() => {\n    autoResizeEditor()\n  }, [messageContent])\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element\n      if (!target.closest('.emoji-picker') && !target.closest('.file-dialog')) {\n        setShowEmojiPicker(false)\n        setShowFileDialog(false)\n      }\n      if (!target.closest('.reaction-picker')) {\n        setShowReactionPicker(null)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  // Clean up on unmount\n  useEffect(() => {\n    return () => {\n      cleanupPendingImages()\n    }\n  }, [])\n\n  // Clean up pending images\n  const cleanupPendingImages = () => {\n    console.log('Cleaning up pending images:', pendingImages.length)\n    pendingImages.forEach(({ localUrl }) => {\n      console.log('Revoking blob URL:', localUrl)\n      URL.revokeObjectURL(localUrl)\n    })\n    setPendingImages([])\n  }\n\n  // Helper function to extract text content from HTML, replacing images with placeholder text\n  const extractEditableContent = (htmlContent: string) => {\n    const tempDiv = document.createElement('div')\n    tempDiv.innerHTML = htmlContent\n    \n    // Replace images with placeholder text\n    const images = tempDiv.querySelectorAll('img')\n    images.forEach((img, index) => {\n      const placeholder = document.createTextNode(`[Image ${index + 1}]`)\n      img.parentNode?.replaceChild(placeholder, img)\n    })\n    \n    // Extract clean text content\n    return tempDiv.textContent || tempDiv.innerText || ''\n  }\n\n  // Message Action Functions\n  const handleEditMessage = (message: ForumMessage) => {\n    setEditingMessageId(message.id)\n    // Reconstruct the HTML content with the image embedded for editing\n    let editableContent = message.content\n    \n    // If there's an image_url, make sure it's embedded in the content for editing\n    if (message.image_url && !message.content.includes('<img')) {\n      editableContent = message.content + `<img src=\"${message.image_url}\" style=\"max-width:300px; border-radius:8px; margin:8px 0; display:block;\">`\n    }\n    \n    setEditingContent(editableContent)\n  }\n\n  const handleSaveEdit = async (messageId: string) => {\n    if (!editingContent.trim()) return\n\n    try {\n      // Get the HTML content from the edit editor\n      let htmlContent = editEditorRef.current?.innerHTML || editingContent\n\n      // Remove any image tags from content since we store images separately\n      const cleanContent = htmlContent.replace(/<img[^>]*>/g, '').trim()\n\n      if (!cleanContent) return\n\n      const { data, error } = await updateForumMessage(messageId, cleanContent)\n      if (data && !error) {\n        setEditingMessageId(null)\n        setEditingContent(\"\")\n        // Clear the edit editor content\n        if (editEditorRef.current) {\n          editEditorRef.current.innerHTML = \"\"\n        }\n        loadMessages(selectedCategory!.id, selectedTopic?.id)\n      } else {\n        console.error('Failed to update message:', error)\n      }\n    } catch (error) {\n      console.error('Error updating message:', error)\n    }\n  }\n\n  const handleCancelEdit = () => {\n    setEditingMessageId(null)\n    setEditingContent(\"\")\n    // Clear the edit editor content\n    if (editEditorRef.current) {\n      editEditorRef.current.innerHTML = \"\"\n    }\n  }\n\n  const handleDeleteMessage = async (messageId: string) => {\n    if (!confirm('Are you sure you want to delete this message?')) return\n\n    try {\n      const { error } = await deleteForumMessage(messageId)\n      if (!error) {\n        loadMessages(selectedCategory!.id, selectedTopic?.id)\n      } else {\n        console.error('Failed to delete message:', error)\n      }\n    } catch (error) {\n      console.error('Error deleting message:', error)\n    }\n  }\n\n  const handleQuickReaction = async (messageId: string, emoji: string) => {\n    try {\n      const message = messages.find(m => m.id === messageId)\n      if (!message) return\n\n      // Check if user already reacted with this specific emoji\n      const userReacted = message.reactions[emoji]?.includes(user.id)\n      \n      if (userReacted) {\n        // Remove this reaction\n        await removeReaction(messageId, emoji)\n      } else {\n        // Remove any existing reaction from this user first (one reaction per user)\n        for (const [existingEmoji, users] of Object.entries(message.reactions)) {\n          if (users.includes(user.id)) {\n            await removeReaction(messageId, existingEmoji)\n            break\n          }\n        }\n        \n        // Add the new reaction\n        await addReaction(messageId, emoji)\n      }\n      \n      loadMessages(selectedCategory!.id, selectedTopic?.id)\n      setShowReactionPicker(null)\n    } catch (error) {\n      console.error('Error adding reaction:', error)\n    }\n  }\n\n  // Message Handling\n  const handleSendMessage = async () => {\n    if (!messageContent.trim() || !selectedCategory || isSending) return\n\n    setIsSending(true)\n\n    try {\n      // Get HTML content for rich formatting\n      let htmlContent = editorRef.current?.innerHTML || messageContent\n      let uploadedImageUrl: string | null = null\n\n      // Upload pending images and get URLs, but remove images from content\n      if (pendingImages.length > 0) {\n        for (const { file, localUrl } of pendingImages) {\n          try {\n            const supabaseUrl = await uploadImageToSupabase(file)\n            if (supabaseUrl) {\n              // Save the first uploaded image URL\n              if (!uploadedImageUrl) {\n                uploadedImageUrl = supabaseUrl\n              }\n            }\n            // Clean up local URL\n            URL.revokeObjectURL(localUrl)\n          } catch (error) {\n            console.error('Failed to upload image:', error)\n          }\n        }\n        \n        // Remove all image tags from content since we store images separately\n        htmlContent = htmlContent.replace(/<img[^>]*>/g, '')\n        \n        // Clear pending images\n        setPendingImages([])\n      }\n\n      const input: CreateMessageInput = {\n        content: htmlContent, // Send HTML content with Supabase URLs\n        category_id: selectedCategory.id,\n        topic_id: selectedTopic?.id,\n        image_url: uploadedImageUrl // Pass the image URL directly\n      }\n\n      const { data, error } = await createForumMessage(input)\n      if (data && !error) {\n        setMessageContent(\"\")\n        if (editorRef.current) {\n          editorRef.current.innerHTML = \"\"\n        }\n        autoResizeEditor()\n        setShouldAutoScroll(true) // Enable auto-scroll for user's own messages\n        loadMessages(selectedCategory.id, selectedTopic?.id)\n      } else {\n        // If sending failed, clean up any uploaded images from this attempt\n        console.error('Failed to send message:', error)\n      }\n    } finally {\n      setIsSending(false)\n    }\n  }\n\n  // Auto-resize editor\n  const autoResizeEditor = () => {\n    if (editorRef.current) {\n      editorRef.current.style.height = 'auto'\n      \n      // Check if there are images in the content\n      const hasImages = editorRef.current.querySelector('img')\n      const maxHeight = hasImages ? 200 : 120 // Increase max height when images are present\n      \n      const newHeight = Math.min(editorRef.current.scrollHeight, maxHeight)\n      editorRef.current.style.height = `${Math.max(newHeight, 44)}px` // Ensure minimum height\n    }\n  }\n\n  // Rich text formatting functions\n  const applyFormatting = (command: string, value?: string) => {\n    document.execCommand(command, false, value)\n    if (editorRef.current) {\n      editorRef.current.focus()\n      updateContent()\n    }\n  }\n\n  // Update content from contentEditable\n  const updateContent = () => {\n    if (editorRef.current) {\n      const htmlContent = editorRef.current.innerHTML\n      // Convert HTML back to text for storage/sending\n      const textContent = editorRef.current.innerText || editorRef.current.textContent || ''\n      \n      // Check if there are images even if text is empty\n      const hasImages = editorRef.current.querySelector('img')\n      const hasContent = textContent.trim() || hasImages\n      \n      setMessageContent(hasContent ? (textContent || ' ') : '')\n    }\n  }\n\n  // No need for updateEditContent - we'll get content directly when saving\n\n  // Insert custom formatting (for markdown-style)\n  const insertCustomFormatting = (before: string, after: string = before) => {\n    if (!editorRef.current) return\n    \n    const selection = window.getSelection()\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0)\n      const selectedText = range.toString()\n      \n      const wrapper = document.createElement('span')\n      wrapper.innerHTML = before + selectedText + after\n      \n      range.deleteContents()\n      range.insertNode(wrapper)\n      \n      // Move cursor to end\n      selection.removeAllRanges()\n      const newRange = document.createRange()\n      newRange.setStartAfter(wrapper)\n      newRange.collapse(true)\n      selection.addRange(newRange)\n      \n      updateContent()\n    }\n  }\n\n  // Emoji data\n  const emojis = [\n    '😀', '😃', '😄', '😁', '😅', '😂', '🤣', '😊', '😇', '🙂',\n    '😉', '😌', '😍', '🥰', '😘', '😗', '😋', '😎', '🤔', '🤨',\n    '😐', '😑', '😶', '🙄', '😏', '😣', '😥', '😮', '🤐', '😯',\n    '😴', '😔', '😕', '🙃', '🤑', '😲', '☹️', '🙁', '😖', '😞',\n    '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '👏', '🙌', '👐',\n    '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻',\n    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',\n    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',\n    '🔥', '⭐', '🌟', '✨', '⚡', '☄️', '💫', '🌈', '☀️', '⛅',\n    '🎉', '🎊', '🎈', '🎁', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️'\n  ]\n\n  // Insert emoji into editor\n  const insertEmoji = (emoji: string) => {\n    if (editorRef.current) {\n      const selection = window.getSelection()\n      if (selection && selection.rangeCount > 0) {\n        const range = selection.getRangeAt(0)\n        const textNode = document.createTextNode(emoji)\n        range.insertNode(textNode)\n        \n        // Move cursor after emoji\n        range.setStartAfter(textNode)\n        range.collapse(true)\n        selection.removeAllRanges()\n        selection.addRange(range)\n        \n        updateContent()\n        editorRef.current.focus()\n      }\n    }\n    setShowEmojiPicker(false)\n  }\n\n  // Upload image to Supabase Storage\n  const uploadImageToSupabase = async (file: File): Promise<string | null> => {\n    try {\n      // Check if user is authenticated\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) {\n        console.error('User not authenticated')\n        return null\n      }\n\n      // Generate unique filename with user ID for organization\n      const fileExt = file.name.split('.').pop()\n      const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`\n      \n      // Upload to Supabase Storage\n      const { data, error } = await supabase.storage\n        .from('forum-images')\n        .upload(fileName, file, {\n          cacheControl: '3600',\n          upsert: false\n        })\n\n      if (error) {\n        console.error('Error uploading image:', error)\n        return null\n      }\n\n      // Get public URL\n      const { data: { publicUrl } } = supabase.storage\n        .from('forum-images')\n        .getPublicUrl(fileName)\n\n      return publicUrl\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      return null\n    }\n  }\n\n  // Handle image upload (show preview only, upload on send)\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file && file.type.startsWith('image/')) {\n      console.log('Creating blob URL for file:', file.name, file.size)\n      \n      // Create local preview URL\n      const localUrl = URL.createObjectURL(file)\n      console.log('Created blob URL:', localUrl)\n      \n      // Add to pending images\n      setPendingImages(prev => {\n        const newImages = [...prev, { file, localUrl }]\n        console.log('Pending images updated:', newImages.length)\n        return newImages\n      })\n      \n      // Show local preview in editor\n      if (editorRef.current) {\n        const img = document.createElement('img')\n        img.src = localUrl\n        img.style.maxWidth = '300px'\n        img.style.borderRadius = '8px'\n        img.style.margin = '8px 0'\n        img.style.display = 'block'\n        img.setAttribute('data-pending-image', localUrl) // Mark as pending\n        \n        // Error handling for image load\n        img.onerror = () => {\n          console.error('Failed to load image preview:', localUrl)\n          img.parentNode?.removeChild(img)\n          updateContent()\n        }\n        \n        // Resize editor when image loads\n        img.onload = () => {\n          console.log('Image loaded successfully:', localUrl)\n          autoResizeEditor()\n        }\n        \n        const selection = window.getSelection()\n        if (selection && selection.rangeCount > 0) {\n          const range = selection.getRangeAt(0)\n          range.insertNode(img)\n          range.setStartAfter(img)\n          range.collapse(true)\n          selection.removeAllRanges()\n          selection.addRange(range)\n          \n          updateContent()\n          \n          // Auto-resize after image insertion\n          setTimeout(() => {\n            autoResizeEditor()\n          }, 100) // Increased timeout\n        }\n      }\n    }\n    \n    // Reset input\n    if (event.target) {\n      event.target.value = ''\n    }\n  }\n\n  // Mock course files for reference\n  const courseFiles = [\n    { id: '1', name: 'Lecture 1 - Introduction.pdf', type: 'pdf' },\n    { id: '2', name: 'Assignment 1.docx', type: 'docx' },\n    { id: '3', name: 'Neural Networks Slides.pptx', type: 'pptx' },\n    { id: '4', name: 'Algorithms Cheat Sheet.pdf', type: 'pdf' },\n    { id: '5', name: 'Python Exercises.py', type: 'python' }\n  ]\n\n  // Insert file reference\n  const insertFileReference = (file: { id: string; name: string; type: string }) => {\n    if (editorRef.current) {\n      const fileRef = document.createElement('span')\n      fileRef.className = 'inline-flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-300 rounded text-sm border border-blue-400/30'\n      fileRef.innerHTML = `📄 ${file.name}`\n      fileRef.setAttribute('data-file-id', file.id)\n      \n      const selection = window.getSelection()\n      if (selection && selection.rangeCount > 0) {\n        const range = selection.getRangeAt(0)\n        range.insertNode(fileRef)\n        range.setStartAfter(fileRef)\n        range.collapse(true)\n        selection.removeAllRanges()\n        selection.addRange(range)\n        \n        updateContent()\n        editorRef.current.focus()\n      }\n    }\n    setShowFileDialog(false)\n  }\n\n  // Simple markdown renderer for messages\n  const renderMessageContent = (content: string) => {\n    // Basic markdown formatting\n    let formatted = content\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')  // Bold\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')              // Italic  \n      .replace(/`(.*?)`/g, '<code class=\"bg-white/10 px-1 rounded text-purple-300\">$1</code>') // Code\n\n    return <span dangerouslySetInnerHTML={{ __html: formatted }} />\n  }\n\n  const handleCreateTopic = async () => {\n    if (!newTopicTitle.trim() || !selectedCategory) return\n\n    const input: CreateTopicInput = {\n      title: newTopicTitle,\n      description: newTopicDescription,\n      category_id: selectedCategory.id,\n      initial_message: newTopicDescription || \"Topic created\"\n    }\n\n    const { data, error } = await createForumTopic(input)\n    if (data && !error) {\n      setNewTopicTitle(\"\")\n      setNewTopicDescription(\"\")\n      setIsCreatingTopic(false)\n      loadTopics(selectedCategory.id)\n    }\n  }\n\n  // Filter topics based on search\n  const filteredTopics = topics.filter(topic =>\n    topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    topic.description?.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  // Category Icons\n  const getCategoryIcon = (category: ForumCategory) => {\n    switch (category.name) {\n      case 'announcements': return '📢'\n      case 'general': return '💬'\n      case 'questions': return '❓'\n      default: return '📁'\n    }\n  }\n\n  const getCategoryColor = (category: ForumCategory) => {\n    switch (category.name) {\n      case 'announcements': return 'from-orange-500 to-red-500'\n      case 'general': return 'from-blue-500 to-cyan-500'\n      case 'questions': return 'from-green-500 to-emerald-500'\n      default: return 'from-purple-500 to-pink-500'\n    }\n  }\n\n  return (\n    <Card className=\"bg-white/5 backdrop-blur-xl border-white/10 h-[700px] flex flex-col\">\n      <CardHeader className=\"py-3\">\n        <CardTitle className=\"text-white flex items-center gap-2\">\n          💬 Discussion Forums\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"p-0 flex-1 flex flex-col min-h-0\">\n        <div className=\"flex flex-1 min-h-0\">\n          {/* Categories Panel - Left */}\n          <div className=\"w-72 border-r border-white/10 flex flex-col\">\n            <div className=\"p-3 border-b border-white/10\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <h3 className=\"text-white font-medium\">Categories</h3>\n                <Button\n                  size=\"sm\"\n                  variant=\"outline\"\n                  className=\"border-white/20 text-white hover:bg-white/10 h-7 px-2\"\n                >\n                  <Plus className=\"w-3 h-3 mr-1\" />\n                  New\n                </Button>\n              </div>\n              <div className=\"space-y-2\">\n                {categories.map((category) => (\n                  <div\n                    key={category.id}\n                    onClick={() => setSelectedCategory(category)}\n                    className={cn(\n                      \"p-3 rounded-lg cursor-pointer transition-all duration-200\",\n                      selectedCategory?.id === category.id\n                        ? `bg-gradient-to-r ${getCategoryColor(category)} text-white`\n                        : \"bg-white/5 hover:bg-white/10 text-white/80\"\n                    )}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-3\">\n                        <span className=\"text-lg\">{getCategoryIcon(category)}</span>\n                        <div>\n                          <h4 className=\"font-medium capitalize\">{category.name.replace('_', ' ')}</h4>\n                          <p className=\"text-xs opacity-70\">{category.description}</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xs opacity-70\">\n                          {category.type === 'forum' ? `${topics.length} topics` : 'Chat'}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Topics Panel - Middle (for Q&A) */}\n          {selectedCategory?.type === 'forum' && (\n            <div className={cn(\n              \"border-r border-white/10 flex flex-col transition-all duration-300\",\n              isTopicsVisible ? \"w-64\" : \"w-12\"\n            )}>\n              <div className=\"p-3 border-b border-white/10 flex items-center justify-between\">\n                {isTopicsVisible ? (\n                  <>\n                    <h3 className=\"text-white font-medium\">Topics</h3>\n                    <div className=\"flex gap-2\">\n                      <Button\n                        size=\"sm\"\n                        onClick={() => setIsCreatingTopic(true)}\n                        className=\"bg-green-500 hover:bg-green-600 text-white h-8\"\n                      >\n                        <Plus className=\"w-4 h-4\" />\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => setIsTopicsVisible(false)}\n                        className=\"border-white/20 text-white hover:bg-white/10 h-8 w-8 p-0\"\n                      >\n                        <EyeOff className=\"w-4 h-4\" />\n                      </Button>\n                    </div>\n                  </>\n                ) : (\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => setIsTopicsVisible(true)}\n                    className=\"border-white/20 text-white hover:bg-white/10 h-8 w-8 p-0\"\n                  >\n                    <Eye className=\"w-4 h-4\" />\n                  </Button>\n                )}\n              </div>\n\n              {isTopicsVisible && (\n                <>\n                  {/* Search Topics */}\n                  <div className=\"p-3 border-b border-white/10\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4\" />\n                      <Input\n                        placeholder=\"Search topics...\"\n                        value={searchQuery}\n                        onChange={(e) => setSearchQuery(e.target.value)}\n                        className=\"pl-10 bg-white/5 border-white/20 text-white placeholder:text-white/50\"\n                      />\n                    </div>\n                  </div>\n\n                  {/* Topics List */}\n                  <div className=\"flex-1 overflow-y-auto\">\n                    <div className=\"p-3 space-y-2\">\n                      {isCreatingTopic && (\n                        <Card className=\"bg-white/10 border-white/20\">\n                          <CardContent className=\"p-3 space-y-2\">\n                            <Input\n                              placeholder=\"Topic title...\"\n                              value={newTopicTitle}\n                              onChange={(e) => setNewTopicTitle(e.target.value)}\n                              className=\"bg-white/5 border-white/20 text-white placeholder:text-white/50\"\n                            />\n                            <Textarea\n                              placeholder=\"Topic description (optional)...\"\n                              value={newTopicDescription}\n                              onChange={(e) => setNewTopicDescription(e.target.value)}\n                              className=\"bg-white/5 border-white/20 text-white placeholder:text-white/50 h-20\"\n                            />\n                            <div className=\"flex gap-2\">\n                              <Button size=\"sm\" onClick={handleCreateTopic} className=\"bg-green-500 hover:bg-green-600\">\n                                Create\n                              </Button>\n                              <Button size=\"sm\" variant=\"outline\" onClick={() => setIsCreatingTopic(false)} className=\"border-white/20 text-white\">\n                                Cancel\n                              </Button>\n                            </div>\n                          </CardContent>\n                        </Card>\n                      )}\n\n                      {filteredTopics.map((topic) => (\n                        <div\n                          key={topic.id}\n                          onClick={() => setSelectedTopic(topic)}\n                          className={cn(\n                            \"p-3 rounded-lg cursor-pointer transition-all duration-200 border\",\n                            selectedTopic?.id === topic.id\n                              ? \"bg-purple-500/20 border-purple-400/40\"\n                              : \"bg-white/5 border-white/10 hover:bg-white/10\"\n                          )}\n                        >\n                          <div className=\"flex items-start justify-between\">\n                            <div className=\"flex-1\">\n                              <div className=\"flex items-center gap-2 mb-1\">\n                                {topic.is_pinned && <Pin className=\"h-3 w-3 text-yellow-400\" />}\n                                {topic.is_solved && <CheckCircle className=\"h-3 w-3 text-green-400\" />}\n                                {topic.is_locked && <Lock className=\"h-3 w-3 text-red-400\" />}\n                                <h4 className=\"text-white font-medium text-sm line-clamp-1\">\n                                  {topic.title}\n                                </h4>\n                              </div>\n                              <p className=\"text-white/60 text-xs line-clamp-2 mb-2\">\n                                {topic.description}\n                              </p>\n                              <div className=\"flex items-center gap-2 text-xs text-white/50\">\n                                <span>{topic.message_count} replies</span>\n                                <span>•</span>\n                                <span>{topic.participant_count} participants</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n\n                      {filteredTopics.length === 0 && !isCreatingTopic && (\n                        <div className=\"text-center py-8\">\n                          <p className=\"text-white/60 mb-4\">No topics found</p>\n                          <Button\n                            size=\"sm\"\n                            onClick={() => setIsCreatingTopic(true)}\n                            className=\"bg-green-500 hover:bg-green-600\"\n                          >\n                            <Plus className=\"w-4 h-4 mr-2\" />\n                            Create First Topic\n                          </Button>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </>\n              )}\n            </div>\n          )}\n\n          {/* Chat Interface - Right */}\n          <div className=\"flex-1 flex flex-col h-full\">\n            {/* Chat Header */}\n            <div className=\"p-3 border-b border-white/10 flex-shrink-0\">\n              <div>\n                <h3 className=\"text-white font-medium\">\n                  {selectedCategory?.name === 'announcements' \n                    ? '📢 Announcements' \n                    : selectedCategory?.name === 'general'\n                    ? '💬 General Chat'\n                    : selectedTopic \n                    ? `❓ ${selectedTopic.title}`\n                    : '❓ Select a Topic'\n                  }\n                </h3>\n                <p className=\"text-white/60 text-sm\">\n                  {selectedCategory?.name === 'announcements' \n                    ? 'One message per day limit'\n                    : selectedCategory?.type === 'chat'\n                    ? 'Chat freely with classmates'\n                    : selectedTopic\n                    ? selectedTopic.description\n                    : 'Choose a topic to start chatting'\n                  }\n                </p>\n              </div>\n            </div>\n\n            {/* Messages Area - Fixed Height with Scroll */}\n            <div \n              ref={messagesContainerRef}\n              className=\"flex-1 overflow-y-auto min-h-0\"\n              onScroll={checkScrollPosition}\n            >\n              <div className=\"p-3\">\n              <div className=\"space-y-1\">\n                {messages.map((message) => {\n                  const isCurrentUser = message.user_id === user.id\n                  const isEditing = editingMessageId === message.id\n                  \n                  return (\n                    <div \n                      key={message.id} \n                      className=\"flex gap-3 group\"\n                      onMouseEnter={() => setHoveredMessageId(message.id)}\n                      onMouseLeave={() => setHoveredMessageId(null)}\n                    >\n                      <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <span className=\"text-white text-sm font-medium\">\n                          {(message.user?.name || user?.name || user?.email || 'User')?.charAt(0).toUpperCase()}\n                        </span>\n                      </div>\n                      <div className=\"flex-1 relative\">\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <span className=\"text-white font-medium text-sm\">\n                            {message.user?.name || user?.name || user?.email?.split('@')[0] || 'Student'}\n                          </span>\n                          <span className=\"text-white/50 text-xs\">\n                            {new Date(message.created_at).toLocaleTimeString()}\n                          </span>\n                          {message.is_edited && (\n                            <Badge variant=\"outline\" className=\"border-white/20 text-white/60 text-xs\">\n                              edited\n                            </Badge>\n                          )}\n                        </div>\n\n\n\n                        <div className=\"bg-white/5 rounded-lg p-3\">\n                          {/* Edit Mode */}\n                          {isEditing ? (\n                            <div className=\"space-y-2\">\n                              {/* Rich text editor for editing */}\n                              <div className=\"relative bg-white/5 rounded-lg border border-white/20 focus-within:border-purple-400/50 transition-colors\">\n                                <div\n                                  ref={editEditorRef}\n                                  contentEditable\n                                  suppressContentEditableWarning={true}\n                                  className=\"w-full bg-transparent border-none outline-none text-white resize-none min-h-[60px] overflow-y-auto p-3 text-sm leading-5 [&_strong]:font-bold [&_em]:italic [&_u]:underline [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_li]:ml-4 [&_blockquote]:border-l-2 [&_blockquote]:border-white/30 [&_blockquote]:pl-3 [&_blockquote]:italic [&_img]:block [&_img]:my-2 empty:before:content-[attr(data-placeholder)] empty:before:text-white/50\"\n                                  onKeyDown={(e) => {\n                                    if (e.key === 'Enter' && e.ctrlKey) {\n                                      e.preventDefault()\n                                      handleSaveEdit(message.id)\n                                    }\n                                  }}\n                                  data-placeholder=\"Edit your message...\"\n                                />\n                              </div>\n                              <div className=\"flex gap-2\">\n                                <Button\n                                  size=\"sm\"\n                                  onClick={() => handleSaveEdit(message.id)}\n                                  className=\"bg-green-500 hover:bg-green-600 text-white\"\n                                >\n                                  Save\n                                </Button>\n                                <Button\n                                  size=\"sm\"\n                                  variant=\"outline\"\n                                  onClick={handleCancelEdit}\n                                  className=\"border-white/20 text-white hover:bg-white/10\"\n                                >\n                                  Cancel\n                                </Button>\n                              </div>\n                              <div className=\"text-xs text-white/40\">\n                                Press Ctrl + Enter to save quickly\n                              </div>\n                            </div>\n                          ) : (\n                            <>\n                              {/* Normal Message Display */}\n                              <div \n                                className=\"text-white text-sm [&_strong]:font-bold [&_em]:italic [&_u]:underline [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_li]:ml-4 [&_blockquote]:border-l-2 [&_blockquote]:border-white/30 [&_blockquote]:pl-3 [&_blockquote]:italic\"\n                                dangerouslySetInnerHTML={{ __html: message.content }}\n                              />\n                              \n                              {/* Referenced Files */}\n                              {message.referenced_files.length > 0 && (\n                                <div className=\"mt-2 space-y-1\">\n                                  {message.referenced_files.map((file, index) => (\n                                    <div key={index} className=\"flex items-center gap-2 p-2 bg-white/5 rounded text-xs\">\n                                      <FileText className=\"h-3 w-3 text-blue-400\" />\n                                      <span className=\"text-white/80\">{file.file_name}</span>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n\n                              {/* Image */}\n                              {message.image_url && (\n                                <div className=\"mt-2\">\n                                  <img \n                                    src={message.image_url} \n                                    alt=\"Shared image\" \n                                    className=\"max-w-xs rounded-lg\"\n                                  />\n                                </div>\n                              )}\n\n                              {/* Reactions */}\n                              {Object.keys(message.reactions).length > 0 && (\n                                <div className=\"flex gap-2 mt-2\">\n                                  {Object.entries(message.reactions).map(([emoji, users]) => (\n                                    <button\n                                      key={emoji}\n                                      onClick={() => handleQuickReaction(message.id, emoji)}\n                                      className={cn(\n                                        \"px-2 py-1 rounded text-xs transition-colors\",\n                                        users.includes(user.id)\n                                          ? \"bg-purple-500/20 text-purple-300\"\n                                          : \"bg-white/5 text-white/60 hover:bg-white/10\"\n                                      )}\n                                    >\n                                      {emoji} {users.length}\n                                    </button>\n                                  ))}\n                                </div>\n                              )}\n                            </>\n                          )}\n                        </div>\n\n                        {/* Message Actions - Always reserve space, show on hover */}\n                        {!isEditing && (\n                          <div className=\"flex items-center gap-1 mt-1 relative h-6\"> {/* Fixed height to prevent layout shift */}\n                            <TooltipProvider>\n                              {/* React Button for All Users */}\n                              <Tooltip>\n                                <TooltipTrigger asChild>\n                                  <Button\n                                    size=\"sm\"\n                                    variant=\"ghost\"\n                                    onClick={() => setShowReactionPicker(message.id)}\n                                    className={cn(\n                                      \"h-6 w-6 p-0 rounded-md transition-all duration-200 ease-in-out\",\n                                      hoveredMessageId === message.id \n                                        ? \"text-white/70 hover:text-white hover:bg-white/10 opacity-100\" \n                                        : \"text-white/20 opacity-0 pointer-events-none\"\n                                    )}\n                                  >\n                                    <Smile className=\"w-3 h-3\" />\n                                  </Button>\n                                </TooltipTrigger>\n                                <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                                  <p>Add reaction</p>\n                                </TooltipContent>\n                              </Tooltip>\n\n                              {/* Edit and Delete for Current User */}\n                              {isCurrentUser && (\n                                <>\n                                  <Tooltip>\n                                    <TooltipTrigger asChild>\n                                      <Button\n                                        size=\"sm\"\n                                        variant=\"ghost\"\n                                        onClick={() => handleEditMessage(message)}\n                                        className={cn(\n                                          \"h-6 w-6 p-0 rounded-md transition-all duration-200 ease-in-out\",\n                                          hoveredMessageId === message.id \n                                            ? \"text-white/70 hover:text-white hover:bg-white/10 opacity-100\" \n                                            : \"text-white/20 opacity-0 pointer-events-none\"\n                                        )}\n                                      >\n                                        <Edit2 className=\"w-3 h-3\" />\n                                      </Button>\n                                    </TooltipTrigger>\n                                    <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                                      <p>Edit message</p>\n                                    </TooltipContent>\n                                  </Tooltip>\n\n                                  <Tooltip>\n                                    <TooltipTrigger asChild>\n                                      <Button\n                                        size=\"sm\"\n                                        variant=\"ghost\"\n                                        onClick={() => handleDeleteMessage(message.id)}\n                                        className={cn(\n                                          \"h-6 w-6 p-0 rounded-md transition-all duration-200 ease-in-out\",\n                                          hoveredMessageId === message.id \n                                            ? \"text-white/70 hover:text-red-400 hover:bg-white/10 opacity-100\" \n                                            : \"text-white/20 opacity-0 pointer-events-none\"\n                                        )}\n                                      >\n                                        <Trash2 className=\"w-3 h-3\" />\n                                      </Button>\n                                    </TooltipTrigger>\n                                    <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                                      <p>Delete message</p>\n                                    </TooltipContent>\n                                  </Tooltip>\n                                </>\n                              )}\n                            </TooltipProvider>\n\n                            {/* Reaction Picker */}\n                            {showReactionPicker === message.id && (\n                              <div className=\"reaction-picker absolute bottom-full left-0 mb-1 bg-gray-900 border border-gray-700 rounded-lg p-2 z-20 shadow-xl\">\n                                <div className=\"grid grid-cols-4 gap-1\">\n                                  {['❤️', '👍', '👎', '😂', '😮', '😢', '🔥', '👏'].map((emoji) => (\n                                    <button\n                                      key={emoji}\n                                      onClick={() => handleQuickReaction(message.id, emoji)}\n                                      className=\"text-lg hover:bg-gray-800 rounded p-1 transition-colors\"\n                                    >\n                                      {emoji}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )\n                })}\n                <div ref={messagesEndRef} />\n              </div>\n            </div>\n            </div>\n\n            {/* Message Input - Fixed at Bottom */}\n            {(selectedCategory?.type !== 'forum' || selectedTopic) && (\n              <TooltipProvider>\n                <div className=\"p-3 border-t border-white/10 flex-shrink-0\">\n                  {/* Rich Text Editor */}\n                  <div className=\"relative bg-white/5 rounded-lg border border-white/20 focus-within:border-purple-400/50 transition-colors\">\n                    <div\n                      ref={editorRef}\n                      contentEditable\n                      suppressContentEditableWarning={true}\n                      className=\"w-full bg-transparent border-none outline-none text-white resize-none min-h-[44px] overflow-y-auto p-3 text-sm leading-5 [&_strong]:font-bold [&_em]:italic [&_u]:underline [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_li]:ml-4 [&_blockquote]:border-l-2 [&_blockquote]:border-white/30 [&_blockquote]:pl-3 [&_blockquote]:italic [&_img]:block [&_img]:my-2 empty:before:content-[attr(data-placeholder)] empty:before:text-white/50\"\n                      style={{ height: '44px' }}\n                      onInput={updateContent}\n                      onKeyDown={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault()\n                          handleSendMessage()\n                        }\n                      }}\n                      data-placeholder={\n                        selectedCategory?.name === 'announcements'\n                          ? \"Share an important announcement...\"\n                          : \"Type your message...\"\n                      }\n                    />\n                  </div>\n\n                  {/* Hidden file input */}\n                  <input\n                    type=\"file\"\n                    ref={fileInputRef}\n                    onChange={handleImageUpload}\n                    accept=\"image/*\"\n                    className=\"hidden\"\n                  />\n\n                  {/* Action Buttons */}\n                  <div className=\"flex items-center justify-between mt-2\">\n                    <div className=\"flex items-center gap-1 relative\">\n                      <Tooltip>\n                        <TooltipTrigger asChild>\n                          <Button\n                            size=\"sm\"\n                            variant=\"ghost\"\n                            onClick={() => fileInputRef.current?.click()}\n                            className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                          >\n                            <ImageIcon className=\"w-4 h-4\" />\n                          </Button>\n                        </TooltipTrigger>\n                        <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                          <p>Add Images</p>\n                        </TooltipContent>\n                      </Tooltip>\n                      \n                      <div className=\"relative\">\n                        <Tooltip>\n                          <TooltipTrigger asChild>\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              onClick={() => setShowFileDialog(!showFileDialog)}\n                              className={cn(\n                                \"h-8 w-8 p-0 rounded-md transition-colors\",\n                                showFileDialog \n                                  ? \"text-blue-400 bg-blue-400/10\" \n                                  : \"text-white/60 hover:text-white hover:bg-white/10\"\n                              )}\n                            >\n                              <FileText className=\"w-4 h-4\" />\n                            </Button>\n                          </TooltipTrigger>\n                          <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                            <p>Reference Files</p>\n                          </TooltipContent>\n                        </Tooltip>\n                        \n                        {/* File Dialog */}\n                        {showFileDialog && (\n                          <div className=\"file-dialog absolute bottom-full left-0 mb-2 w-64 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50\">\n                            <div className=\"p-3\">\n                              <h4 className=\"text-white text-sm font-medium mb-2\">Course Files</h4>\n                              <div className=\"max-h-32 overflow-y-auto space-y-1\">\n                                {courseFiles.map((file) => (\n                                  <button\n                                    key={file.id}\n                                    onClick={() => insertFileReference(file)}\n                                    className=\"w-full text-left p-2 rounded hover:bg-gray-800 transition-colors\"\n                                  >\n                                    <div className=\"flex items-center gap-2\">\n                                      <FileText className=\"w-3 h-3 text-blue-400\" />\n                                      <span className=\"text-white text-xs truncate\">{file.name}</span>\n                                    </div>\n                                  </button>\n                                ))}\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                      \n                      <div className=\"relative\">\n                        <Tooltip>\n                          <TooltipTrigger asChild>\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                              className={cn(\n                                \"h-8 w-8 p-0 rounded-md transition-colors\",\n                                showEmojiPicker \n                                  ? \"text-yellow-400 bg-yellow-400/10\" \n                                  : \"text-white/60 hover:text-white hover:bg-white/10\"\n                              )}\n                            >\n                              <Smile className=\"w-4 h-4\" />\n                            </Button>\n                          </TooltipTrigger>\n                          <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                            <p>Add Emoji</p>\n                          </TooltipContent>\n                        </Tooltip>\n                        \n                        {/* Emoji Picker */}\n                        {showEmojiPicker && (\n                          <div className=\"emoji-picker absolute bottom-full left-0 mb-2 w-64 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50\">\n                            <div className=\"p-3\">\n                              <h4 className=\"text-white text-sm font-medium mb-2\">Choose Emoji</h4>\n                              <div className=\"grid grid-cols-8 gap-1 max-h-32 overflow-y-auto\">\n                                {emojis.map((emoji, index) => (\n                                  <button\n                                    key={index}\n                                    onClick={() => insertEmoji(emoji)}\n                                    className=\"w-6 h-6 text-lg hover:bg-gray-800 rounded transition-colors flex items-center justify-center\"\n                                    title={emoji}\n                                  >\n                                    {emoji}\n                                  </button>\n                                ))}\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                      \n                      <Tooltip>\n                        <TooltipTrigger asChild>\n                          <Button\n                            size=\"sm\"\n                            variant=\"ghost\"\n                            onClick={() => setShowFormatting(!showFormatting)}\n                            className={cn(\n                              \"h-8 w-8 p-0 rounded-md transition-colors\",\n                              showFormatting \n                                ? \"text-purple-400 bg-purple-400/10\" \n                                : \"text-white/60 hover:text-white hover:bg-white/10\"\n                            )}\n                          >\n                            {showFormatting ? <ChevronDown className=\"w-4 h-4\" /> : <Type className=\"w-4 h-4\" />}\n                          </Button>\n                        </TooltipTrigger>\n                        <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                          <p>Text Formatting</p>\n                        </TooltipContent>\n                      </Tooltip>\n\n                      {/* Horizontal Formatting Options */}\n                      {showFormatting && (\n                        <>\n                          <div className=\"w-px h-4 bg-white/20 mx-1\" />\n                          \n                          <Tooltip>\n                            <TooltipTrigger asChild>\n                              <Button\n                                size=\"sm\"\n                                variant=\"ghost\"\n                                onClick={() => applyFormatting('bold')}\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                              >\n                                <Bold className=\"w-4 h-4\" />\n                              </Button>\n                            </TooltipTrigger>\n                            <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                              <p>Bold</p>\n                            </TooltipContent>\n                          </Tooltip>\n                          \n                          <Tooltip>\n                            <TooltipTrigger asChild>\n                              <Button\n                                size=\"sm\"\n                                variant=\"ghost\"\n                                onClick={() => applyFormatting('italic')}\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                              >\n                                <Italic className=\"w-4 h-4\" />\n                              </Button>\n                            </TooltipTrigger>\n                            <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                              <p>Italic</p>\n                            </TooltipContent>\n                          </Tooltip>\n                          \n                          <Tooltip>\n                            <TooltipTrigger asChild>\n                              <Button\n                                size=\"sm\"\n                                variant=\"ghost\"\n                                onClick={() => applyFormatting('underline')}\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                              >\n                                <Underline className=\"w-4 h-4\" />\n                              </Button>\n                            </TooltipTrigger>\n                            <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                              <p>Underline</p>\n                            </TooltipContent>\n                          </Tooltip>\n                          \n                          <Tooltip>\n                            <TooltipTrigger asChild>\n                              <Button\n                                size=\"sm\"\n                                variant=\"ghost\"\n                                onClick={() => applyFormatting('insertUnorderedList')}\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                              >\n                                <List className=\"w-4 h-4\" />\n                              </Button>\n                            </TooltipTrigger>\n                            <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                              <p>Bullet List</p>\n                            </TooltipContent>\n                          </Tooltip>\n                          \n                          <Tooltip>\n                            <TooltipTrigger asChild>\n                              <Button\n                                size=\"sm\"\n                                variant=\"ghost\"\n                                onClick={() => applyFormatting('insertOrderedList')}\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                              >\n                                <ListOrdered className=\"w-4 h-4\" />\n                              </Button>\n                            </TooltipTrigger>\n                            <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                              <p>Numbered List</p>\n                            </TooltipContent>\n                          </Tooltip>\n                          \n                          <Tooltip>\n                            <TooltipTrigger asChild>\n                              <Button\n                                size=\"sm\"\n                                variant=\"ghost\"\n                                onClick={() => applyFormatting('formatBlock', 'blockquote')}\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md\"\n                              >\n                                <Quote className=\"w-4 h-4\" />\n                              </Button>\n                            </TooltipTrigger>\n                            <TooltipContent className=\"bg-gray-900 text-white border-gray-700\">\n                              <p>Quote</p>\n                            </TooltipContent>\n                          </Tooltip>\n                        </>\n                      )}\n                    </div>\n                    \n                    <Button\n                      onClick={handleSendMessage}\n                      disabled={!messageContent.trim() || isSending}\n                      size=\"sm\"\n                      className={cn(\n                        \"h-8 px-3 transition-all rounded-md\",\n                        messageContent.trim() && !isSending\n                          ? \"bg-purple-500 hover:bg-purple-600 text-white\"\n                          : \"bg-white/10 text-white/40 cursor-not-allowed\"\n                      )}\n                    >\n                      {isSending ? (\n                        <>\n                          <div className=\"w-4 h-4 mr-1 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                          {pendingImages.length > 0 ? 'Uploading...' : 'Sending...'}\n                        </>\n                      ) : (\n                        <>\n                          <Send className=\"w-4 h-4 mr-1\" />\n                          Send\n                        </>\n                      )}\n                    </Button>\n                  </div>\n                  \n                  {/* Helper Text */}\n                  <div className=\"mt-2 text-xs text-white/40\">\n                    Press Enter to send, Shift + Enter for new line\n                  </div>\n                </div>\n              </TooltipProvider>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgCA;AAWA;;;AArDA;;;;;;;;;;;;AA4DO,SAAS,eAAe,KAAuC;QAAvC,EAAE,QAAQ,EAAE,IAAI,EAAuB,GAAvC;;IAC7B,mBAAmB;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAG3D,WAAW;IACX,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,EAAE;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5E,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QACf,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;QACjD,IAAI,QAAQ,CAAC,OAAO;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAC7C,IAAI,QAAQ,CAAC,OAAO;YAClB,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,OAAO,YAAoB;QAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;QAC3D,IAAI,QAAQ,CAAC,OAAO;YAClB,YAAY;QACd;IACF;IAIA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;mCAAG;QAAC;KAAS;IAEb,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW,MAAM,GAAG,KAAK,CAAC,kBAAkB;gBAC9C,oBAAoB,UAAU,CAAC,EAAE;YACnC;QACF;mCAAG;QAAC;QAAY;KAAiB;IAEjC,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB;gBACpB,IAAI,iBAAiB,IAAI,KAAK,SAAS;oBACrC,WAAW,iBAAiB,EAAE;gBAChC,OAAO;oBACL,UAAU,EAAE;oBACZ,iBAAiB;oBACjB,aAAa,iBAAiB,EAAE;gBAClC;YACF;QACF;mCAAG;QAAC;KAAiB;IAErB,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe;gBACjB,aAAa,iBAAkB,EAAE,EAAE,cAAc,EAAE;YACrD;QACF;mCAAG;QAAC;KAAc;IAElB,8DAA8D;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEpD,2CAA2C;IAC3C,MAAM,sBAAsB;QAC1B,IAAI,qBAAqB,OAAO,EAAE;YAChC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,qBAAqB,OAAO;YAC9E,MAAM,SAAS,eAAe,YAAY,eAAe,IAAI,yBAAyB;;YACtF,gBAAgB;QAClB;IACF;IAEA,sDAAsD;IACtD,MAAM,4BAA4B;QAChC,IAAI,qBAAqB,OAAO,EAAE;YAChC,qBAAqB,OAAO,CAAC,SAAS,GAAG,qBAAqB,OAAO,CAAC,YAAY;QACpF;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,IAAI,eAAe,oBAAoB,cAAc;oBACnD,uCAAuC;oBACvC;oDAAW;4BACT;4BACA,IAAI,aAAa;gCACf,eAAe;4BACjB;4BACA,IAAI,kBAAkB;gCACpB,oBAAoB;4BACtB;wBACF;mDAAG;gBACL;YACF;QACF;mCAAG;QAAC;QAAU;QAAkB;QAAc;KAAY;IAE1D,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,eAAe;QACjB;mCAAG;QAAC;QAAkB;KAAc;IAEpC,+BAA+B;IAC/B,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,SAAS,WAAW;QAClC,MAAM,YAAY,OAAO,YAAY;QAErC,8BAA8B;QAC9B,MAAM,kBAAkB,CAAC;QACzB,MAAM,QAAQ,CAAC;QAEf,6CAA6C;QAC7C,sBAAA,gCAAA,UAAW,eAAe;QAC1B,sBAAA,gCAAA,UAAW,QAAQ,CAAC;IACtB;IAEA,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,oBAAoB,cAAc,OAAO,EAAE;gBAC7C,4CAA4C;gBAC5C,cAAc,OAAO,CAAC,SAAS,GAAG;gBAElC,yCAAyC;gBACzC;gDAAW;wBACT,IAAI,cAAc,OAAO,EAAE;4BACzB,cAAc,OAAO,CAAC,KAAK;4BAC3B,eAAe,cAAc,OAAO;wBACtC;oBACF;+CAAG,KAAI,wBAAwB;YACjC;QACF;mCAAG;QAAC;KAAiB,GAAE,uDAAuD;IAE9E,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAe;IAEnB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;+DAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,oBAAoB,CAAC,OAAO,OAAO,CAAC,iBAAiB;wBACvE,mBAAmB;wBACnB,kBAAkB;oBACpB;oBACA,IAAI,CAAC,OAAO,OAAO,CAAC,qBAAqB;wBACvC,sBAAsB;oBACxB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;4CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;mCAAG,EAAE;IAEL,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;4CAAO;oBACL;gBACF;;QACF;mCAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,QAAQ,GAAG,CAAC,+BAA+B,cAAc,MAAM;QAC/D,cAAc,OAAO,CAAC;gBAAC,EAAE,QAAQ,EAAE;YACjC,QAAQ,GAAG,CAAC,sBAAsB;YAClC,IAAI,eAAe,CAAC;QACtB;QACA,iBAAiB,EAAE;IACrB;IAEA,4FAA4F;IAC5F,MAAM,yBAAyB,CAAC;QAC9B,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,SAAS,GAAG;QAEpB,uCAAuC;QACvC,MAAM,SAAS,QAAQ,gBAAgB,CAAC;QACxC,OAAO,OAAO,CAAC,CAAC,KAAK;gBAEnB;YADA,MAAM,cAAc,SAAS,cAAc,CAAC,AAAC,UAAmB,OAAV,QAAQ,GAAE;aAChE,kBAAA,IAAI,UAAU,cAAd,sCAAA,gBAAgB,YAAY,CAAC,aAAa;QAC5C;QAEA,6BAA6B;QAC7B,OAAO,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;IACrD;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,oBAAoB,QAAQ,EAAE;QAC9B,mEAAmE;QACnE,IAAI,kBAAkB,QAAQ,OAAO;QAErC,8EAA8E;QAC9E,IAAI,QAAQ,SAAS,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,SAAS;YAC1D,kBAAkB,QAAQ,OAAO,GAAG,AAAC,aAA8B,OAAlB,QAAQ,SAAS,EAAC;QACrE;QAEA,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,eAAe,IAAI,IAAI;QAE5B,IAAI;gBAEgB;YADlB,4CAA4C;YAC5C,IAAI,cAAc,EAAA,yBAAA,cAAc,OAAO,cAArB,6CAAA,uBAAuB,SAAS,KAAI;YAEtD,sEAAsE;YACtE,MAAM,eAAe,YAAY,OAAO,CAAC,eAAe,IAAI,IAAI;YAEhE,IAAI,CAAC,cAAc;YAEnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;YAC5D,IAAI,QAAQ,CAAC,OAAO;gBAClB,oBAAoB;gBACpB,kBAAkB;gBAClB,gCAAgC;gBAChC,IAAI,cAAc,OAAO,EAAE;oBACzB,cAAc,OAAO,CAAC,SAAS,GAAG;gBACpC;gBACA,aAAa,iBAAkB,EAAE,EAAE,0BAAA,oCAAA,cAAe,EAAE;YACtD,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,mBAAmB;QACvB,oBAAoB;QACpB,kBAAkB;QAClB,gCAAgC;QAChC,IAAI,cAAc,OAAO,EAAE;YACzB,cAAc,OAAO,CAAC,SAAS,GAAG;QACpC;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC3C,IAAI,CAAC,OAAO;gBACV,aAAa,iBAAkB,EAAE,EAAE,0BAAA,oCAAA,cAAe,EAAE;YACtD,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,sBAAsB,OAAO,WAAmB;QACpD,IAAI;gBAKkB;YAJpB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,IAAI,CAAC,SAAS;YAEd,yDAAyD;YACzD,MAAM,eAAc,2BAAA,QAAQ,SAAS,CAAC,MAAM,cAAxB,+CAAA,yBAA0B,QAAQ,CAAC,KAAK,EAAE;YAE9D,IAAI,aAAa;gBACf,uBAAuB;gBACvB,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YAClC,OAAO;gBACL,4EAA4E;gBAC5E,KAAK,MAAM,CAAC,eAAe,MAAM,IAAI,OAAO,OAAO,CAAC,QAAQ,SAAS,EAAG;oBACtE,IAAI,MAAM,QAAQ,CAAC,KAAK,EAAE,GAAG;wBAC3B,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;wBAChC;oBACF;gBACF;gBAEA,uBAAuB;gBACvB,MAAM,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,WAAW;YAC/B;YAEA,aAAa,iBAAkB,EAAE,EAAE,0BAAA,oCAAA,cAAe,EAAE;YACpD,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,mBAAmB;IACnB,MAAM,oBAAoB;QACxB,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,oBAAoB,WAAW;QAE9D,aAAa;QAEb,IAAI;gBAEgB;YADlB,uCAAuC;YACvC,IAAI,cAAc,EAAA,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,SAAS,KAAI;YAClD,IAAI,mBAAkC;YAEtC,qEAAqE;YACrE,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,KAAK,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,cAAe;oBAC9C,IAAI;wBACF,MAAM,cAAc,MAAM,sBAAsB;wBAChD,IAAI,aAAa;4BACf,oCAAoC;4BACpC,IAAI,CAAC,kBAAkB;gCACrB,mBAAmB;4BACrB;wBACF;wBACA,qBAAqB;wBACrB,IAAI,eAAe,CAAC;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;gBAEA,sEAAsE;gBACtE,cAAc,YAAY,OAAO,CAAC,eAAe;gBAEjD,uBAAuB;gBACvB,iBAAiB,EAAE;YACrB;YAEA,MAAM,QAA4B;gBAChC,SAAS;gBACT,aAAa,iBAAiB,EAAE;gBAChC,QAAQ,EAAE,0BAAA,oCAAA,cAAe,EAAE;gBAC3B,WAAW,iBAAiB,8BAA8B;YAC5D;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YACjD,IAAI,QAAQ,CAAC,OAAO;gBAClB,kBAAkB;gBAClB,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,SAAS,GAAG;gBAChC;gBACA;gBACA,oBAAoB,OAAM,6CAA6C;gBACvE,aAAa,iBAAiB,EAAE,EAAE,0BAAA,oCAAA,cAAe,EAAE;YACrD,OAAO;gBACL,oEAAoE;gBACpE,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YAEjC,2CAA2C;YAC3C,MAAM,YAAY,UAAU,OAAO,CAAC,aAAa,CAAC;YAClD,MAAM,YAAY,YAAY,MAAM,IAAI,8CAA8C;;YAEtF,MAAM,YAAY,KAAK,GAAG,CAAC,UAAU,OAAO,CAAC,YAAY,EAAE;YAC3D,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,AAAC,GAA0B,OAAxB,KAAK,GAAG,CAAC,WAAW,KAAI,OAAI,wBAAwB;QAC1F;IACF;IAEA,iCAAiC;IACjC,MAAM,kBAAkB,CAAC,SAAiB;QACxC,SAAS,WAAW,CAAC,SAAS,OAAO;QACrC,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK;YACvB;QACF;IACF;IAEA,sCAAsC;IACtC,MAAM,gBAAgB;QACpB,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,cAAc,UAAU,OAAO,CAAC,SAAS;YAC/C,gDAAgD;YAChD,MAAM,cAAc,UAAU,OAAO,CAAC,SAAS,IAAI,UAAU,OAAO,CAAC,WAAW,IAAI;YAEpF,kDAAkD;YAClD,MAAM,YAAY,UAAU,OAAO,CAAC,aAAa,CAAC;YAClD,MAAM,aAAa,YAAY,IAAI,MAAM;YAEzC,kBAAkB,aAAc,eAAe,MAAO;QACxD;IACF;IAEA,yEAAyE;IAEzE,gDAAgD;IAChD,MAAM,yBAAyB,SAAC;YAAgB,yEAAgB;QAC9D,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,YAAY,OAAO,YAAY;QACrC,IAAI,aAAa,UAAU,UAAU,GAAG,GAAG;YACzC,MAAM,QAAQ,UAAU,UAAU,CAAC;YACnC,MAAM,eAAe,MAAM,QAAQ;YAEnC,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG,SAAS,eAAe;YAE5C,MAAM,cAAc;YACpB,MAAM,UAAU,CAAC;YAEjB,qBAAqB;YACrB,UAAU,eAAe;YACzB,MAAM,WAAW,SAAS,WAAW;YACrC,SAAS,aAAa,CAAC;YACvB,SAAS,QAAQ,CAAC;YAClB,UAAU,QAAQ,CAAC;YAEnB;QACF;IACF;IAEA,aAAa;IACb,MAAM,SAAS;QACb;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAM;QAAK;QAAM;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QACnD;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACvD;IAED,2BAA2B;IAC3B,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,YAAY,OAAO,YAAY;YACrC,IAAI,aAAa,UAAU,UAAU,GAAG,GAAG;gBACzC,MAAM,QAAQ,UAAU,UAAU,CAAC;gBACnC,MAAM,WAAW,SAAS,cAAc,CAAC;gBACzC,MAAM,UAAU,CAAC;gBAEjB,0BAA0B;gBAC1B,MAAM,aAAa,CAAC;gBACpB,MAAM,QAAQ,CAAC;gBACf,UAAU,eAAe;gBACzB,UAAU,QAAQ,CAAC;gBAEnB;gBACA,UAAU,OAAO,CAAC,KAAK;YACzB;QACF;QACA,mBAAmB;IACrB;IAEA,mCAAmC;IACnC,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,iCAAiC;YACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;gBACT,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,yDAAyD;YACzD,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,AAAC,GAAa,OAAX,KAAK,EAAE,EAAC,KAAiB,OAAd,KAAK,GAAG,IAAG,KAA8C,OAA3C,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAG,KAAW,OAAR;YAExF,6BAA6B;YAC7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAC3C,IAAI,CAAC,gBACL,MAAM,CAAC,UAAU,MAAM;gBACtB,cAAc;gBACd,QAAQ;YACV;YAEF,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;YACT;YAEA,iBAAiB;YACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAC7C,IAAI,CAAC,gBACL,YAAY,CAAC;YAEhB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,0DAA0D;IAC1D,MAAM,oBAAoB,CAAC;YACZ;QAAb,MAAM,QAAO,sBAAA,MAAM,MAAM,CAAC,KAAK,cAAlB,0CAAA,mBAAoB,CAAC,EAAE;QACpC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAC1C,QAAQ,GAAG,CAAC,+BAA+B,KAAK,IAAI,EAAE,KAAK,IAAI;YAE/D,2BAA2B;YAC3B,MAAM,WAAW,IAAI,eAAe,CAAC;YACrC,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,wBAAwB;YACxB,iBAAiB,CAAA;gBACf,MAAM,YAAY;uBAAI;oBAAM;wBAAE;wBAAM;oBAAS;iBAAE;gBAC/C,QAAQ,GAAG,CAAC,2BAA2B,UAAU,MAAM;gBACvD,OAAO;YACT;YAEA,+BAA+B;YAC/B,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,MAAM,SAAS,aAAa,CAAC;gBACnC,IAAI,GAAG,GAAG;gBACV,IAAI,KAAK,CAAC,QAAQ,GAAG;gBACrB,IAAI,KAAK,CAAC,YAAY,GAAG;gBACzB,IAAI,KAAK,CAAC,MAAM,GAAG;gBACnB,IAAI,KAAK,CAAC,OAAO,GAAG;gBACpB,IAAI,YAAY,CAAC,sBAAsB,WAAU,kBAAkB;gBAEnE,gCAAgC;gBAChC,IAAI,OAAO,GAAG;wBAEZ;oBADA,QAAQ,KAAK,CAAC,iCAAiC;qBAC/C,kBAAA,IAAI,UAAU,cAAd,sCAAA,gBAAgB,WAAW,CAAC;oBAC5B;gBACF;gBAEA,iCAAiC;gBACjC,IAAI,MAAM,GAAG;oBACX,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C;gBACF;gBAEA,MAAM,YAAY,OAAO,YAAY;gBACrC,IAAI,aAAa,UAAU,UAAU,GAAG,GAAG;oBACzC,MAAM,QAAQ,UAAU,UAAU,CAAC;oBACnC,MAAM,UAAU,CAAC;oBACjB,MAAM,aAAa,CAAC;oBACpB,MAAM,QAAQ,CAAC;oBACf,UAAU,eAAe;oBACzB,UAAU,QAAQ,CAAC;oBAEnB;oBAEA,oCAAoC;oBACpC,WAAW;wBACT;oBACF,GAAG,MAAK,oBAAoB;gBAC9B;YACF;QACF;QAEA,cAAc;QACd,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,KAAK,GAAG;QACvB;IACF;IAEA,kCAAkC;IAClC,MAAM,cAAc;QAClB;YAAE,IAAI;YAAK,MAAM;YAAgC,MAAM;QAAM;QAC7D;YAAE,IAAI;YAAK,MAAM;YAAqB,MAAM;QAAO;QACnD;YAAE,IAAI;YAAK,MAAM;YAA+B,MAAM;QAAO;QAC7D;YAAE,IAAI;YAAK,MAAM;YAA8B,MAAM;QAAM;QAC3D;YAAE,IAAI;YAAK,MAAM;YAAuB,MAAM;QAAS;KACxD;IAED,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG;YACpB,QAAQ,SAAS,GAAG,AAAC,MAAe,OAAV,KAAK,IAAI;YACnC,QAAQ,YAAY,CAAC,gBAAgB,KAAK,EAAE;YAE5C,MAAM,YAAY,OAAO,YAAY;YACrC,IAAI,aAAa,UAAU,UAAU,GAAG,GAAG;gBACzC,MAAM,QAAQ,UAAU,UAAU,CAAC;gBACnC,MAAM,UAAU,CAAC;gBACjB,MAAM,aAAa,CAAC;gBACpB,MAAM,QAAQ,CAAC;gBACf,UAAU,eAAe;gBACzB,UAAU,QAAQ,CAAC;gBAEnB;gBACA,UAAU,OAAO,CAAC,KAAK;YACzB;QACF;QACA,kBAAkB;IACpB;IAEA,wCAAwC;IACxC,MAAM,uBAAuB,CAAC;QAC5B,4BAA4B;QAC5B,IAAI,YAAY,QACb,OAAO,CAAC,kBAAkB,uBAAwB,OAAO;SACzD,OAAO,CAAC,cAAc,eAA4B,WAAW;SAC7D,OAAO,CAAC,YAAY,oEAAoE,OAAO;;QAElG,qBAAO,6LAAC;YAAK,yBAAyB;gBAAE,QAAQ;YAAU;;;;;;IAC5D;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,kBAAkB;QAEhD,MAAM,QAA0B;YAC9B,OAAO;YACP,aAAa;YACb,aAAa,iBAAiB,EAAE;YAChC,iBAAiB,uBAAuB;QAC1C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE;QAC/C,IAAI,QAAQ,CAAC,OAAO;YAClB,iBAAiB;YACjB,uBAAuB;YACvB,mBAAmB;YACnB,WAAW,iBAAiB,EAAE;QAChC;IACF;IAEA,gCAAgC;IAChC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;YAEnC;eADA,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,SAC1D,qBAAA,MAAM,WAAW,cAAjB,yCAAA,mBAAmB,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;IAGnE,iBAAiB;IACjB,MAAM,kBAAkB,CAAC;QACvB,OAAQ,SAAS,IAAI;YACnB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,IAAI;YACnB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAqC;;;;;;;;;;;0BAI5D,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAIrC,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;gDAEC,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,CAAA,6BAAA,uCAAA,iBAAkB,EAAE,MAAK,SAAS,EAAE,GAChC,AAAC,oBAA8C,OAA3B,iBAAiB,WAAU,iBAC/C;0DAGN,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAW,gBAAgB;;;;;;8EAC3C,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA0B,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;sFACnE,6LAAC;4EAAE,WAAU;sFAAsB,SAAS,WAAW;;;;;;;;;;;;;;;;;;sEAG3D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACZ,SAAS,IAAI,KAAK,UAAU,AAAC,GAAgB,OAAd,OAAO,MAAM,EAAC,aAAW;;;;;;;;;;;;;;;;;+CAnB1D,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;wBA8BzB,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,yBAC1B,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,sEACA,kBAAkB,SAAS;;8CAE3B,6LAAC;oCAAI,WAAU;8CACZ,gCACC;;0DACE,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,mBAAmB;wDAClC,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,mBAAmB;wDAClC,WAAU;kEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;qEAKxB,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;gCAKpB,iCACC;;sDAEE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;;;;;;;;;;;;;;;;;sDAMhB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,iCACC,6LAAC,mIAAA,CAAA,OAAI;wDAAC,WAAU;kEACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oEAChD,WAAU;;;;;;8EAEZ,6LAAC,uIAAA,CAAA,WAAQ;oEACP,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;oEACtD,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAS;4EAAmB,WAAU;sFAAkC;;;;;;sFAG1F,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;4EAAU,SAAS,IAAM,mBAAmB;4EAAQ,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;;;;;;oDAQ5H,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;4DAEC,SAAS,IAAM,iBAAiB;4DAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE,GAC1B,0CACA;sEAGN,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;gFACZ,MAAM,SAAS,kBAAI,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAClC,MAAM,SAAS,kBAAI,6LAAC,8NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;gFAC1C,MAAM,SAAS,kBAAI,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FACpC,6LAAC;oFAAG,WAAU;8FACX,MAAM,KAAK;;;;;;;;;;;;sFAGhB,6LAAC;4EAAE,WAAU;sFACV,MAAM,WAAW;;;;;;sFAEpB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;;wFAAM,MAAM,aAAa;wFAAC;;;;;;;8FAC3B,6LAAC;8FAAK;;;;;;8FACN,6LAAC;;wFAAM,MAAM,iBAAiB;wFAAC;;;;;;;;;;;;;;;;;;;;;;;;2DAzBhC,MAAM,EAAE;;;;;oDAgChB,eAAe,MAAM,KAAK,KAAK,CAAC,iCAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAClC,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,IAAM,mBAAmB;gEAClC,WAAU;;kFAEV,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAanD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,kBACxB,qBACA,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,YAC3B,oBACA,gBACA,AAAC,KAAwB,OAApB,cAAc,KAAK,IACxB;;;;;;0DAGN,6LAAC;gDAAE,WAAU;0DACV,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,kBACxB,8BACA,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,SAC3B,gCACA,gBACA,cAAc,WAAW,GACzB;;;;;;;;;;;;;;;;;8CAOV,6LAAC;oCACC,KAAK;oCACL,WAAU;oCACV,UAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;kDACf,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,GAAG,CAAC,CAAC;wDAaJ,OAAC,eAMC,gBAAoC;oDAlB/C,MAAM,gBAAgB,QAAQ,OAAO,KAAK,KAAK,EAAE;oDACjD,MAAM,YAAY,qBAAqB,QAAQ,EAAE;oDAEjD,qBACE,6LAAC;wDAEC,WAAU;wDACV,cAAc,IAAM,oBAAoB,QAAQ,EAAE;wDAClD,cAAc,IAAM,oBAAoB;;0EAExC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;+EACb,QAAC,EAAA,gBAAA,QAAQ,IAAI,cAAZ,oCAAA,cAAc,IAAI,MAAI,iBAAA,2BAAA,KAAM,IAAI,MAAI,iBAAA,2BAAA,KAAM,KAAK,KAAI,oBAApD,4BAAA,MAA6D,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0EAGvF,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,EAAA,iBAAA,QAAQ,IAAI,cAAZ,qCAAA,eAAc,IAAI,MAAI,iBAAA,2BAAA,KAAM,IAAI,MAAI,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,KAAK,CAAC,IAAI,CAAC,EAAE,KAAI;;;;;;0FAErE,6LAAC;gFAAK,WAAU;0FACb,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;4EAEjD,QAAQ,SAAS,kBAChB,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAAwC;;;;;;;;;;;;kFAQ/E,6LAAC;wEAAI,WAAU;kFAEZ,0BACC,6LAAC;4EAAI,WAAU;;8FAEb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFACC,KAAK;wFACL,eAAe;wFACf,gCAAgC;wFAChC,WAAU;wFACV,WAAW,CAAC;4FACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,OAAO,EAAE;gGAClC,EAAE,cAAc;gGAChB,eAAe,QAAQ,EAAE;4FAC3B;wFACF;wFACA,oBAAiB;;;;;;;;;;;8FAGrB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,qIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAS,IAAM,eAAe,QAAQ,EAAE;4FACxC,WAAU;sGACX;;;;;;sGAGD,6LAAC,qIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAQ;4FACR,SAAS;4FACT,WAAU;sGACX;;;;;;;;;;;;8FAIH,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;iGAKzC;;8FAEE,6LAAC;oFACC,WAAU;oFACV,yBAAyB;wFAAE,QAAQ,QAAQ,OAAO;oFAAC;;;;;;gFAIpD,QAAQ,gBAAgB,CAAC,MAAM,GAAG,mBACjC,6LAAC;oFAAI,WAAU;8FACZ,QAAQ,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnC,6LAAC;4FAAgB,WAAU;;8GACzB,6LAAC,iNAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;8GACpB,6LAAC;oGAAK,WAAU;8GAAiB,KAAK,SAAS;;;;;;;2FAFvC;;;;;;;;;;gFASf,QAAQ,SAAS,kBAChB,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFACC,KAAK,QAAQ,SAAS;wFACtB,KAAI;wFACJ,WAAU;;;;;;;;;;;gFAMf,OAAO,IAAI,CAAC,QAAQ,SAAS,EAAE,MAAM,GAAG,mBACvC,6LAAC;oFAAI,WAAU;8FACZ,OAAO,OAAO,CAAC,QAAQ,SAAS,EAAE,GAAG,CAAC;4FAAC,CAAC,OAAO,MAAM;6GACpD,6LAAC;4FAEC,SAAS,IAAM,oBAAoB,QAAQ,EAAE,EAAE;4FAC/C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+CACA,MAAM,QAAQ,CAAC,KAAK,EAAE,IAClB,qCACA;;gGAGL;gGAAM;gGAAE,MAAM,MAAM;;2FAThB;;;;;;;;;;;;;;;;;;oEAmBlB,CAAC,2BACA,6LAAC;wEAAI,WAAU;;4EAA4C;0FACzD,6LAAC,sIAAA,CAAA,kBAAe;;kGAEd,6LAAC,sIAAA,CAAA,UAAO;;0GACN,6LAAC,sIAAA,CAAA,iBAAc;gGAAC,OAAO;0GACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oGACL,MAAK;oGACL,SAAQ;oGACR,SAAS,IAAM,sBAAsB,QAAQ,EAAE;oGAC/C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,qBAAqB,QAAQ,EAAE,GAC3B,iEACA;8GAGN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wGAAC,WAAU;;;;;;;;;;;;;;;;0GAGrB,6LAAC,sIAAA,CAAA,iBAAc;gGAAC,WAAU;0GACxB,cAAA,6LAAC;8GAAE;;;;;;;;;;;;;;;;;oFAKN,+BACC;;0GACE,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAQ;4GACR,SAAS,IAAM,kBAAkB;4GACjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,qBAAqB,QAAQ,EAAE,GAC3B,iEACA;sHAGN,cAAA,6LAAC,qMAAA,CAAA,QAAK;gHAAC,WAAU;;;;;;;;;;;;;;;;kHAGrB,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,WAAU;kHACxB,cAAA,6LAAC;sHAAE;;;;;;;;;;;;;;;;;0GAIP,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAQ;4GACR,SAAS,IAAM,oBAAoB,QAAQ,EAAE;4GAC7C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,qBAAqB,QAAQ,EAAE,GAC3B,mEACA;sHAGN,cAAA,6LAAC,6MAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;;;;;;;;;;;kHAGtB,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,WAAU;kHACxB,cAAA,6LAAC;sHAAE;;;;;;;;;;;;;;;;;;;;;;;;;4EAQZ,uBAAuB,QAAQ,EAAE,kBAChC,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFAAI,WAAU;8FACZ;wFAAC;wFAAM;wFAAM;wFAAM;wFAAM;wFAAM;wFAAM;wFAAM;qFAAK,CAAC,GAAG,CAAC,CAAC,sBACrD,6LAAC;4FAEC,SAAS,IAAM,oBAAoB,QAAQ,EAAE,EAAE;4FAC/C,WAAU;sGAET;2FAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDA1MhB,QAAQ,EAAE;;;;;gDAyNrB;8DACA,6LAAC;oDAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;gCAMb,CAAC,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,WAAW,aAAa,mBACnD,6LAAC,sIAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK;oDACL,eAAe;oDACf,gCAAgC;oDAChC,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAO;oDACxB,SAAS;oDACT,WAAW,CAAC;wDACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;4DACpC,EAAE,cAAc;4DAChB;wDACF;oDACF;oDACA,oBACE,CAAA,6BAAA,uCAAA,iBAAkB,IAAI,MAAK,kBACvB,uCACA;;;;;;;;;;;0DAMV,6LAAC;gDACC,MAAK;gDACL,KAAK;gDACL,UAAU;gDACV,QAAO;gDACP,WAAU;;;;;;0DAIZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,sIAAA,CAAA,UAAO;;kFACN,6LAAC,sIAAA,CAAA,iBAAc;wEAAC,OAAO;kFACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS;oFAAM;wFAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;4EAC1C,WAAU;sFAEV,cAAA,6LAAC,uMAAA,CAAA,QAAS;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAGzB,6LAAC,sIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACxB,cAAA,6LAAC;sFAAE;;;;;;;;;;;;;;;;;0EAIP,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,kBAAkB,CAAC;oFAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,iBACI,iCACA;8FAGN,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGxB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;oEAKN,gCACC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAsC;;;;;;8FACpD,6LAAC;oFAAI,WAAU;8FACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;4FAEC,SAAS,IAAM,oBAAoB;4FACnC,WAAU;sGAEV,cAAA,6LAAC;gGAAI,WAAU;;kHACb,6LAAC,iNAAA,CAAA,WAAQ;wGAAC,WAAU;;;;;;kHACpB,6LAAC;wGAAK,WAAU;kHAA+B,KAAK,IAAI;;;;;;;;;;;;2FANrD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAgB1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,mBAAmB,CAAC;oFACnC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,kBACI,qCACA;8FAGN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGrB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;oEAKN,iCACC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAsC;;;;;;8FACpD,6LAAC;oFAAI,WAAU;8FACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4FAEC,SAAS,IAAM,YAAY;4FAC3B,WAAU;4FACV,OAAO;sGAEN;2FALI;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAcnB,6LAAC,sIAAA,CAAA,UAAO;;kFACN,6LAAC,sIAAA,CAAA,iBAAc;wEAAC,OAAO;kFACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,kBAAkB,CAAC;4EAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,iBACI,qCACA;sFAGL,+BAAiB,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;qGAAe,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG5E,6LAAC,sIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACxB,cAAA,6LAAC;sFAAE;;;;;;;;;;;;;;;;;4DAKN,gCACC;;kFACE,6LAAC;wEAAI,WAAU;;;;;;kFAEf,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,gBAAgB;oFAC/B,WAAU;8FAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGpB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;kFAIP,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,gBAAgB;oFAC/B,WAAU;8FAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGtB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;kFAIP,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,gBAAgB;oFAC/B,WAAU;8FAEV,cAAA,6LAAC,+MAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGzB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;kFAIP,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,gBAAgB;oFAC/B,WAAU;8FAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGpB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;kFAIP,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,gBAAgB;oFAC/B,WAAU;8FAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAG3B,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;kFAIP,6LAAC,sIAAA,CAAA,UAAO;;0FACN,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,gBAAgB,eAAe;oFAC9C,WAAU;8FAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGrB,6LAAC,sIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACxB,cAAA,6LAAC;8FAAE;;;;;;;;;;;;;;;;;;;;;;;;;kEAOb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU,CAAC,eAAe,IAAI,MAAM;wDACpC,MAAK;wDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sCACA,eAAe,IAAI,MAAM,CAAC,YACtB,iDACA;kEAGL,0BACC;;8EACE,6LAAC;oEAAI,WAAU;;;;;;gEACd,cAAc,MAAM,GAAG,IAAI,iBAAiB;;yFAG/C;;8EACE,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;0DAQzC,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D;GAp6CgB;KAAA", "debugId": null}}, {"offset": {"line": 5637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n          aria-label=\"Close\"\n          className=\"absolute top-4 right-4 inline-flex size-8 items-center justify-center\n                     rounded-md text-white hover:bg-white/15\n                     focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2\n                     focus:ring-offset-gray-900\n                     transition\n                     [&_svg]:pointer-events-none [&_svg]:size-5\"\n        >\n          <XIcon className=\"stroke-[2.25]\" />\n        </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACtB,cAAW;wBACX,WAAU;kCAOV,cAAA,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM3B;MApCS;AAsCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 5837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 5872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/course/enhanced-upload-modal.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useCallback } from \"react\"\r\nimport { useDropzone } from \"react-dropzone\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription } from \"@/components/ui/dialog\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Textarea } from \"@/components/ui/textarea\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Progress } from \"@/components/ui/progress\"\r\nimport { fileOperations } from \"@/lib/supabase\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Upload,\r\n  FileText,\r\n  Image as ImageIcon,\r\n  X,\r\n  Loader2,\r\n  BookOpen,\r\n  Target,\r\n  GraduationCap,\r\n  MoreVertical\r\n} from \"lucide-react\"\r\n\r\ninterface FileWithMetadata extends File {\r\n  id: string\r\n  category: string\r\n  description: string\r\n  page_count?: number\r\n  status: 'pending' | 'uploading' | 'completed' | 'error'\r\n  progress: number\r\n  error?: string\r\n  preview?: string\r\n  uploadAsAnonymous?: boolean\r\n  displayName?: string // Custom name for display/upload\r\n}\r\n\r\ninterface EnhancedUploadModalProps {\r\n  courseId: string\r\n  onClose: () => void\r\n  onUploadComplete: () => void\r\n}\r\n\r\nexport function EnhancedUploadModal({ courseId, onClose, onUploadComplete }: EnhancedUploadModalProps) {\r\n  const [files, setFiles] = useState<FileWithMetadata[]>([])\r\n  const [isUploading, setIsUploading] = useState(false)\r\n  const [currentStep, setCurrentStep] = useState<'select' | 'configure'>('select')\r\n\r\n  const categories = [\r\n    { id: 'lecture_notes', label: 'Lecture Notes', icon: BookOpen, description: 'Course lectures and presentations' },\r\n    { id: 'summaries', label: 'Summaries', icon: FileText, description: 'Study summaries and notes' },\r\n    { id: 'practice_materials', label: 'Practice Materials', icon: Target, description: 'Exercises and practice problems' },\r\n    { id: 'exams', label: 'Exams', icon: GraduationCap, description: 'Past exams and test materials' },\r\n    { id: 'others', label: 'Others', icon: MoreVertical, description: 'Other course materials' }\r\n  ]\r\n\r\n  // Accepted file types: PDF, Word documents, Images\r\n  const acceptedFileTypes = {\r\n    'application/pdf': ['.pdf'],\r\n    'application/msword': ['.doc'],\r\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\r\n    'image/jpeg': ['.jpg', '.jpeg'],\r\n    'image/png': ['.png'],\r\n    'image/gif': ['.gif'],\r\n    'image/webp': ['.webp']\r\n  }\r\n\r\n  const maxFileSize = 50 * 1024 * 1024 // 50MB\r\n\r\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\r\n    // Handle rejected files\r\n    if (rejectedFiles.length > 0) {\r\n      console.warn('Some files were rejected:', rejectedFiles)\r\n      // Could show toast notifications here\r\n    }\r\n\r\n    // Convert accepted files to our format\r\n    const newFiles: FileWithMetadata[] = acceptedFiles.map((file) => ({\r\n      ...file,\r\n      id: Math.random().toString(36).substring(7),\r\n      category: '', // No default category - user must select\r\n      description: '',\r\n      page_count: file.type === 'application/pdf' ? undefined : undefined,\r\n      status: 'pending' as const,\r\n      progress: 0,\r\n      preview: file.type && file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,\r\n      uploadAsAnonymous: false,\r\n      displayName: file.name // Use displayName for editable name\r\n    }))\r\n\r\n    setFiles(prev => [...prev, ...newFiles])\r\n\r\n    // Move to configure step when files are added\r\n    if (newFiles.length > 0) {\r\n      setCurrentStep('configure')\r\n    }\r\n  }, [])\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: acceptedFileTypes,\r\n    maxFiles: 10,\r\n    maxSize: maxFileSize,\r\n    multiple: true\r\n  })\r\n\r\n  const updateFileMetadata = (fileId: string, updates: Partial<FileWithMetadata>) => {\r\n    setFiles(prev => prev.map(file => \r\n      file.id === fileId ? { ...file, ...updates } : file\r\n    ))\r\n  }\r\n\r\n  const removeFile = (fileId: string) => {\r\n    setFiles(prev => prev.filter(f => f.id !== fileId))\r\n  }\r\n\r\n  const uploadFiles = async () => {\r\n    if (files.length === 0) return\r\n\r\n    setIsUploading(true)\r\n\r\n    for (const file of files) {\r\n      if (file.status === 'completed') continue\r\n\r\n      try {\r\n        // Update status to uploading\r\n        updateFileMetadata(file.id, { status: 'uploading', progress: 0 })\r\n\r\n        // Simulate progress\r\n        for (let progress = 0; progress <= 90; progress += 10) {\r\n          await new Promise(resolve => setTimeout(resolve, 100))\r\n          updateFileMetadata(file.id, { progress })\r\n        }\r\n\r\n        // Upload to Supabase\r\n        const { error } = await fileOperations.uploadFile(courseId, file, {\r\n          category: file.category,\r\n          description: file.description || undefined,\r\n          page_count: file.page_count || undefined,\r\n          displayName: file.displayName || undefined\r\n        })\r\n\r\n                 if (error) {\r\n           updateFileMetadata(file.id, { \r\n             status: 'error', \r\n             error: (error as any)?.message || 'Upload failed',\r\n             progress: 0\r\n           })\r\n         } else {\r\n           updateFileMetadata(file.id, { status: 'completed', progress: 100 })\r\n         }\r\n       } catch (error) {\r\n         updateFileMetadata(file.id, { \r\n           status: 'error', \r\n           error: 'Upload failed',\r\n           progress: 0\r\n         })\r\n      }\r\n    }\r\n\r\n    setIsUploading(false)\r\n\r\n    // Check if all files uploaded successfully\r\n    const allCompleted = files.every(f => f.status === 'completed')\r\n    if (allCompleted) {\r\n      onUploadComplete()\r\n    }\r\n  }\r\n\r\n  const getFileIcon = (file: FileWithMetadata) => {\r\n    // Add null/undefined check for file.type\r\n    if (!file.type) return <FileText className=\"h-5 w-5 text-gray-500\" />\r\n\r\n    if (file.type.startsWith('image/')) return <ImageIcon className=\"h-5 w-5 text-green-500\" />\r\n    if (file.type.includes('pdf')) return <FileText className=\"h-5 w-5 text-red-500\" />\r\n    if (file.type.includes('word') || file.type.includes('document')) return <FileText className=\"h-5 w-5 text-blue-500\" />\r\n    return <FileText className=\"h-5 w-5 text-gray-500\" />\r\n  }\r\n\r\n\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes'\r\n    const k = 1024\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n  }\r\n\r\n  const canUpload = files.length > 0 && files.every(f => \r\n    f.category && f.category !== '' && f.status !== 'uploading'\r\n  )\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-900 border-0 shadow-2xl\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-white text-xl font-bold\">\r\n            {currentStep === 'select' ? 'Upload Course Materials' : 'Configure Your Files'}\r\n          </DialogTitle>\r\n          <DialogDescription className=\"text-white/80 text-base\">\r\n            {currentStep === 'select'\r\n              ? 'Upload PDF documents, Word files, and images to share with your classmates'\r\n              : 'Set names, categories, and descriptions for your selected files'\r\n            }\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-6\">\r\n          {/* File Selection Step */}\r\n          {currentStep === 'select' && (\r\n            <Card className={cn(\r\n              \"border-2 border-dashed transition-colors bg-white/10 backdrop-blur-sm\",\r\n              isDragActive ? \"border-purple-300 bg-purple-500/20\" : \"border-white/30 hover:border-purple-300\"\r\n            )}>\r\n              <CardContent className=\"p-8\">\r\n                <div {...getRootProps()} className=\"cursor-pointer\">\r\n                  <input {...getInputProps()} />\r\n                  <div className=\"flex flex-col items-center justify-center text-center\">\r\n                    <div className={cn(\r\n                      \"rounded-full p-6 mb-4\",\r\n                      isDragActive ? \"bg-purple-400/30\" : \"bg-white/20\"\r\n                    )}>\r\n                      <Upload className={cn(\r\n                        \"h-12 w-12\",\r\n                        isDragActive ? \"text-purple-200\" : \"text-white\"\r\n                      )} />\r\n                    </div>\r\n\r\n                    <h3 className=\"text-xl font-semibold mb-2 text-white\">\r\n                      {isDragActive ? 'Drop files here' : 'Select Files to Upload'}\r\n                    </h3>\r\n\r\n                    <p className=\"text-white/90 mb-4 max-w-md\">\r\n                      Drag and drop your files here, or click to browse.\r\n                      Supports PDF, Word documents, and images.\r\n                    </p>\r\n\r\n                    <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\r\n                      <Badge variant=\"secondary\" className=\"bg-white/20 text-white border-white/30\">PDF</Badge>\r\n                      <Badge variant=\"secondary\" className=\"bg-white/20 text-white border-white/30\">Word</Badge>\r\n                      <Badge variant=\"secondary\" className=\"bg-white/20 text-white border-white/30\">Images</Badge>\r\n                    </div>\r\n\r\n                    <Button size=\"lg\" className=\"bg-purple-600 hover:bg-purple-700 text-white\">\r\n                      <Upload className=\"h-5 w-5 mr-2\" />\r\n                      Choose Files\r\n                    </Button>\r\n\r\n                    <p className=\"text-white/60 text-sm mt-4\">\r\n                      Max file size: 50 MB • Max 10 files\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n\r\n          {/* File Configuration Step */}\r\n          {currentStep === 'configure' && files.length > 0 && (\r\n            <div className=\"space-y-4\">\r\n              {/* Back to file selection button */}\r\n              <div className=\"flex items-center justify-between\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  onClick={() => setCurrentStep('select')}\r\n                  className=\"text-white hover:bg-white/10\"\r\n                >\r\n                  ← Back to File Selection\r\n                </Button>\r\n                <span className=\"text-white/70 text-sm\">\r\n                  {files.length} file{files.length !== 1 ? 's' : ''} selected\r\n                </span>\r\n              </div>\r\n\r\n              <Card className=\"bg-white/10 backdrop-blur-sm border-white/20\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2 text-white\">\r\n                    <FileText className=\"h-5 w-5\" />\r\n                    Configure Your Files ({files.length})\r\n                  </CardTitle>\r\n                  <CardDescription className=\"text-white/80\">\r\n                    Set names, categories, and descriptions for each file\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"space-y-6\">\r\n                    {files.map((file) => (\r\n                      <div key={file.id} className=\"border border-white/20 rounded-lg p-6 space-y-4 bg-white/5\">\r\n                        {/* File Header */}\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <div className=\"flex-shrink-0\">\r\n                            {file.preview ? (\r\n                              <img\r\n                                src={file.preview}\r\n                                alt={file.name}\r\n                                className=\"h-16 w-16 object-cover rounded-lg\"\r\n                              />\r\n                            ) : (\r\n                              <div className=\"h-16 w-16 rounded-lg bg-white/20 flex items-center justify-center\">\r\n                                {getFileIcon(file)}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n\r\n                          <div className=\"flex-1 min-w-0\">\r\n                            <div className=\"flex items-center justify-between mb-2\">\r\n                              <div>\r\n                                <h4 className=\"font-medium text-white text-lg\">{file.displayName || file.name}</h4>\r\n                                <p className=\"text-white/60 text-sm\">{formatFileSize(file.size)}</p>\r\n                              </div>\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                className=\"h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10\"\r\n                                onClick={() => removeFile(file.id)}\r\n                                disabled={isUploading}\r\n                              >\r\n                                <X className=\"h-4 w-4\" />\r\n                              </Button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {/* File Configuration Form */}\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          {/* File Name */}\r\n                          <div className=\"space-y-2\">\r\n                            <Label className=\"text-white font-medium\">File Name</Label>\r\n                            <Input\r\n                              value={file.displayName || file.name}\r\n                              onChange={(e) => updateFileMetadata(file.id, { displayName: e.target.value })}\r\n                              className=\"bg-white/10 border-white/20 text-white placeholder:text-white/50\"\r\n                              placeholder=\"Enter file name\"\r\n                            />\r\n                          </div>\r\n\r\n                          {/* Category Selection - Improved UI */}\r\n                          <div className=\"space-y-3\">\r\n                            <Label className=\"text-white font-medium\">Category *</Label>\r\n                            <div className=\"grid grid-cols-2 gap-2\">\r\n                              {categories.map((category) => {\r\n                                const Icon = category.icon\r\n                                const isSelected = file.category === category.id\r\n                                return (\r\n                                  <button\r\n                                    key={category.id}\r\n                                    type=\"button\"\r\n                                    onClick={() => updateFileMetadata(file.id, { category: category.id })}\r\n                                    disabled={isUploading}\r\n                                    className={cn(\r\n                                      \"flex items-center gap-3 p-3 rounded-lg border text-left transition-all\",\r\n                                      isSelected\r\n                                        ? \"border-purple-400 bg-purple-500/20 text-white\"\r\n                                        : \"border-white/20 bg-white/5 text-white/80 hover:border-purple-300 hover:bg-white/10\"\r\n                                    )}\r\n                                  >\r\n                                    <Icon className=\"h-5 w-5 flex-shrink-0\" />\r\n                                    <div className=\"min-w-0\">\r\n                                      <div className=\"font-medium text-sm\">{category.label}</div>\r\n                                      <div className=\"text-xs opacity-70 truncate\">{category.description}</div>\r\n                                    </div>\r\n                                  </button>\r\n                                )\r\n                              })}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {/* Description and Additional Options */}\r\n                        <div className=\"space-y-4\">\r\n                          {/* Description */}\r\n                          <div className=\"space-y-2\">\r\n                            <Label className=\"text-white font-medium\">Description</Label>\r\n                            <Textarea\r\n                              value={file.description}\r\n                              onChange={(e) => updateFileMetadata(file.id, { description: e.target.value })}\r\n                              className=\"bg-white/10 border-white/20 text-white placeholder:text-white/50 min-h-[80px]\"\r\n                              placeholder=\"Optional description of the file content...\"\r\n                              disabled={isUploading}\r\n                            />\r\n                          </div>\r\n\r\n                          {/* Page Count for PDFs */}\r\n                          {file.type === 'application/pdf' && (\r\n                            <div className=\"space-y-2\">\r\n                              <Label className=\"text-white font-medium\">Page Count</Label>\r\n                              <Input\r\n                                type=\"number\"\r\n                                placeholder=\"Optional\"\r\n                                value={file.page_count || ''}\r\n                                onChange={(e) => updateFileMetadata(file.id, {\r\n                                  page_count: e.target.value ? parseInt(e.target.value) : undefined\r\n                                })}\r\n                                className=\"bg-white/10 border-white/20 text-white placeholder:text-white/50 w-32\"\r\n                                disabled={isUploading}\r\n                              />\r\n                            </div>\r\n                          )}\r\n\r\n                          {/* Anonymous Upload Toggle */}\r\n                          <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-white/5 border border-white/20\">\r\n                            <input\r\n                              type=\"checkbox\"\r\n                              id={`anonymous-${file.id}`}\r\n                              checked={file.uploadAsAnonymous || false}\r\n                              onChange={(e) => updateFileMetadata(file.id, { uploadAsAnonymous: e.target.checked })}\r\n                              disabled={isUploading}\r\n                              className=\"h-4 w-4 rounded border-white/30 bg-white/10 text-purple-500 focus:ring-purple-400\"\r\n                            />\r\n                            <Label htmlFor={`anonymous-${file.id}`} className=\"text-white font-medium cursor-pointer\">\r\n                              Upload anonymously\r\n                            </Label>\r\n                            <span className=\"text-white/60 text-sm\">\r\n                              (Hide your name from other students)\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Upload Button */}\r\n                <div className=\"flex justify-between items-center mt-6 pt-4 border-t border-white/20\">\r\n                  <div className=\"text-sm text-white/80\">\r\n                    {files.filter(f => f.status === 'completed').length} of {files.length} files uploaded\r\n                  </div>\r\n\r\n                  <div className=\"flex gap-3\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      onClick={onClose}\r\n                      disabled={isUploading}\r\n                      className=\"border-white/30 text-white hover:bg-white/10\"\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                    <Button\r\n                      onClick={uploadFiles}\r\n                      disabled={!canUpload || isUploading}\r\n                      className=\"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0\"\r\n                    >\r\n                      {isUploading ? (\r\n                        <>\r\n                          <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                          Uploading...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <Upload className=\"h-4 w-4 mr-2\" />\r\n                          Upload {files.length} File{files.length !== 1 ? 's' : ''}\r\n                        </>\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;;AA6CO,SAAS,oBAAoB,KAAiE;QAAjE,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAA4B,GAAjE;;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAEvE,MAAM,aAAa;QACjB;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAAoC;QAChH;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAA4B;QAChG;YAAE,IAAI;YAAsB,OAAO;YAAsB,MAAM,yMAAA,CAAA,SAAM;YAAE,aAAa;QAAkC;QACtH;YAAE,IAAI;YAAS,OAAO;YAAS,MAAM,2NAAA,CAAA,gBAAa;YAAE,aAAa;QAAgC;QACjG;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,6NAAA,CAAA,eAAY;YAAE,aAAa;QAAyB;KAC5F;IAED,mDAAmD;IACnD,MAAM,oBAAoB;QACxB,mBAAmB;YAAC;SAAO;QAC3B,sBAAsB;YAAC;SAAO;QAC9B,2EAA2E;YAAC;SAAQ;QACpF,cAAc;YAAC;YAAQ;SAAQ;QAC/B,aAAa;YAAC;SAAO;QACrB,aAAa;YAAC;SAAO;QACrB,cAAc;YAAC;SAAQ;IACzB;IAEA,MAAM,cAAc,KAAK,OAAO,KAAK,OAAO;;IAE5C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,eAAuB;YACjD,wBAAwB;YACxB,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,QAAQ,IAAI,CAAC,6BAA6B;YAC1C,sCAAsC;YACxC;YAEA,uCAAuC;YACvC,MAAM,WAA+B,cAAc,GAAG;oEAAC,CAAC,OAAS,CAAC;wBAChE,GAAG,IAAI;wBACP,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;wBACzC,UAAU;wBACV,aAAa;wBACb,YAAY,KAAK,IAAI,KAAK,oBAAoB,YAAY;wBAC1D,QAAQ;wBACR,UAAU;wBACV,SAAS,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,eAAe,CAAC,QAAQ;wBACnF,mBAAmB;wBACnB,aAAa,KAAK,IAAI,CAAC,oCAAoC;oBAC7D,CAAC;;YAED;2DAAS,CAAA,OAAQ;2BAAI;2BAAS;qBAAS;;YAEvC,8CAA8C;YAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,eAAe;YACjB;QACF;kDAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAEnD;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C;IAEA,MAAM,cAAc;QAClB,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,eAAe;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,MAAM,KAAK,aAAa;YAEjC,IAAI;gBACF,6BAA6B;gBAC7B,mBAAmB,KAAK,EAAE,EAAE;oBAAE,QAAQ;oBAAa,UAAU;gBAAE;gBAE/D,oBAAoB;gBACpB,IAAK,IAAI,WAAW,GAAG,YAAY,IAAI,YAAY,GAAI;oBACrD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD,mBAAmB,KAAK,EAAE,EAAE;wBAAE;oBAAS;gBACzC;gBAEA,qBAAqB;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,UAAU,MAAM;oBAChE,UAAU,KAAK,QAAQ;oBACvB,aAAa,KAAK,WAAW,IAAI;oBACjC,YAAY,KAAK,UAAU,IAAI;oBAC/B,aAAa,KAAK,WAAW,IAAI;gBACnC;gBAES,IAAI,OAAO;wBAGR;oBAFT,mBAAmB,KAAK,EAAE,EAAE;wBAC1B,QAAQ;wBACR,OAAO,EAAA,QAAC,mBAAD,4BAAA,MAAgB,OAAO,KAAI;wBAClC,UAAU;oBACZ;gBACF,OAAO;oBACL,mBAAmB,KAAK,EAAE,EAAE;wBAAE,QAAQ;wBAAa,UAAU;oBAAI;gBACnE;YACF,EAAE,OAAO,OAAO;gBACd,mBAAmB,KAAK,EAAE,EAAE;oBAC1B,QAAQ;oBACR,OAAO;oBACP,UAAU;gBACZ;YACH;QACF;QAEA,eAAe;QAEf,2CAA2C;QAC3C,MAAM,eAAe,MAAM,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACnD,IAAI,cAAc;YAChB;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,yCAAyC;QACzC,IAAI,CAAC,KAAK,IAAI,EAAE,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAE3C,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;QAChE,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1D,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC7F,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAIA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,YAAY,MAAM,MAAM,GAAG,KAAK,MAAM,KAAK,CAAC,CAAA,IAChD,EAAE,QAAQ,IAAI,EAAE,QAAQ,KAAK,MAAM,EAAE,MAAM,KAAK;IAGlD,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,gBAAgB,WAAW,4BAA4B;;;;;;sCAE1D,6LAAC,qIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC1B,gBAAgB,WACb,+EACA;;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,0BACf,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,yEACA,eAAe,uCAAuC;sCAEtD,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAK,GAAG,cAAc;oCAAE,WAAU;;sDACjC,6LAAC;4CAAO,GAAG,eAAe;;;;;;sDAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,yBACA,eAAe,qBAAqB;8DAEpC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,aACA,eAAe,oBAAoB;;;;;;;;;;;8DAIvC,6LAAC;oDAAG,WAAU;8DACX,eAAe,oBAAoB;;;;;;8DAGtC,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;8DAK3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAyC;;;;;;sEAC9E,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAyC;;;;;;sEAC9E,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAyC;;;;;;;;;;;;8DAGhF,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;;sEAC1B,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAIrC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,gBAAgB,eAAe,MAAM,MAAM,GAAG,mBAC7C,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAK,WAAU;;gDACb,MAAM,MAAM;gDAAC;gDAAM,MAAM,MAAM,KAAK,IAAI,MAAM;gDAAG;;;;;;;;;;;;;8CAItD,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;wDACT,MAAM,MAAM;wDAAC;;;;;;;8DAEtC,6LAAC,mIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAgB;;;;;;;;;;;;sDAI7C,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4DAAkB,WAAU;;8EAE3B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,KAAK,OAAO,iBACX,6LAAC;gFACC,KAAK,KAAK,OAAO;gFACjB,KAAK,KAAK,IAAI;gFACd,WAAU;;;;;qGAGZ,6LAAC;gFAAI,WAAU;0FACZ,YAAY;;;;;;;;;;;sFAKnB,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;;0GACC,6LAAC;gGAAG,WAAU;0GAAkC,KAAK,WAAW,IAAI,KAAK,IAAI;;;;;;0GAC7E,6LAAC;gGAAE,WAAU;0GAAyB,eAAe,KAAK,IAAI;;;;;;;;;;;;kGAEhE,6LAAC,qIAAA,CAAA,SAAM;wFACL,SAAQ;wFACR,MAAK;wFACL,WAAU;wFACV,SAAS,IAAM,WAAW,KAAK,EAAE;wFACjC,UAAU;kGAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAOrB,6LAAC;oEAAI,WAAU;;sFAEb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAyB;;;;;;8FAC1C,6LAAC,oIAAA,CAAA,QAAK;oFACJ,OAAO,KAAK,WAAW,IAAI,KAAK,IAAI;oFACpC,UAAU,CAAC,IAAM,mBAAmB,KAAK,EAAE,EAAE;4FAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wFAAC;oFAC3E,WAAU;oFACV,aAAY;;;;;;;;;;;;sFAKhB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAyB;;;;;;8FAC1C,6LAAC;oFAAI,WAAU;8FACZ,WAAW,GAAG,CAAC,CAAC;wFACf,MAAM,OAAO,SAAS,IAAI;wFAC1B,MAAM,aAAa,KAAK,QAAQ,KAAK,SAAS,EAAE;wFAChD,qBACE,6LAAC;4FAEC,MAAK;4FACL,SAAS,IAAM,mBAAmB,KAAK,EAAE,EAAE;oGAAE,UAAU,SAAS,EAAE;gGAAC;4FACnE,UAAU;4FACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,aACI,kDACA;;8GAGN,6LAAC;oGAAK,WAAU;;;;;;8GAChB,6LAAC;oGAAI,WAAU;;sHACb,6LAAC;4GAAI,WAAU;sHAAuB,SAAS,KAAK;;;;;;sHACpD,6LAAC;4GAAI,WAAU;sHAA+B,SAAS,WAAW;;;;;;;;;;;;;2FAd/D,SAAS,EAAE;;;;;oFAkBtB;;;;;;;;;;;;;;;;;;8EAMN,6LAAC;oEAAI,WAAU;;sFAEb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAyB;;;;;;8FAC1C,6LAAC,uIAAA,CAAA,WAAQ;oFACP,OAAO,KAAK,WAAW;oFACvB,UAAU,CAAC,IAAM,mBAAmB,KAAK,EAAE,EAAE;4FAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wFAAC;oFAC3E,WAAU;oFACV,aAAY;oFACZ,UAAU;;;;;;;;;;;;wEAKb,KAAK,IAAI,KAAK,mCACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAyB;;;;;;8FAC1C,6LAAC,oIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,aAAY;oFACZ,OAAO,KAAK,UAAU,IAAI;oFAC1B,UAAU,CAAC,IAAM,mBAAmB,KAAK,EAAE,EAAE;4FAC3C,YAAY,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;wFAC1D;oFACA,WAAU;oFACV,UAAU;;;;;;;;;;;;sFAMhB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFACC,MAAK;oFACL,IAAI,AAAC,aAAoB,OAAR,KAAK,EAAE;oFACxB,SAAS,KAAK,iBAAiB,IAAI;oFACnC,UAAU,CAAC,IAAM,mBAAmB,KAAK,EAAE,EAAE;4FAAE,mBAAmB,EAAE,MAAM,CAAC,OAAO;wFAAC;oFACnF,UAAU;oFACV,WAAU;;;;;;8FAEZ,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAS,AAAC,aAAoB,OAAR,KAAK,EAAE;oFAAI,WAAU;8FAAwC;;;;;;8FAG1F,6LAAC;oFAAK,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;;2DA7HpC,KAAK,EAAE;;;;;;;;;;8DAuIvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gEAAC;gEAAK,MAAM,MAAM;gEAAC;;;;;;;sEAGxE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS;oEACT,UAAU;oEACV,WAAU;8EACX;;;;;;8EAGD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,UAAU,CAAC,aAAa;oEACxB,WAAU;8EAET,4BACC;;0FACE,6LAAC,oNAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAA8B;;qGAInD;;0FACE,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;4EAC3B,MAAM,MAAM;4EAAC;4EAAM,MAAM,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchF;GAragB;;QAuDwC,2KAAA,CAAA,cAAW;;;KAvDnD", "debugId": null}}, {"offset": {"line": 6829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/course/file-preview-modal.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { \r\n  Heart, Download, Plus, Share2, \r\n  FileText, BookOpen, Target, GraduationCap, \r\n  MoreVertical, Calendar, User, Eye,\r\n  Image as ImageIcon, X\r\n} from \"lucide-react\"\r\n\r\ninterface FileRecord {\r\n  id: string\r\n  name: string\r\n  type: string\r\n  size: number\r\n  category: string\r\n  description?: string\r\n  page_count?: number\r\n  like_count: number\r\n  download_count: number\r\n  user_liked: boolean\r\n  created_at: string\r\n  uploader: {\r\n    full_name: string | null\r\n    email: string\r\n  }\r\n  storage_path: string\r\n}\r\n\r\ninterface FilePreviewModalProps {\r\n  file: FileRecord\r\n  onClose: () => void\r\n  onAddToLearning: (fileId: string) => void\r\n  onLike: (fileId: string) => void\r\n  onDownload: (fileId: string) => void\r\n  isEnrolled: boolean\r\n}\r\n\r\nexport function FilePreviewModal({ \r\n  file, \r\n  onClose, \r\n  onAddToLearning, \r\n  onLike, \r\n  onDownload, \r\n  isEnrolled \r\n}: FilePreviewModalProps) {\r\n  const [isLiking, setIsLiking] = useState(false)\r\n  const [isDownloading, setIsDownloading] = useState(false)\r\n  const [isAddingToLearning, setIsAddingToLearning] = useState(false)\r\n\r\n  const getCategoryIcon = (category: string) => {\r\n    switch (category) {\r\n      case 'lecture_notes': return <BookOpen className=\"h-4 w-4\" />\r\n      case 'summaries': return <FileText className=\"h-4 w-4\" />\r\n      case 'practice_materials': return <Target className=\"h-4 w-4\" />\r\n      case 'exams': return <GraduationCap className=\"h-4 w-4\" />\r\n      default: return <MoreVertical className=\"h-4 w-4\" />\r\n    }\r\n  }\r\n\r\n  const getCategoryColor = (category: string) => {\r\n    const colors = {\r\n      lecture_notes: 'bg-blue-500/10 text-blue-400 border-blue-400/20',\r\n      summaries: 'bg-green-500/10 text-green-400 border-green-400/20',\r\n      practice_materials: 'bg-purple-500/10 text-purple-400 border-purple-400/20',\r\n      exams: 'bg-red-500/10 text-red-400 border-red-400/20',\r\n      others: 'bg-gray-500/10 text-gray-400 border-gray-400/20'\r\n    }\r\n    return colors[category as keyof typeof colors] || colors.others\r\n  }\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes'\r\n    const k = 1024\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n  }\r\n\r\n  const getFileTypeIcon = (type: string) => {\r\n    if (type.includes('pdf')) return <FileText className=\"h-8 w-8 text-red-400\" />\r\n    if (type.includes('word') || type.includes('document')) return <FileText className=\"h-8 w-8 text-blue-400\" />\r\n    if (type.includes('image')) return <ImageIcon className=\"h-8 w-8 text-green-400\" />\r\n    return <FileText className=\"h-8 w-8 text-gray-400\" />\r\n  }\r\n\r\n  const getDisplayName = (uploader: FileRecord['uploader']) => {\r\n    return uploader.full_name || uploader.email.split('@')[0] || 'Anonymous'\r\n  }\r\n\r\n  const handleLike = async () => {\r\n    setIsLiking(true)\r\n    try {\r\n      await onLike(file.id)\r\n    } finally {\r\n      setIsLiking(false)\r\n    }\r\n  }\r\n\r\n  const handleDownload = async () => {\r\n    setIsDownloading(true)\r\n    try {\r\n      await onDownload(file.id)\r\n    } finally {\r\n      setIsDownloading(false)\r\n    }\r\n  }\r\n\r\n  const handleAddToLearning = async () => {\r\n    setIsAddingToLearning(true)\r\n    try {\r\n      await onAddToLearning(file.id)\r\n    } finally {\r\n      setIsAddingToLearning(false)\r\n    }\r\n  }\r\n\r\n  const renderFilePreview = () => {\r\n    // For now, we'll show a placeholder\r\n    // In the future, we can implement actual file previews for PDFs, images, etc.\r\n    return (\r\n      <div className=\"h-full bg-gray-50 dark:bg-gray-900 rounded-lg flex flex-col items-center justify-center p-8\">\r\n        {getFileTypeIcon(file.type)}\r\n        <h3 className=\"text-lg font-semibold mt-4 mb-2 text-center\">{file.name}</h3>\r\n        <p className=\"text-sm text-gray-500 text-center mb-4\">\r\n          {file.type.split('/')[1]?.toUpperCase() || 'FILE'} • {formatFileSize(file.size)}\r\n        </p>\r\n        \r\n        {/* Preview placeholder text */}\r\n        <div className=\"text-center text-gray-400 space-y-2\">\r\n          <Eye className=\"h-6 w-6 mx-auto\" />\r\n          <p className=\"text-sm\">File preview will be available soon</p>\r\n          <p className=\"text-xs\">Support for PDF, Word, and Image previews coming</p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-6xl h-[85vh] p-0\">\r\n        <div className=\"flex h-full\">\r\n          {/* Preview Area */}\r\n          <div className=\"flex-1 p-6\">\r\n            {renderFilePreview()}\r\n          </div>\r\n          \r\n          {/* Sidebar with file details and actions */}\r\n          <div className=\"w-80 border-l bg-gray-50 dark:bg-gray-900 p-6 space-y-6 overflow-y-auto\">\r\n            {/* Header */}\r\n            <div className=\"flex items-start justify-between\">\r\n              <div className=\"flex-1\">\r\n                <h2 className=\"font-semibold text-lg line-clamp-2 mb-2\">{file.name}</h2>\r\n                <Badge variant=\"outline\" className={getCategoryColor(file.category)}>\r\n                  {getCategoryIcon(file.category)}\r\n                  <span className=\"ml-1 capitalize\">{file.category.replace('_', ' ')}</span>\r\n                </Badge>\r\n              </div>\r\n              <Button \r\n                variant=\"ghost\" \r\n                size=\"sm\" \r\n                onClick={onClose}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Description */}\r\n            {file.description && (\r\n              <div>\r\n                <h4 className=\"font-medium mb-2 text-sm\">Description</h4>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">{file.description}</p>\r\n              </div>\r\n            )}\r\n\r\n            <Separator />\r\n\r\n            {/* File Details */}\r\n            <div className=\"space-y-3\">\r\n              <h4 className=\"font-medium text-sm\">File Details</h4>\r\n              <div className=\"space-y-2 text-sm\">\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-500\">Type:</span>\r\n                  <span className=\"font-medium\">{file.type.split('/')[1]?.toUpperCase() || 'Unknown'}</span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-500\">Size:</span>\r\n                  <span className=\"font-medium\">{formatFileSize(file.size)}</span>\r\n                </div>\r\n                {file.page_count && (\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-gray-500\">Pages:</span>\r\n                    <span className=\"font-medium\">{file.page_count}</span>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-500\">Uploaded:</span>\r\n                  <span className=\"font-medium\">{new Date(file.created_at).toLocaleDateString()}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Uploader Info */}\r\n            <div className=\"space-y-3\">\r\n              <h4 className=\"font-medium text-sm\">Uploaded By</h4>\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\r\n                  <User className=\"h-4 w-4 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <p className=\"font-medium text-sm\">{getDisplayName(file.uploader)}</p>\r\n                  <p className=\"text-xs text-gray-500\">\r\n                    <Calendar className=\"h-3 w-3 inline mr-1\" />\r\n                    {new Date(file.created_at).toLocaleDateString()}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Stats */}\r\n            <div className=\"space-y-3\">\r\n              <h4 className=\"font-medium text-sm\">Statistics</h4>\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"text-center p-3 bg-white dark:bg-gray-800 rounded-lg\">\r\n                  <div className=\"text-lg font-bold text-red-500\">{file.like_count}</div>\r\n                  <div className=\"text-xs text-gray-500\">Likes</div>\r\n                </div>\r\n                <div className=\"text-center p-3 bg-white dark:bg-gray-800 rounded-lg\">\r\n                  <div className=\"text-lg font-bold text-blue-500\">{file.download_count || 0}</div>\r\n                  <div className=\"text-xs text-gray-500\">Downloads</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            {isEnrolled && (\r\n              <>\r\n                <Separator />\r\n                <div className=\"space-y-3\">\r\n                  <h4 className=\"font-medium text-sm\">Actions</h4>\r\n                  <div className=\"space-y-2\">\r\n                    <Button \r\n                      onClick={handleLike}\r\n                      disabled={isLiking}\r\n                      variant={file.user_liked ? \"default\" : \"outline\"}\r\n                      className={cn(\r\n                        \"w-full\",\r\n                        file.user_liked && \"bg-red-500 hover:bg-red-600 text-white\"\r\n                      )}\r\n                    >\r\n                      <Heart className={cn(\"w-4 h-4 mr-2\", file.user_liked && \"fill-current\")} />\r\n                      {isLiking ? 'Loading...' : file.user_liked ? 'Unlike' : 'Like'}\r\n                    </Button>\r\n                    \r\n                    <Button \r\n                      onClick={handleAddToLearning}\r\n                      disabled={isAddingToLearning}\r\n                      variant=\"outline\"\r\n                      className=\"w-full\"\r\n                    >\r\n                      <Plus className=\"w-4 h-4 mr-2\" />\r\n                      {isAddingToLearning ? 'Adding...' : 'Add to My Learning'}\r\n                    </Button>\r\n                    \r\n                    <Button \r\n                      onClick={handleDownload}\r\n                      disabled={isDownloading}\r\n                      variant=\"outline\"\r\n                      className=\"w-full\"\r\n                    >\r\n                      <Download className=\"w-4 h-4 mr-2\" />\r\n                      {isDownloading ? 'Downloading...' : 'Download'}\r\n                    </Button>\r\n                    \r\n                    <Button \r\n                      variant=\"outline\"\r\n                      className=\"w-full\"\r\n                      onClick={() => {\r\n                        // Copy file URL to clipboard\r\n                        navigator.clipboard.writeText(window.location.href)\r\n                      }}\r\n                    >\r\n                      <Share2 className=\"w-4 h-4 mr-2\" />\r\n                      Share\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n\r\n            {/* Non-enrolled user message */}\r\n            {!isEnrolled && (\r\n              <>\r\n                <Separator />\r\n                <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\r\n                  <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\r\n                    Enroll in this course to like, download, and add files to your learning path.\r\n                  </p>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AA2CO,SAAS,iBAAiB,KAOT;QAPS,EAC/B,IAAI,EACJ,OAAO,EACP,eAAe,EACf,MAAM,EACN,UAAU,EACV,UAAU,EACY,GAPS;QAkJgB;;IA1I/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAiB,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YACjD,KAAK;gBAAa,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAsB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,6LAAC,6NAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;QAC1C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,eAAe;YACf,WAAW;YACX,oBAAoB;YACpB,OAAO;YACP,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,OAAO,MAAM;IACjE;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,QAAQ,CAAC,QAAQ,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QACrD,IAAI,KAAK,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,aAAa,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QACnF,IAAI,KAAK,QAAQ,CAAC,UAAU,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;QACxD,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,SAAS,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;IAC/D;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,MAAM,OAAO,KAAK,EAAE;QACtB,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;QACjB,IAAI;YACF,MAAM,WAAW,KAAK,EAAE;QAC1B,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,sBAAsB;QAC1B,sBAAsB;QACtB,IAAI;YACF,MAAM,gBAAgB,KAAK,EAAE;QAC/B,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,oBAAoB;YAQjB;QAPP,oCAAoC;QACpC,8EAA8E;QAC9E,qBACE,6LAAC;YAAI,WAAU;;gBACZ,gBAAgB,KAAK,IAAI;8BAC1B,6LAAC;oBAAG,WAAU;8BAA+C,KAAK,IAAI;;;;;;8BACtE,6LAAC;oBAAE,WAAU;;wBACV,EAAA,oBAAA,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,cAAvB,wCAAA,kBAAyB,WAAW,OAAM;wBAAO;wBAAI,eAAe,KAAK,IAAI;;;;;;;8BAIhF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,6LAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;;;;;;;;IAI/B;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;sBACvB,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C,KAAK,IAAI;;;;;;0DAClE,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAW,iBAAiB,KAAK,QAAQ;;oDAC/D,gBAAgB,KAAK,QAAQ;kEAC9B,6LAAC;wDAAK,WAAU;kEAAmB,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;kDAGlE,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAKhB,KAAK,WAAW,kBACf,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAE,WAAU;kDAA4C,KAAK,WAAW;;;;;;;;;;;;0CAI7E,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAe,EAAA,oBAAA,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,cAAvB,wCAAA,kBAAyB,WAAW,OAAM;;;;;;;;;;;;0DAE3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAe,eAAe,KAAK,IAAI;;;;;;;;;;;;4CAExD,KAAK,UAAU,kBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAe,KAAK,UAAU;;;;;;;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAe,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;0CAKjF,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAuB,eAAe,KAAK,QAAQ;;;;;;kEAChE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0CAMrD,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAkC,KAAK,UAAU;;;;;;kEAChE,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAmC,KAAK,cAAc,IAAI;;;;;;kEACzE,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;4BAM5C,4BACC;;kDACE,6LAAC,wIAAA,CAAA,YAAS;;;;;kDACV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU;wDACV,SAAS,KAAK,UAAU,GAAG,YAAY;wDACvC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,UACA,KAAK,UAAU,IAAI;;0EAGrB,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,KAAK,UAAU,IAAI;;;;;;4DACvD,WAAW,eAAe,KAAK,UAAU,GAAG,WAAW;;;;;;;kEAG1D,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU;wDACV,SAAQ;wDACR,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,qBAAqB,cAAc;;;;;;;kEAGtC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU;wDACV,SAAQ;wDACR,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,gBAAgB,mBAAmB;;;;;;;kEAGtC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;4DACP,6BAA6B;4DAC7B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;wDACpD;;0EAEA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;4BAS5C,CAAC,4BACA;;kDACE,6LAAC,wIAAA,CAAA,YAAS;;;;;kDACV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9E;GAjRgB;KAAA", "debugId": null}}, {"offset": {"line": 7632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/course/lecture-notes-tab.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useEffect } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from \"@/components/ui/dialog\"\r\nimport { fileOperations } from \"@/lib/supabase\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { \r\n  Heart, Download, Eye, Plus, Upload, \r\n  FileText, BookOpen, Target, GraduationCap, \r\n  MoreVertical, Star, Image as ImageIcon,\r\n  Loader2, FolderOpen\r\n} from \"lucide-react\"\r\n\r\ninterface LectureNotesTabProps {\r\n  courseId: string\r\n  user: any\r\n  isEnrolled: boolean\r\n}\r\n\r\ninterface FileRecord {\r\n  id: string\r\n  name: string\r\n  type: string\r\n  size: number\r\n  category: string\r\n  description?: string\r\n  page_count?: number\r\n  like_count: number\r\n  download_count: number\r\n  user_liked: boolean\r\n  created_at: string\r\n  uploader: {\r\n    full_name: string | null\r\n    email: string\r\n  }\r\n  storage_path: string\r\n}\r\n\r\nexport function LectureNotesTab({ courseId, user, isEnrolled }: LectureNotesTabProps) {\r\n  const [files, setFiles] = useState<FileRecord[]>([])\r\n  const [activeFilter, setActiveFilter] = useState('all')\r\n  const [selectedFile, setSelectedFile] = useState<FileRecord | null>(null)\r\n  const [showUpload, setShowUpload] = useState(false)\r\n  const [loading, setLoading] = useState(true)\r\n\r\n  const categories = [\r\n    { id: 'all', label: 'All Files', icon: FolderOpen, color: 'text-white/80' },\r\n    { id: 'top_rated', label: 'Top Rated', icon: Star, color: 'text-yellow-400' },\r\n    { id: 'lecture_notes', label: 'Lecture Notes', icon: BookOpen, color: 'text-blue-400' },\r\n    { id: 'summaries', label: 'Summaries', icon: FileText, color: 'text-green-400' },\r\n    { id: 'practice_materials', label: 'Practice Materials', icon: Target, color: 'text-purple-400' },\r\n    { id: 'exams', label: 'Exams', icon: GraduationCap, color: 'text-red-400' },\r\n    { id: 'others', label: 'Others', icon: MoreVertical, color: 'text-gray-400' }\r\n  ]\r\n\r\n  useEffect(() => {\r\n    loadFiles()\r\n  }, [courseId, activeFilter])\r\n\r\n  const loadFiles = async () => {\r\n    setLoading(true)\r\n    try {\r\n      const { data, error } = await fileOperations.getCourseFiles(courseId, activeFilter)\r\n      if (!error && data) {\r\n        setFiles(data)\r\n      } else {\r\n        console.error('Error loading files:', error)\r\n        setFiles([])\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading files:', error)\r\n      setFiles([])\r\n    }\r\n    setLoading(false)\r\n  }\r\n\r\n  const handleLike = async (fileId: string) => {\r\n    try {\r\n      const { error } = await fileOperations.toggleFileLike(fileId)\r\n      if (!error) {\r\n        await loadFiles() // Refresh to update like counts\r\n      }\r\n    } catch (error) {\r\n      console.error('Error toggling like:', error)\r\n    }\r\n  }\r\n\r\n  const handleAddToLearning = async (fileId: string) => {\r\n    try {\r\n      const { error } = await fileOperations.addToLearning(fileId)\r\n      if (!error) {\r\n        // Show success message or update UI\r\n        console.log('Added to learning path successfully')\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding to learning:', error)\r\n    }\r\n  }\r\n\r\n  const handleDownload = async (fileId: string) => {\r\n    try {\r\n      const { data, error } = await fileOperations.downloadFile(fileId)\r\n      if (!error && data) {\r\n        // Create download link\r\n        const link = document.createElement('a')\r\n        link.href = data.url\r\n        link.download = data.fileName\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        \r\n        // Refresh files to update download count\r\n        await loadFiles()\r\n      }\r\n    } catch (error) {\r\n      console.error('Error downloading file:', error)\r\n    }\r\n  }\r\n\r\n  const getCategoryIcon = (category: string) => {\r\n    const categoryData = categories.find(c => c.id === category)\r\n    const Icon = categoryData?.icon || FileText\r\n    return <Icon className=\"h-4 w-4\" />\r\n  }\r\n\r\n  const getCategoryColor = (category: string) => {\r\n    const colors = {\r\n      lecture_notes: 'bg-blue-500/10 text-blue-400 border-blue-400/20',\r\n      summaries: 'bg-green-500/10 text-green-400 border-green-400/20',\r\n      practice_materials: 'bg-purple-500/10 text-purple-400 border-purple-400/20',\r\n      exams: 'bg-red-500/10 text-red-400 border-red-400/20',\r\n      others: 'bg-gray-500/10 text-gray-400 border-gray-400/20'\r\n    }\r\n    return colors[category as keyof typeof colors] || colors.others\r\n  }\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes'\r\n    const k = 1024\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n  }\r\n\r\n  const getFileTypeIcon = (type: string) => {\r\n    if (type.includes('pdf')) return <FileText className=\"h-6 w-6 text-red-400\" />\r\n    if (type.includes('word') || type.includes('document')) return <FileText className=\"h-6 w-6 text-blue-400\" />\r\n    if (type.includes('image')) return <ImageIcon className=\"h-6 w-6 text-green-400\" />\r\n    return <FileText className=\"h-6 w-6 text-gray-400\" />\r\n  }\r\n\r\n  const getDisplayName = (uploader: FileRecord['uploader']) => {\r\n    return uploader.full_name || uploader.email.split('@')[0] || 'Anonymous'\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-12\">\r\n        <Loader2 className=\"h-8 w-8 animate-spin text-purple-400\" />\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-xl font-bold text-white\">Lecture Notes & Materials</h3>\r\n          <p className=\"text-white/70 text-sm\">Browse and share course materials (PDF, Word, Images)</p>\r\n        </div>\r\n        {isEnrolled && (\r\n          <Button \r\n            onClick={() => setShowUpload(true)}\r\n            className=\"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white\"\r\n          >\r\n            <Upload className=\"w-4 h-4 mr-2\" />\r\n            Upload Material\r\n          </Button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Category Filters */}\r\n      <div className=\"flex gap-2 overflow-x-auto pb-2\">\r\n        {categories.map((category) => {\r\n          const isActive = activeFilter === category.id\r\n          return (\r\n            <Button\r\n              key={category.id}\r\n              variant={isActive ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setActiveFilter(category.id)}\r\n              className={cn(\r\n                \"whitespace-nowrap transition-all duration-200\",\r\n                isActive\r\n                  ? \"bg-purple-500 text-white scale-105 shadow-lg\"\r\n                  : \"border-white/20 text-white/80 hover:bg-white/10 hover:scale-105\"\r\n              )}\r\n            >\r\n              <category.icon className={cn(\"h-3 w-3 mr-1\", category.color)} />\r\n              {category.label}\r\n            </Button>\r\n          )\r\n        })}\r\n      </div>\r\n\r\n      {/* Files Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\r\n        {files.map((file) => (\r\n          <Card \r\n            key={file.id} \r\n            className=\"bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-200 group cursor-pointer hover:scale-[1.02]\"\r\n            onClick={() => setSelectedFile(file)}\r\n          >\r\n            <CardContent className=\"p-4\">\r\n              {/* File Preview */}\r\n              <div className=\"aspect-[4/3] bg-white/10 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden\">\r\n                <div className=\"text-white/60 flex flex-col items-center gap-2\">\r\n                  {getFileTypeIcon(file.type)}\r\n                  <span className=\"text-xs text-white/40 uppercase font-medium\">\r\n                    {file.type.split('/')[1] || 'File'}\r\n                  </span>\r\n                </div>\r\n                \r\n                {/* Overlay with actions */}\r\n                <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2\">\r\n                  <Button \r\n                    size=\"sm\" \r\n                    variant=\"outline\" \r\n                    className=\"border-white/40 text-white hover:bg-white/20\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation()\r\n                      setSelectedFile(file)\r\n                    }}\r\n                  >\r\n                    <Eye className=\"w-4 h-4\" />\r\n                  </Button>\r\n                  {isEnrolled && (\r\n                    <>\r\n                      <Button \r\n                        size=\"sm\" \r\n                        variant=\"outline\" \r\n                        className=\"border-white/40 text-white hover:bg-white/20\"\r\n                        onClick={(e) => {\r\n                          e.stopPropagation()\r\n                          handleAddToLearning(file.id)\r\n                        }}\r\n                      >\r\n                        <Plus className=\"w-4 h-4\" />\r\n                      </Button>\r\n                      <Button \r\n                        size=\"sm\" \r\n                        variant=\"outline\" \r\n                        className=\"border-white/40 text-white hover:bg-white/20\"\r\n                        onClick={(e) => {\r\n                          e.stopPropagation()\r\n                          handleDownload(file.id)\r\n                        }}\r\n                      >\r\n                        <Download className=\"w-4 h-4\" />\r\n                      </Button>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* File Info */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <h4 className=\"text-white font-medium text-sm line-clamp-2 flex-1\">\r\n                    {file.name}\r\n                  </h4>\r\n                </div>\r\n\r\n                {file.description && (\r\n                  <p className=\"text-white/70 text-xs line-clamp-2\">\r\n                    {file.description}\r\n                  </p>\r\n                )}\r\n\r\n                {/* Metadata */}\r\n                <div className=\"flex items-center gap-2 text-xs\">\r\n                  <Badge variant=\"outline\" className={getCategoryColor(file.category)}>\r\n                    {getCategoryIcon(file.category)}\r\n                    <span className=\"ml-1 capitalize\">{file.category.replace('_', ' ')}</span>\r\n                  </Badge>\r\n                  {file.page_count && (\r\n                    <span className=\"text-white/60\">{file.page_count} pages</span>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"text-xs text-white/60\">\r\n                  <span>{formatFileSize(file.size)}</span>\r\n                </div>\r\n\r\n                {/* Stats and actions */}\r\n                <div className=\"flex items-center justify-between pt-2 border-t border-white/10\">\r\n                  <div className=\"flex items-center gap-3 text-xs text-white/60\">\r\n                    <span className=\"flex items-center gap-1\">\r\n                      <Heart className=\"w-3 h-3\" />\r\n                      {file.like_count}\r\n                    </span>\r\n                    <span className=\"flex items-center gap-1\">\r\n                      <Download className=\"w-3 h-3\" />\r\n                      {file.download_count || 0}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {isEnrolled && (\r\n                    <Button \r\n                      size=\"sm\" \r\n                      variant=\"ghost\" \r\n                      className=\"h-6 w-6 p-0 text-white/60 hover:text-red-400\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation()\r\n                        handleLike(file.id)\r\n                      }}\r\n                    >\r\n                      <Heart className={cn(\"w-4 h-4\", file.user_liked ? 'fill-current text-red-400' : '')} />\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Upload info */}\r\n                <p className=\"text-white/50 text-xs\">\r\n                  By {getDisplayName(file.uploader)} • {new Date(file.created_at).toLocaleDateString()}\r\n                </p>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Empty State */}\r\n      {files.length === 0 && !loading && (\r\n        <Card className=\"bg-white/5 backdrop-blur-xl border-white/10 border-dashed\">\r\n          <CardContent className=\"p-8 text-center\">\r\n            <FolderOpen className=\"w-12 h-12 text-white/30 mx-auto mb-4\" />\r\n            <h4 className=\"text-white font-medium mb-2\">\r\n              {activeFilter === 'all' ? 'No materials yet' : `No ${activeFilter.replace('_', ' ')} found`}\r\n            </h4>\r\n            <p className=\"text-white/70 text-sm mb-4\">\r\n              {activeFilter === 'all' \r\n                ? 'Be the first to share course materials with your classmates'\r\n                : `No files in the ${activeFilter.replace('_', ' ')} category yet`\r\n              }\r\n            </p>\r\n            {isEnrolled && activeFilter === 'all' && (\r\n              <Button onClick={() => setShowUpload(true)}>\r\n                <Upload className=\"w-4 h-4 mr-2\" />\r\n                Upload First Material\r\n              </Button>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Upload Modal - We'll create this component next */}\r\n      {showUpload && (\r\n        <UploadModal \r\n          courseId={courseId}\r\n          onClose={() => setShowUpload(false)}\r\n          onUploadComplete={() => {\r\n            setShowUpload(false)\r\n            loadFiles()\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* File Preview Modal - We'll create this component next */}\r\n      {selectedFile && (\r\n        <FilePreviewModal \r\n          file={selectedFile}\r\n          onClose={() => setSelectedFile(null)}\r\n          onAddToLearning={handleAddToLearning}\r\n          onLike={handleLike}\r\n          onDownload={handleDownload}\r\n          isEnrolled={isEnrolled}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Import the actual components\r\nimport { EnhancedUploadModal } from \"./enhanced-upload-modal\"\r\nimport { FilePreviewModal } from \"./file-preview-modal\"\r\n\r\n// Wrapper for the upload modal\r\nfunction UploadModal({ courseId, onClose, onUploadComplete }: {\r\n  courseId: string\r\n  onClose: () => void\r\n  onUploadComplete: () => void\r\n}) {\r\n  return (\r\n    <EnhancedUploadModal \r\n      courseId={courseId}\r\n      onClose={onClose}\r\n      onUploadComplete={onUploadComplete}\r\n    />\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0XA,+BAA+B;AAC/B;AACA;;;AArYA;;;;;;;;AAyCO,SAAS,gBAAgB,KAAoD;QAApD,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAwB,GAApD;;IAC9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,OAAO;YAAa,MAAM,qNAAA,CAAA,aAAU;YAAE,OAAO;QAAgB;QAC1E;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;QAAkB;QAC5E;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,iNAAA,CAAA,WAAQ;YAAE,OAAO;QAAgB;QACtF;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,iNAAA,CAAA,WAAQ;YAAE,OAAO;QAAiB;QAC/E;YAAE,IAAI;YAAsB,OAAO;YAAsB,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;QAAkB;QAChG;YAAE,IAAI;YAAS,OAAO;YAAS,MAAM,2NAAA,CAAA,gBAAa;YAAE,OAAO;QAAe;QAC1E;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,6NAAA,CAAA,eAAY;YAAE,OAAO;QAAgB;KAC7E;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,YAAY;QAChB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,UAAU;YACtE,IAAI,CAAC,SAAS,MAAM;gBAClB,SAAS;YACX,OAAO;gBACL,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,EAAE;QACb;QACA,WAAW;IACb;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;YACtD,IAAI,CAAC,OAAO;gBACV,MAAM,aAAY,gCAAgC;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;YACrD,IAAI,CAAC,OAAO;gBACV,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YAC1D,IAAI,CAAC,SAAS,MAAM;gBAClB,uBAAuB;gBACvB,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG,KAAK,GAAG;gBACpB,KAAK,QAAQ,GAAG,KAAK,QAAQ;gBAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,yCAAyC;gBACzC,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnD,MAAM,OAAO,CAAA,yBAAA,mCAAA,aAAc,IAAI,KAAI,iNAAA,CAAA,WAAQ;QAC3C,qBAAO,6LAAC;YAAK,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,eAAe;YACf,WAAW;YACX,oBAAoB;YACpB,OAAO;YACP,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,OAAO,MAAM;IACjE;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,QAAQ,CAAC,QAAQ,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QACrD,IAAI,KAAK,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,aAAa,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QACnF,IAAI,KAAK,QAAQ,CAAC,UAAU,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;QACxD,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,SAAS,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;IAC/D;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAC7C,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;oBAEtC,4BACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,cAAc;wBAC7B,WAAU;;0CAEV,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,iBAAiB,SAAS,EAAE;oBAC7C,qBACE,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAS,WAAW,YAAY;wBAChC,MAAK;wBACL,SAAS,IAAM,gBAAgB,SAAS,EAAE;wBAC1C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,WACI,iDACA;;0CAGN,6LAAC,SAAS,IAAI;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,SAAS,KAAK;;;;;;4BAC1D,SAAS,KAAK;;uBAZV,SAAS,EAAE;;;;;gBAetB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,mIAAA,CAAA,OAAI;wBAEH,WAAU;wBACV,SAAS,IAAM,gBAAgB;kCAE/B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,gBAAgB,KAAK,IAAI;8DAC1B,6LAAC;oDAAK,WAAU;8DACb,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;sDAKhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,gBAAgB;oDAClB;8DAEA,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;gDAEhB,4BACC;;sEACE,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,oBAAoB,KAAK,EAAE;4DAC7B;sEAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,eAAe,KAAK,EAAE;4DACxB;sEAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DACX,KAAK,IAAI;;;;;;;;;;;wCAIb,KAAK,WAAW,kBACf,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAKrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAW,iBAAiB,KAAK,QAAQ;;wDAC/D,gBAAgB,KAAK,QAAQ;sEAC9B,6LAAC;4DAAK,WAAU;sEAAmB,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;gDAE/D,KAAK,UAAU,kBACd,6LAAC;oDAAK,WAAU;;wDAAiB,KAAK,UAAU;wDAAC;;;;;;;;;;;;;sDAIrD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAM,eAAe,KAAK,IAAI;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,UAAU;;;;;;;sEAElB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,KAAK,cAAc,IAAI;;;;;;;;;;;;;gDAI3B,4BACC,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,WAAW,KAAK,EAAE;oDACpB;8DAEA,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,KAAK,UAAU,GAAG,8BAA8B;;;;;;;;;;;;;;;;;sDAMtF,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC/B,eAAe,KAAK,QAAQ;gDAAE;gDAAI,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;uBAnHnF,KAAK,EAAE;;;;;;;;;;YA4HjB,MAAM,MAAM,KAAK,KAAK,CAAC,yBACtB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6LAAC;4BAAG,WAAU;sCACX,iBAAiB,QAAQ,qBAAqB,AAAC,MAAoC,OAA/B,aAAa,OAAO,CAAC,KAAK,MAAK;;;;;;sCAEtF,6LAAC;4BAAE,WAAU;sCACV,iBAAiB,QACd,gEACA,AAAC,mBAAiD,OAA/B,aAAa,OAAO,CAAC,KAAK,MAAK;;;;;;wBAGvD,cAAc,iBAAiB,uBAC9B,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,cAAc;;8CACnC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAS5C,4BACC,6LAAC;gBACC,UAAU;gBACV,SAAS,IAAM,cAAc;gBAC7B,kBAAkB;oBAChB,cAAc;oBACd;gBACF;;;;;;YAKH,8BACC,6LAAC,2JAAA,CAAA,mBAAgB;gBACf,MAAM;gBACN,SAAS,IAAM,gBAAgB;gBAC/B,iBAAiB;gBACjB,QAAQ;gBACR,YAAY;gBACZ,YAAY;;;;;;;;;;;;AAKtB;GAxVgB;KAAA;;;AA8VhB,+BAA+B;AAC/B,SAAS,YAAY,KAIpB;QAJoB,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAIzD,GAJoB;IAKnB,qBACE,6LAAC,8JAAA,CAAA,sBAAmB;QAClB,UAAU;QACV,SAAS;QACT,kBAAkB;;;;;;AAGxB;MAZS", "debugId": null}}, {"offset": {"line": 8361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,KAIoC;QAJpC,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD,GAJpC;IAKhB,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 8407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/ui/fade-in.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect, useRef, ReactNode } from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\ninterface FadeInProps {\r\n  children: ReactNode\r\n  className?: string\r\n  delay?: number\r\n  duration?: number\r\n  direction?: 'up' | 'down' | 'left' | 'right' | 'none'\r\n  distance?: number\r\n}\r\n\r\nexport function FadeIn({ \r\n  children, \r\n  className, \r\n  delay = 0, \r\n  duration = 0.6,\r\n  direction = 'up',\r\n  distance = 30\r\n}: FadeInProps) {\r\n  const ref = useRef<HTMLDivElement>(null)\r\n\r\n  useEffect(() => {\r\n    const element = ref.current\r\n    if (!element) return\r\n\r\n    // Set initial state\r\n    element.style.opacity = '0'\r\n    element.style.transform = getInitialTransform(direction, distance)\r\n    element.style.transition = `opacity ${duration}s ease-out, transform ${duration}s ease-out`\r\n\r\n    // Create intersection observer\r\n    const observer = new IntersectionObserver(\r\n      (entries) => {\r\n        entries.forEach((entry) => {\r\n          if (entry.isIntersecting) {\r\n            setTimeout(() => {\r\n              element.style.opacity = '1'\r\n              element.style.transform = 'translate3d(0, 0, 0) scale(1)'\r\n            }, delay * 1000)\r\n            observer.unobserve(element)\r\n          }\r\n        })\r\n      },\r\n      { threshold: 0.1 }\r\n    )\r\n\r\n    observer.observe(element)\r\n\r\n    return () => observer.disconnect()\r\n  }, [delay, duration, direction, distance])\r\n\r\n  return (\r\n    <div ref={ref} className={cn(\"will-change-transform\", className)}>\r\n      {children}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction getInitialTransform(direction: string, distance: number): string {\r\n  switch (direction) {\r\n    case 'up':\r\n      return `translate3d(0, ${distance}px, 0)`\r\n    case 'down':\r\n      return `translate3d(0, -${distance}px, 0)`\r\n    case 'left':\r\n      return `translate3d(${distance}px, 0, 0)`\r\n    case 'right':\r\n      return `translate3d(-${distance}px, 0, 0)`\r\n    default:\r\n      return 'translate3d(0, 0, 0) scale(0.9)'\r\n  }\r\n}\r\n\r\n// Staggered children animation\r\ninterface StaggeredFadeInProps {\r\n  children: ReactNode[]\r\n  className?: string\r\n  staggerDelay?: number\r\n  initialDelay?: number\r\n}\r\n\r\nexport function StaggeredFadeIn({ \r\n  children, \r\n  className, \r\n  staggerDelay = 0.1, \r\n  initialDelay = 0 \r\n}: StaggeredFadeInProps) {\r\n  return (\r\n    <div className={className}>\r\n      {children.map((child, index) => (\r\n        <FadeIn \r\n          key={index} \r\n          delay={initialDelay + (index * staggerDelay)}\r\n        >\r\n          {child}\r\n        </FadeIn>\r\n      ))}\r\n    </div>\r\n  )\r\n} "], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAcO,SAAS,OAAO,KAOT;QAPS,EACrB,QAAQ,EACR,SAAS,EACT,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,IAAI,EAChB,WAAW,EAAE,EACD,GAPS;;IAQrB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,UAAU,IAAI,OAAO;YAC3B,IAAI,CAAC,SAAS;YAEd,oBAAoB;YACpB,QAAQ,KAAK,CAAC,OAAO,GAAG;YACxB,QAAQ,KAAK,CAAC,SAAS,GAAG,oBAAoB,WAAW;YACzD,QAAQ,KAAK,CAAC,UAAU,GAAG,AAAC,WAA2C,OAAjC,UAAS,0BAAiC,OAAT,UAAS;YAEhF,+BAA+B;YAC/B,MAAM,WAAW,IAAI;oCACnB,CAAC;oBACC,QAAQ,OAAO;4CAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB;wDAAW;wCACT,QAAQ,KAAK,CAAC,OAAO,GAAG;wCACxB,QAAQ,KAAK,CAAC,SAAS,GAAG;oCAC5B;uDAAG,QAAQ;gCACX,SAAS,SAAS,CAAC;4BACrB;wBACF;;gBACF;mCACA;gBAAE,WAAW;YAAI;YAGnB,SAAS,OAAO,CAAC;YAEjB;oCAAO,IAAM,SAAS,UAAU;;QAClC;2BAAG;QAAC;QAAO;QAAU;QAAW;KAAS;IAEzC,qBACE,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBACnD;;;;;;AAGP;GA7CgB;KAAA;AA+ChB,SAAS,oBAAoB,SAAiB,EAAE,QAAgB;IAC9D,OAAQ;QACN,KAAK;YACH,OAAO,AAAC,kBAA0B,OAAT,UAAS;QACpC,KAAK;YACH,OAAO,AAAC,mBAA2B,OAAT,UAAS;QACrC,KAAK;YACH,OAAO,AAAC,eAAuB,OAAT,UAAS;QACjC,KAAK;YACH,OAAO,AAAC,gBAAwB,OAAT,UAAS;QAClC;YACE,OAAO;IACX;AACF;AAUO,SAAS,gBAAgB,KAKT;QALS,EAC9B,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EAClB,eAAe,CAAC,EACK,GALS;IAM9B,qBACE,6LAAC;QAAI,WAAW;kBACb,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,6LAAC;gBAEC,OAAO,eAAgB,QAAQ;0BAE9B;eAHI;;;;;;;;;;AAQf;MAlBgB", "debugId": null}}, {"offset": {"line": 8519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/components/course/course-content.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { use<PERSON><PERSON><PERSON> } from \"next/navigation\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ForumInterface } from \"./forum-interface\"\nimport { LectureNotesTab } from \"./lecture-notes-tab\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { cn } from \"@/lib/utils\"\nimport { FadeIn } from \"@/components/ui/fade-in\"\nimport { \n  BookOpen,\n  Users,\n  UserPlus,\n  UserMinus,\n  Calendar,\n  GraduationCap,\n  Globe,\n  Lock,\n  Settings,\n  Upload,\n  FileText,\n  Brain,\n  ChevronLeft,\n  ArrowLeft,\n  Clock,\n  Star,\n  MoreVertical,\n  Download,\n  Eye,\n  Edit,\n  Trash2,\n  Share2,\n  Award,\n  Target,\n  TrendingUp\n} from \"lucide-react\"\n\ninterface Course {\n  id: string\n  title: string\n  description: string\n  university: string\n  is_public: boolean\n  course_code: string\n  semester: string\n  professor_name: string\n  enrollment_count: number\n  user_id: string\n  created_at: string\n  color: string\n  course_enrollments: {\n    id: string\n    user_id: string\n    is_tutor: boolean\n    profiles: {\n      name: string\n      username: string\n      is_username_public: boolean\n    }\n  }[]\n}\n\ninterface CourseContentProps {\n  course: Course\n  user: any\n  isEnrolled: boolean\n  isOwner: boolean\n  onEnroll: () => void\n  onUnenroll: () => void\n}\n\nexport function CourseContent({ course, user, isEnrolled, isOwner, onEnroll, onUnenroll }: CourseContentProps) {\n  const router = useRouter()\n  const [activeTab, setActiveTab] = useState(\"overview\")\n  const [files, setFiles] = useState<any[]>([])\n  const [notes, setNotes] = useState<any[]>([])\n\n  // Mock data for demonstration - will be replaced with real data\n  const mockFiles = [\n    {\n      id: \"1\",\n      name: \"Lecture 1 - Introduction.pdf\",\n      type: \"pdf\",\n      size: \"2.4 MB\",\n      uploaded_at: \"2024-01-15T10:00:00Z\",\n      uploaded_by: \"Alex M.\"\n    },\n    {\n      id: \"2\", \n      name: \"Assignment 1.docx\",\n      type: \"docx\",\n      size: \"156 KB\",\n      uploaded_at: \"2024-01-18T14:30:00Z\",\n      uploaded_by: \"Sarah K.\"\n    },\n    {\n      id: \"3\",\n      name: \"Neural Networks Slides.pptx\", \n      type: \"pptx\",\n      size: \"5.8 MB\",\n      uploaded_at: \"2024-01-20T09:15:00Z\",\n      uploaded_by: \"Mike T.\"\n    },\n    {\n      id: \"4\",\n      name: \"Algorithms Cheat Sheet.pdf\",\n      type: \"pdf\", \n      size: \"891 KB\",\n      uploaded_at: \"2024-01-22T16:45:00Z\",\n      uploaded_by: \"John D.\"\n    }\n  ]\n\n  const mockNotes = [\n    {\n      id: \"1\",\n      title: \"Introduction to Machine Learning\",\n      content: \"Key concepts and fundamentals...\",\n      created_at: \"2024-01-16T15:30:00Z\",\n      is_ai_generated: false,\n      created_by: \"You\"\n    },\n    {\n      id: \"2\",\n      title: \"Neural Network Basics - AI Summary\",\n      content: \"Auto-generated summary from lecture materials...\",\n      created_at: \"2024-01-21T11:45:00Z\", \n      is_ai_generated: true,\n      created_by: \"AI Assistant\"\n    }\n  ]\n\n  // Community stats\n  const communityStats = {\n    filesShared: mockFiles.length,\n    discussions: 8,\n    exercises: 5,\n    members: course.enrollment_count\n  }\n\n  // Mock discussion forums\n  const discussionForums = [\n    {\n      id: \"general\",\n      title: \"General Discussion\",\n      description: \"Main course discussions and casual chat\",\n      icon: \"💬\",\n      messageCount: 34,\n      lastActivity: \"2 hours ago\",\n      color: \"from-blue-500 to-cyan-500\"\n    },\n    {\n      id: \"questions\",\n      title: \"Questions & Help\", \n      description: \"Get help from classmates and ask questions\",\n      icon: \"❓\",\n      messageCount: 18,\n      lastActivity: \"1 hour ago\",\n      color: \"from-green-500 to-emerald-500\"\n    },\n    {\n      id: \"study-groups\",\n      title: \"Study Groups\",\n      description: \"Organize and join study sessions\",\n      icon: \"📚\",\n      messageCount: 12,\n      lastActivity: \"4 hours ago\", \n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      id: \"announcements\",\n      title: \"Announcements\",\n      description: \"Important course updates and news\",\n      icon: \"📢\",\n      messageCount: 5,\n      lastActivity: \"1 day ago\",\n      color: \"from-orange-500 to-red-500\"\n    }\n  ]\n\n  // Enhanced recent activity\n  const recentActivity = [\n    { \n      type: \"file_upload\",\n      action: \"uploaded\", \n      item: \"Algorithms Cheat Sheet.pdf\", \n      time: \"2 hours ago\", \n      user: \"John D.\",\n      icon: \"📄\"\n    },\n    { \n      type: \"discussion\",\n      action: \"started discussion\", \n      item: \"Midterm Study Group Formation\", \n      time: \"4 hours ago\", \n      user: \"Sarah K.\",\n      icon: \"💬\"\n    },\n    { \n      type: \"exercise\",\n      action: \"completed\", \n      item: \"Chapter 3 Quiz (Score: 85%)\", \n      time: \"6 hours ago\", \n      user: \"Alex M.\",\n      icon: \"🎯\"\n    },\n    { \n      type: \"ai_generated\",\n      action: \"generated\", \n      item: \"5 practice questions from Lecture 2\", \n      time: \"1 day ago\", \n      user: \"AI Assistant\",\n      icon: \"🤖\"\n    },\n    { \n      type: \"member_join\",\n      action: \"joined the course\", \n      item: \"\", \n      time: \"1 day ago\", \n      user: \"3 new students\",\n      icon: \"👥\"\n    }\n  ]\n\n  // Mock data for new features\n  const pinnedResources = [\n    {\n      id: \"1\",\n      title: \"Complete ML Study Guide\",\n      type: \"pdf\",\n      likes: 47,\n      downloads: 123,\n      uploadedBy: \"Sarah K.\",\n      uploadedAt: \"2024-01-20T10:00:00Z\",\n      isPinned: true\n    },\n    {\n      id: \"2\", \n      title: \"Python Cheat Sheet Collection\",\n      type: \"pdf\",\n      likes: 34,\n      downloads: 89,\n      uploadedBy: \"Alex M.\",\n      uploadedAt: \"2024-01-18T14:30:00Z\",\n      isPinned: true\n    },\n    {\n      id: \"3\",\n      title: \"Final Exam Practice Questions\",\n      type: \"docx\",\n      likes: 28,\n      downloads: 67,\n      uploadedBy: \"Mike T.\",\n      uploadedAt: \"2024-01-22T09:15:00Z\",\n      isPinned: true\n    }\n  ]\n\n  const studySessions = [\n    {\n      id: \"1\",\n      title: \"Midterm Prep Session\",\n      type: \"scheduled\",\n      host: \"Sarah K.\",\n      participants: 8,\n      maxParticipants: 12,\n      startTime: \"2024-01-28T14:00:00Z\",\n      duration: \"2 hours\",\n      location: \"Virtual Room A\",\n      description: \"Group study for upcoming midterm exam\",\n      tags: [\"midterm\", \"exam-prep\"]\n    },\n    {\n      id: \"2\",\n      title: \"Algorithm Problem Solving\",\n      type: \"live\",\n      host: \"Alex M.\",\n      participants: 5,\n      maxParticipants: 8,\n      startTime: \"2024-01-25T16:00:00Z\",\n      duration: \"1.5 hours\",\n      location: \"Virtual Room B\",\n      description: \"Working through coding challenges together\",\n      tags: [\"algorithms\", \"coding\"]\n    },\n    {\n      id: \"3\",\n      title: \"Weekly Study Group\",\n      type: \"recurring\",\n      host: \"John D.\",\n      participants: 6,\n      maxParticipants: 10,\n      startTime: \"2024-01-26T18:00:00Z\",\n      duration: \"2 hours\",\n      location: \"Library - Room 203\",\n      description: \"Regular weekly meetup for course discussions\",\n      tags: [\"weekly\", \"discussion\"]\n    }\n  ]\n\n  const leaderboard = {\n    topContributors: [\n      { rank: 1, name: \"Sarah K.\", contributions: 23, badge: \"🏆\", points: 1420 },\n      { rank: 2, name: \"Alex M.\", contributions: 19, badge: \"🥈\", points: 1180 },\n      { rank: 3, name: \"Mike T.\", contributions: 15, badge: \"🥉\", points: 950 },\n      { rank: 4, name: \"John D.\", contributions: 12, badge: \"⭐\", points: 780 },\n      { rank: 5, name: \"Emma L.\", contributions: 9, badge: \"⭐\", points: 560 }\n    ],\n    studyStreaks: [\n      { rank: 1, name: \"Alex M.\", streak: 15, badge: \"🔥\", days: \"days\" },\n      { rank: 2, name: \"Sarah K.\", streak: 12, badge: \"🔥\", days: \"days\" },\n      { rank: 3, name: \"Mike T.\", streak: 8, badge: \"⚡\", days: \"days\" }\n    ],\n    exerciseChampions: [\n      { rank: 1, name: \"John D.\", score: 98, badge: \"🎯\", exercises: 12 },\n      { rank: 2, name: \"Emma L.\", score: 95, badge: \"🎯\", exercises: 10 },\n      { rank: 3, name: \"Sarah K.\", score: 92, badge: \"🏹\", exercises: 8 }\n    ]\n  }\n\n  const enrolledUsers = course.course_enrollments?.slice(0, 8) || []\n\n  return (\n    <div className=\"p-4 md:p-6 space-y-8\">\n      {/* Course Header - Simplified */}\n      <FadeIn delay={0.1} direction=\"down\">\n        <div className=\"flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4\">\n          <div className=\"flex items-center gap-4\">\n            <Button\n              variant=\"outline\"\n              onClick={() => router.push(\"/courses\")}\n              className=\"border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200\"\n            >\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Back to Courses\n            </Button>\n            \n            <div className=\"flex items-center gap-3\">\n              <div \n                className=\"w-12 h-12 rounded-xl flex items-center justify-center transform hover:scale-105 transition-all duration-200\"\n                style={{ backgroundColor: course.color }}\n              >\n                <BookOpen className=\"w-7 h-7 text-white\" />\n              </div>\n              <div>\n                <div className=\"flex items-center gap-2 mb-1\">\n                  <h1 className=\"text-2xl md:text-3xl font-bold text-white\">{course.title}</h1>\n                  {course.course_code && (\n                    <Badge variant=\"secondary\" className=\"bg-purple-500/20 text-purple-200\">\n                      {course.course_code}\n                    </Badge>\n                  )}\n                  {course.is_public ? (\n                    <Globe className=\"w-5 h-5 text-green-400\" />\n                  ) : (\n                    <Lock className=\"w-5 h-5 text-yellow-400\" />\n                  )}\n                  {isOwner && (\n                    <Badge variant=\"outline\" className=\"border-blue-400 text-blue-400\">\n                      Owner\n                    </Badge>\n                  )}\n                </div>\n                <p className=\"text-white/70 text-base\">\n                  {course.description || \"Join this collaborative learning community\"}\n                </p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-3\">\n            {!isOwner && (\n              <Button\n                onClick={isEnrolled ? onUnenroll : onEnroll}\n                className={cn(\n                  \"transform hover:scale-105 transition-all duration-200\",\n                  isEnrolled\n                    ? \"bg-red-500 hover:bg-red-600 text-white\"\n                    : \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white\"\n                )}\n              >\n                {isEnrolled ? (\n                  <>\n                    <UserMinus className=\"w-4 h-4 mr-2\" />\n                    Unenroll\n                  </>\n                ) : (\n                  <>\n                    <UserPlus className=\"w-4 h-4 mr-2\" />\n                    Enroll\n                  </>\n                )}\n              </Button>\n            )}\n            \n            {isOwner && (\n              <Button\n                variant=\"outline\"\n                className=\"border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200\"\n              >\n                <Settings className=\"w-4 h-4 mr-2\" />\n                Manage Course\n              </Button>\n            )}\n            \n            <Button\n              variant=\"outline\"\n              className=\"border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200\"\n            >\n              <Share2 className=\"w-4 h-4 mr-2\" />\n              Share\n            </Button>\n          </div>\n        </div>\n      </FadeIn>\n\n      {/* Content Tabs */}\n      <FadeIn delay={0.3}>\n        <div className=\"space-y-6\">\n          <div className=\"flex items-center gap-3 border-b border-white/20 pb-4\">\n            {[\"overview\", \"lecture-notes\", \"exercises\", \"all-files\", \"my-learning\", \"students\"].map((tab) => (\n              <Button\n                key={tab}\n                variant={activeTab === tab ? \"default\" : \"outline\"}\n                size=\"sm\"\n                className={cn(\n                  \"capitalize transition-all duration-200\",\n                  activeTab === tab\n                    ? \"bg-purple-500 text-white scale-105 shadow-lg\"\n                    : \"border-white/20 text-white/80 hover:bg-white/10 hover:scale-105\"\n                )}\n                onClick={() => setActiveTab(tab)}\n              >\n                                  {tab === \"overview\" && <BookOpen className=\"h-3 w-3 mr-1\" />}\n                  {tab === \"lecture-notes\" && <FileText className=\"h-3 w-3 mr-1\" />}\n                  {tab === \"exercises\" && <Brain className=\"h-3 w-3 mr-1\" />}\n                  {tab === \"all-files\" && <Upload className=\"h-3 w-3 mr-1\" />}\n                  {tab === \"my-learning\" && <Star className=\"h-3 w-3 mr-1\" />}\n                  {tab === \"students\" && <Users className=\"h-3 w-3 mr-1\" />}\n                {tab.replace(\"-\", \" \")}\n              </Button>\n            ))}\n          </div>\n\n          {/* Overview Tab - Redesigned */}\n          {activeTab === \"overview\" && (\n            <div className=\"space-y-6\">\n              {/* Course Information Section */}\n              <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center gap-2\">\n                    <BookOpen className=\"h-5 w-5 text-purple-400\" />\n                    Course Information\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <div>\n                      <p className=\"text-white/60 text-sm\">Professor</p>\n                      <p className=\"text-white font-medium\">{course.professor_name || \"TBA\"}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-white/60 text-sm\">Semester</p>\n                      <p className=\"text-white font-medium\">{course.semester}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-white/60 text-sm\">University</p>\n                      <p className=\"text-white font-medium\">{course.university}</p>\n                    </div>\n                  </div>\n                  {course.description && (\n                    <div>\n                      <p className=\"text-white/60 text-sm mb-2\">Description</p>\n                      <p className=\"text-white/80 text-sm leading-relaxed\">{course.description}</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Recent Activity & Pinned Resources Grid */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Recent Activity */}\n                <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center gap-2\">\n                      <Clock className=\"h-5 w-5 text-purple-400\" />\n                      Recent Activity\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {recentActivity.map((activity, index) => (\n                        <div key={index} className=\"flex items-start gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors\">\n                          <div className=\"text-lg mt-0.5\">{activity.icon}</div>\n                          <div className=\"flex-1\">\n                            <p className=\"text-white text-sm\">\n                              <span className=\"font-medium\">{activity.user}</span>\n                              <span className=\"text-white/80\"> {activity.action} </span>\n                              {activity.item && (\n                                <span className=\"text-purple-300 font-medium\">{activity.item}</span>\n                              )}\n                            </p>\n                            <p className=\"text-white/50 text-xs mt-1\">{activity.time}</p>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Pinned Resources */}\n                <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center gap-2\">\n                      📌 Pinned Resources\n                    </CardTitle>\n                    <CardDescription className=\"text-white/70\">\n                      Most liked and popular study materials\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"space-y-3\">\n                    {pinnedResources.map((resource) => (\n                      <div \n                        key={resource.id}\n                        className=\"p-3 rounded-lg bg-gradient-to-r from-amber-500/10 to-yellow-500/10 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 cursor-pointer group\"\n                      >\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"w-10 h-10 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-lg flex items-center justify-center text-white\">\n                              <FileText className=\"h-5 w-5\" />\n                            </div>\n                            <div className=\"flex-1\">\n                              <div className=\"flex items-center gap-2 mb-1\">\n                                <h4 className=\"text-white font-medium group-hover:text-amber-300 transition-colors text-sm\">\n                                  {resource.title}\n                              </h4>\n                                <Badge variant=\"outline\" className=\"border-amber-400 text-amber-400 text-xs\">\n                                  📌\n                                </Badge>\n                            </div>\n                              <div className=\"flex items-center gap-4 text-xs text-white/60\">\n                                <span>❤️ {resource.likes}</span>\n                                <span>📥 {resource.downloads}</span>\n                                <span>By {resource.uploadedBy}</span>\n                          </div>\n                            </div>\n                          </div>\n                          <div className=\"flex gap-1\">\n                            <Button size=\"sm\" variant=\"outline\" className=\"border-amber-400/30 text-amber-400 hover:bg-amber-400/10 h-8 w-8 p-0\">\n                              <Download className=\"w-3 h-3\" />\n                            </Button>\n                            <Button size=\"sm\" variant=\"outline\" className=\"border-amber-400/30 text-amber-400 hover:bg-amber-400/10 h-8 w-8 p-0\">\n                              <Eye className=\"w-3 h-3\" />\n                            </Button>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                    \n                    <Button \n                      variant=\"outline\"\n                      className=\"w-full border-amber-400/30 text-amber-400 hover:bg-amber-400/10 mt-4\"\n                    >\n                      <Star className=\"w-4 h-4 mr-2\" />\n                      View All Popular Resources\n                    </Button>\n                  </CardContent>\n                </Card>\n              </div>\n\n              {/* Discussion Forums - Functional Interface */}\n              <ForumInterface courseId={course.id} user={user} />\n\n\n\n              {/* Study Session Organizer */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center gap-2\">\n                      📚 Study Sessions\n                    </CardTitle>\n                    <CardDescription className=\"text-white/70\">\n                      Join or organize study groups\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"space-y-3\">\n                    {studySessions.map((session) => (\n                      <div \n                        key={session.id}\n                        className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer group ${\n                          session.type === 'live' \n                            ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-400/20 hover:border-green-400/40' \n                            : session.type === 'scheduled'\n                            ? 'bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border-blue-400/20 hover:border-blue-400/40'\n                            : 'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-400/20 hover:border-purple-400/40'\n                        }`}\n                      >\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <div className=\"flex items-center gap-2\">\n                            <div className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              session.type === 'live' \n                                ? 'bg-green-500/20 text-green-400' \n                                : session.type === 'scheduled'\n                                ? 'bg-blue-500/20 text-blue-400'\n                                : 'bg-purple-500/20 text-purple-400'\n                            }`}>\n                              {session.type === 'live' ? '🔴 LIVE' : session.type === 'scheduled' ? '📅 Scheduled' : '🔄 Recurring'}\n                          </div>\n                            <h4 className=\"text-white font-medium\">{session.title}</h4>\n                        </div>\n                          <div className=\"text-white/60 text-sm\">\n                            {session.participants}/{session.maxParticipants}\n                          </div>\n                        </div>\n                        \n                        <p className=\"text-white/70 text-sm mb-2\">{session.description}</p>\n                        \n                        <div className=\"flex items-center justify-between text-xs\">\n                          <div className=\"text-white/60\">\n                            📍 {session.location} • ⏱️ {session.duration}\n                          </div>\n                          <div className=\"text-white/60\">\n                            Host: {session.host}\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2 mt-3\">\n                          {session.tags.map((tag) => (\n                            <Badge key={tag} variant=\"outline\" className=\"border-white/20 text-white/60 text-xs\">\n                              {tag}\n                            </Badge>\n                          ))}\n                        </div>\n                      </div>\n                    ))}\n                    \n                    <div className=\"flex gap-2 mt-4\">\n                      <Button \n                        className=\"flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white\"\n                      >\n                        <Calendar className=\"w-4 h-4 mr-2\" />\n                        Schedule Session\n                      </Button>\n                      <Button \n                        variant=\"outline\"\n                        className=\"flex-1 border-white/20 text-white hover:bg-white/10\"\n                      >\n                        <Users className=\"w-4 h-4 mr-2\" />\n                        Find Study Buddy\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Course Leaderboard */}\n              <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center gap-2\">\n                      🏆 Course Leaderboard\n                  </CardTitle>\n                    <CardDescription className=\"text-white/70\">\n                      Top contributors and achievers\n                    </CardDescription>\n                </CardHeader>\n                <CardContent>\n                    <div className=\"space-y-4\">\n                      {/* Top Contributors */}\n                      <div>\n                        <h5 className=\"text-white font-medium mb-3 flex items-center gap-2\">\n                          <TrendingUp className=\"h-4 w-4 text-green-400\" />\n                          Top Contributors\n                        </h5>\n                        <div className=\"space-y-2\">\n                          {leaderboard.topContributors.slice(0, 3).map((contributor) => (\n                            <div key={contributor.rank} className=\"flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors\">\n                              <div className=\"flex items-center gap-3\">\n                                <div className=\"text-lg\">{contributor.badge}</div>\n                                <div>\n                                  <div className=\"text-white font-medium text-sm\">{contributor.name}</div>\n                                  <div className=\"text-white/60 text-xs\">{contributor.contributions} contributions</div>\n                                </div>\n                              </div>\n                              <div className=\"text-green-400 font-bold text-sm\">{contributor.points}pts</div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n\n                      {/* Study Streaks */}\n                      <div>\n                        <h5 className=\"text-white font-medium mb-3 flex items-center gap-2\">\n                          <Award className=\"h-4 w-4 text-orange-400\" />\n                          Study Streaks\n                        </h5>\n                        <div className=\"space-y-2\">\n                          {leaderboard.studyStreaks.map((streak) => (\n                            <div key={streak.rank} className=\"flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors\">\n                              <div className=\"flex items-center gap-3\">\n                                <div className=\"text-lg\">{streak.badge}</div>\n                                <div className=\"text-white font-medium text-sm\">{streak.name}</div>\n                              </div>\n                              <div className=\"text-orange-400 font-bold text-sm\">{streak.streak} {streak.days}</div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n\n                      {/* Exercise Champions */}\n                      <div>\n                        <h5 className=\"text-white font-medium mb-3 flex items-center gap-2\">\n                          <Target className=\"h-4 w-4 text-purple-400\" />\n                          Exercise Champions\n                        </h5>\n                        <div className=\"space-y-2\">\n                          {leaderboard.exerciseChampions.map((champion) => (\n                            <div key={champion.rank} className=\"flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors\">\n                              <div className=\"flex items-center gap-3\">\n                                <div className=\"text-lg\">{champion.badge}</div>\n                                <div>\n                                  <div className=\"text-white font-medium text-sm\">{champion.name}</div>\n                                  <div className=\"text-white/60 text-xs\">{champion.exercises} exercises</div>\n                                </div>\n                              </div>\n                              <div className=\"text-purple-400 font-bold text-sm\">{champion.score}%</div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n\n                    <Button \n                      variant=\"outline\"\n                        className=\"w-full border-white/20 text-white hover:bg-white/10 mt-4\"\n                    >\n                        <Award className=\"w-4 h-4 mr-2\" />\n                        View Full Leaderboard\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n              </div>\n            </div>\n          )}\n\n          {/* Files Tab */}\n          {activeTab === \"all-files\" && (\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-xl font-bold text-white\">Course Materials</h3>\n                {(isOwner || isEnrolled) && (\n                  <Button className=\"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white\">\n                    <Upload className=\"w-4 h-4 mr-2\" />\n                    Upload File\n                  </Button>\n                )}\n              </div>\n              \n              <div className=\"grid gap-4\">\n                {mockFiles.map((file) => (\n                  <Card key={file.id} className=\"bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-200\">\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\">\n                            <FileText className=\"h-5 w-5 text-blue-400\" />\n                          </div>\n                          <div>\n                            <h4 className=\"text-white font-medium\">{file.name}</h4>\n                            <p className=\"text-white/70 text-sm\">\n                              {file.size} • Uploaded by {file.uploaded_by} • {new Date(file.uploaded_at).toLocaleDateString()}\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <Button size=\"sm\" variant=\"outline\" className=\"border-white/20 text-white hover:bg-white/10\">\n                            <Download className=\"w-4 h-4\" />\n                          </Button>\n                          <Button size=\"sm\" variant=\"outline\" className=\"border-white/20 text-white hover:bg-white/10\">\n                            <Eye className=\"w-4 h-4\" />\n                          </Button>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Lecture Notes Tab */}\n          {activeTab === \"lecture-notes\" && (\n            <LectureNotesTab \n              courseId={course.id}\n              user={user}\n              isEnrolled={isEnrolled}\n            />\n          )}\n\n                     {/* Exercises Tab */}\n           {activeTab === \"exercises\" && (\n             <div className=\"space-y-6\">\n               <div className=\"flex items-center justify-between\">\n                 <h3 className=\"text-xl font-bold text-white\">Course Exercises</h3>\n                 <Button className=\"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white\">\n                   <Target className=\"w-4 h-4 mr-2\" />\n                   Create Exercise\n                 </Button>\n               </div>\n               \n               <div className=\"grid gap-4\">\n                 {/* Mock exercises would go here */}\n                 <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                   <CardContent className=\"p-4 text-center\">\n                     <h4 className=\"text-white font-medium\">No exercises yet!</h4>\n                     <p className=\"text-white/70 text-sm\">Be the first to create one.</p>\n                   </CardContent>\n                 </Card>\n               </div>\n             </div>\n           )}\n\n           {/* My Learning Tab */}\n           {activeTab === \"my-learning\" && (\n             <div className=\"space-y-6\">\n               <div className=\"flex items-center justify-between\">\n                 <div>\n                   <h3 className=\"text-xl font-bold text-white\">My Learning Path</h3>\n                   <p className=\"text-white/70 text-sm\">Your personalized collection of study materials</p>\n                 </div>\n                 <Button className=\"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white\">\n                   <Star className=\"w-4 h-4 mr-2\" />\n                   Create Custom Path\n                 </Button>\n               </div>\n\n               {/* Learning Progress Overview */}\n               <Card className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                 <CardHeader>\n                   <CardTitle className=\"text-white flex items-center gap-2\">\n                     <Target className=\"h-5 w-5 text-purple-400\" />\n                     Your Progress\n                   </CardTitle>\n                 </CardHeader>\n                 <CardContent className=\"space-y-4\">\n                   <div className=\"space-y-2\">\n                     <div className=\"flex justify-between text-sm\">\n                       <span className=\"text-white/80\">Learning Path Completion</span>\n                       <span className=\"text-white font-medium\">3/8 items (38%)</span>\n                     </div>\n                     <Progress value={38} className=\"h-3\" />\n                   </div>\n                   \n                   <div className=\"grid grid-cols-3 gap-4 pt-4\">\n                     <div className=\"text-center\">\n                       <div className=\"text-2xl font-bold text-white\">5</div>\n                       <div className=\"text-xs text-white/70\">Files Saved</div>\n                     </div>\n                     <div className=\"text-center\">\n                       <div className=\"text-2xl font-bold text-white\">2</div>\n                       <div className=\"text-xs text-white/70\">Exercises Added</div>\n                     </div>\n                     <div className=\"text-center\">\n                       <div className=\"text-2xl font-bold text-white\">4.2h</div>\n                       <div className=\"text-xs text-white/70\">Time Spent</div>\n                     </div>\n                   </div>\n                 </CardContent>\n               </Card>\n\n               {/* Learning Path Items */}\n               <div className=\"space-y-4\">\n                 <h4 className=\"text-lg font-semibold text-white\">My Saved Materials</h4>\n                 \n                 {/* Mock learning path items */}\n                 {[\n                   {\n                     id: \"1\",\n                     type: \"file\",\n                     title: \"Introduction to Algorithms.pdf\",\n                     description: \"Essential reading for understanding basic concepts\",\n                     status: \"completed\",\n                     timeSpent: \"45 min\",\n                     addedDate: \"2 days ago\"\n                   },\n                   {\n                     id: \"2\", \n                     type: \"exercise\",\n                     title: \"Chapter 1 Quiz\",\n                     description: \"Practice questions to test understanding\",\n                     status: \"in_progress\",\n                     timeSpent: \"15 min\",\n                     addedDate: \"1 day ago\"\n                   },\n                   {\n                     id: \"3\",\n                     type: \"file\", \n                     title: \"Advanced Data Structures.pptx\",\n                     description: \"Slides covering trees, graphs, and hash tables\",\n                     status: \"not_started\",\n                     timeSpent: \"0 min\",\n                     addedDate: \"6 hours ago\"\n                   }\n                 ].map((item) => (\n                   <Card key={item.id} className=\"bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-200\">\n                     <CardContent className=\"p-4\">\n                       <div className=\"flex items-center justify-between\">\n                         <div className=\"flex items-center gap-4\">\n                           <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${\n                             item.status === 'completed' ? 'bg-green-500/20' :\n                             item.status === 'in_progress' ? 'bg-yellow-500/20' : 'bg-gray-500/20'\n                           }`}>\n                             {item.type === 'file' ? (\n                               <FileText className={`h-6 w-6 ${\n                                 item.status === 'completed' ? 'text-green-400' :\n                                 item.status === 'in_progress' ? 'text-yellow-400' : 'text-gray-400'\n                               }`} />\n                             ) : (\n                               <Brain className={`h-6 w-6 ${\n                                 item.status === 'completed' ? 'text-green-400' :\n                                 item.status === 'in_progress' ? 'text-yellow-400' : 'text-gray-400'\n                               }`} />\n                             )}\n                           </div>\n                           <div className=\"flex-1\">\n                             <div className=\"flex items-center gap-2 mb-1\">\n                               <h4 className=\"text-white font-medium\">{item.title}</h4>\n                               <Badge variant=\"outline\" className={`text-xs border-current ${\n                                 item.status === 'completed' ? 'text-green-400 border-green-400' :\n                                 item.status === 'in_progress' ? 'text-yellow-400 border-yellow-400' : 'text-gray-400 border-gray-400'\n                               }`}>\n                                 {item.status.replace('_', ' ')}\n                               </Badge>\n                             </div>\n                             <p className=\"text-white/70 text-sm mb-2\">{item.description}</p>\n                             <div className=\"flex items-center gap-4 text-xs text-white/50\">\n                               <span>Added {item.addedDate}</span>\n                               <span>Time spent: {item.timeSpent}</span>\n                             </div>\n                           </div>\n                         </div>\n                         <div className=\"flex items-center gap-2\">\n                           <Button size=\"sm\" variant=\"outline\" className=\"border-white/20 text-white hover:bg-white/10\">\n                             <Eye className=\"w-4 h-4\" />\n                           </Button>\n                           <Button size=\"sm\" variant=\"outline\" className=\"border-white/20 text-white hover:bg-white/10\">\n                             {item.status === 'completed' ? \n                               <Award className=\"w-4 h-4\" /> : \n                               <Target className=\"w-4 h-4\" />\n                             }\n                           </Button>\n                           <Button size=\"sm\" variant=\"outline\" className=\"border-white/20 text-white hover:bg-white/10\">\n                             <MoreVertical className=\"w-4 h-4\" />\n                           </Button>\n                         </div>\n                       </div>\n                     </CardContent>\n                   </Card>\n                 ))}\n\n                 {/* Empty State */}\n                 <Card className=\"bg-white/5 backdrop-blur-xl border-white/10 border-dashed\">\n                   <CardContent className=\"p-8 text-center\">\n                     <Star className=\"w-12 h-12 text-white/30 mx-auto mb-4\" />\n                     <h4 className=\"text-white font-medium mb-2\">Build Your Learning Path</h4>\n                     <p className=\"text-white/70 text-sm mb-4\">\n                       Browse course materials and add them to your personal learning collection\n                     </p>\n                     <div className=\"flex gap-3 justify-center\">\n                       <Button \n                         variant=\"outline\"\n                         className=\"border-white/20 text-white hover:bg-white/10\"\n                         onClick={() => setActiveTab(\"all-files\")}\n                       >\n                         <Upload className=\"w-4 h-4 mr-2\" />\n                         Browse Files\n                       </Button>\n                       <Button \n                         variant=\"outline\"\n                         className=\"border-white/20 text-white hover:bg-white/10\"\n                         onClick={() => setActiveTab(\"exercises\")}\n                       >\n                         <Brain className=\"w-4 h-4 mr-2\" />\n                         Browse Exercises\n                       </Button>\n                     </div>\n                   </CardContent>\n                 </Card>\n               </div>\n             </div>\n           )}\n\n          {/* Students Tab */}\n          {activeTab === \"students\" && (\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-xl font-bold text-white\">Enrolled Students ({course.enrollment_count})</h3>\n              </div>\n              \n              <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                {enrolledUsers.map((enrollment) => (\n                  <Card key={enrollment.id} className=\"bg-white/5 backdrop-blur-xl border-white/10\">\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n                          <span className=\"text-white font-medium text-sm\">\n                            {enrollment.profiles.name?.charAt(0) || 'A'}\n                          </span>\n                        </div>\n                        <div className=\"flex-1\">\n                          <h4 className=\"text-white font-medium\">\n                            {enrollment.profiles.is_username_public \n                              ? enrollment.profiles.username \n                              : enrollment.profiles.name || 'Anonymous'\n                            }\n                          </h4>\n                          {enrollment.is_tutor && (\n                            <Badge variant=\"outline\" className=\"border-yellow-400 text-yellow-400 text-xs\">\n                              Tutor\n                            </Badge>\n                          )}\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </FadeIn>\n    </div>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;AA2EO,SAAS,cAAc,KAA+E;QAA/E,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAsB,GAA/E;QAyPN;;IAxPtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE5C,gEAAgE;IAChE,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;QACf;KACD;IAED,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,YAAY;YACZ,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,YAAY;YACZ,iBAAiB;YACjB,YAAY;QACd;KACD;IAED,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,aAAa,UAAU,MAAM;QAC7B,aAAa;QACb,WAAW;QACX,SAAS,OAAO,gBAAgB;IAClC;IAEA,yBAAyB;IACzB,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;YACd,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;YACd,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;YACd,cAAc;YACd,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;YACd,cAAc;YACd,OAAO;QACT;KACD;IAED,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;KACD;IAED,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,UAAU;QACZ;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,cAAc;YACd,iBAAiB;YACjB,WAAW;YACX,UAAU;YACV,UAAU;YACV,aAAa;YACb,MAAM;gBAAC;gBAAW;aAAY;QAChC;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,cAAc;YACd,iBAAiB;YACjB,WAAW;YACX,UAAU;YACV,UAAU;YACV,aAAa;YACb,MAAM;gBAAC;gBAAc;aAAS;QAChC;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,cAAc;YACd,iBAAiB;YACjB,WAAW;YACX,UAAU;YACV,UAAU;YACV,aAAa;YACb,MAAM;gBAAC;gBAAU;aAAa;QAChC;KACD;IAED,MAAM,cAAc;QAClB,iBAAiB;YACf;gBAAE,MAAM;gBAAG,MAAM;gBAAY,eAAe;gBAAI,OAAO;gBAAM,QAAQ;YAAK;YAC1E;gBAAE,MAAM;gBAAG,MAAM;gBAAW,eAAe;gBAAI,OAAO;gBAAM,QAAQ;YAAK;YACzE;gBAAE,MAAM;gBAAG,MAAM;gBAAW,eAAe;gBAAI,OAAO;gBAAM,QAAQ;YAAI;YACxE;gBAAE,MAAM;gBAAG,MAAM;gBAAW,eAAe;gBAAI,OAAO;gBAAK,QAAQ;YAAI;YACvE;gBAAE,MAAM;gBAAG,MAAM;gBAAW,eAAe;gBAAG,OAAO;gBAAK,QAAQ;YAAI;SACvE;QACD,cAAc;YACZ;gBAAE,MAAM;gBAAG,MAAM;gBAAW,QAAQ;gBAAI,OAAO;gBAAM,MAAM;YAAO;YAClE;gBAAE,MAAM;gBAAG,MAAM;gBAAY,QAAQ;gBAAI,OAAO;gBAAM,MAAM;YAAO;YACnE;gBAAE,MAAM;gBAAG,MAAM;gBAAW,QAAQ;gBAAG,OAAO;gBAAK,MAAM;YAAO;SACjE;QACD,mBAAmB;YACjB;gBAAE,MAAM;gBAAG,MAAM;gBAAW,OAAO;gBAAI,OAAO;gBAAM,WAAW;YAAG;YAClE;gBAAE,MAAM;gBAAG,MAAM;gBAAW,OAAO;gBAAI,OAAO;gBAAM,WAAW;YAAG;YAClE;gBAAE,MAAM;gBAAG,MAAM;gBAAY,OAAO;gBAAI,OAAO;gBAAM,WAAW;YAAE;SACnE;IACH;IAEA,MAAM,gBAAgB,EAAA,6BAAA,OAAO,kBAAkB,cAAzB,iDAAA,2BAA2B,KAAK,CAAC,GAAG,OAAM,EAAE;IAElE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,yIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAK,WAAU;0BAC5B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,OAAO,KAAK;4CAAC;sDAEvC,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6C,OAAO,KAAK;;;;;;wDACtE,OAAO,WAAW,kBACjB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAClC,OAAO,WAAW;;;;;;wDAGtB,OAAO,SAAS,iBACf,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;iFAEjB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAEjB,yBACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAgC;;;;;;;;;;;;8DAKvE,6LAAC;oDAAE,WAAU;8DACV,OAAO,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAM/B,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,yBACA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,aAAa;oCACnC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA,aACI,2CACA;8CAGL,2BACC;;0DACE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;qEAIxC;;0DACE,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;gCAO5C,yBACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAKzC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC,yIAAA,CAAA,SAAM;gBAAC,OAAO;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAY;gCAAiB;gCAAa;gCAAa;gCAAe;6BAAW,CAAC,GAAG,CAAC,CAAC,oBACvF,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,cAAc,MAAM,YAAY;oCACzC,MAAK;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,cAAc,MACV,iDACA;oCAEN,SAAS,IAAM,aAAa;;wCAET,QAAQ,4BAAc,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAC1D,QAAQ,iCAAmB,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAC/C,QAAQ,6BAAe,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCACxC,QAAQ,6BAAe,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACzC,QAAQ,+BAAiB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACzC,QAAQ,4BAAc,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCACzC,IAAI,OAAO,CAAC,KAAK;;mCAjBb;;;;;;;;;;wBAuBV,cAAc,4BACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;;;;;;sDAIpD,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAA0B,OAAO,cAAc,IAAI;;;;;;;;;;;;sEAElE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAA0B,OAAO,QAAQ;;;;;;;;;;;;sEAExD,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAA0B,OAAO,UAAU;;;;;;;;;;;;;;;;;;gDAG3D,OAAO,WAAW,kBACjB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAE,WAAU;sEAAyC,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAOhF,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAA4B;;;;;;;;;;;;8DAIjD,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,6LAAC;gEAAgB,WAAU;;kFACzB,6LAAC;wEAAI,WAAU;kFAAkB,SAAS,IAAI;;;;;;kFAC9C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;;kGACX,6LAAC;wFAAK,WAAU;kGAAe,SAAS,IAAI;;;;;;kGAC5C,6LAAC;wFAAK,WAAU;;4FAAgB;4FAAE,SAAS,MAAM;4FAAC;;;;;;;oFACjD,SAAS,IAAI,kBACZ,6LAAC;wFAAK,WAAU;kGAA+B,SAAS,IAAI;;;;;;;;;;;;0FAGhE,6LAAC;gFAAE,WAAU;0FAA8B,SAAS,IAAI;;;;;;;;;;;;;+DAVlD;;;;;;;;;;;;;;;;;;;;;sDAmBlB,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;sEAG1D,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAgB;;;;;;;;;;;;8DAI7C,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;wDACpB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;gEAEC,WAAU;0EAEV,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;8FAEtB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGAAG,WAAU;8GACX,SAAS,KAAK;;;;;;8GAEjB,6LAAC,oIAAA,CAAA,QAAK;oGAAC,SAAQ;oGAAU,WAAU;8GAA0C;;;;;;;;;;;;sGAI/E,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;;wGAAK;wGAAI,SAAS,KAAK;;;;;;;8GACxB,6LAAC;;wGAAK;wGAAI,SAAS,SAAS;;;;;;;8GAC5B,6LAAC;;wGAAK;wGAAI,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;sFAInC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;oFAAU,WAAU;8FAC5C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;8FAEtB,6LAAC,qIAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;oFAAU,WAAU;8FAC5C,cAAA,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+DA7BhB,SAAS,EAAE;;;;;sEAoCpB,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8CAQzC,6LAAC,qJAAA,CAAA,iBAAc;oCAAC,UAAU,OAAO,EAAE;oCAAE,MAAM;;;;;;8CAK3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;sEAG1D,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAgB;;;;;;;;;;;;8DAI7C,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;wDACpB,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC;gEAEC,WAAW,AAAC,0EAMX,OALC,QAAQ,IAAI,KAAK,SACb,uGACA,QAAQ,IAAI,KAAK,cACjB,iGACA;;kFAGN,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAW,AAAC,8CAMhB,OALC,QAAQ,IAAI,KAAK,SACb,mCACA,QAAQ,IAAI,KAAK,cACjB,iCACA;kGAEH,QAAQ,IAAI,KAAK,SAAS,YAAY,QAAQ,IAAI,KAAK,cAAc,iBAAiB;;;;;;kGAEzF,6LAAC;wFAAG,WAAU;kGAA0B,QAAQ,KAAK;;;;;;;;;;;;0FAEvD,6LAAC;gFAAI,WAAU;;oFACZ,QAAQ,YAAY;oFAAC;oFAAE,QAAQ,eAAe;;;;;;;;;;;;;kFAInD,6LAAC;wEAAE,WAAU;kFAA8B,QAAQ,WAAW;;;;;;kFAE9D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;oFAAgB;oFACzB,QAAQ,QAAQ;oFAAC;oFAAO,QAAQ,QAAQ;;;;;;;0FAE9C,6LAAC;gFAAI,WAAU;;oFAAgB;oFACtB,QAAQ,IAAI;;;;;;;;;;;;;kFAIvB,6LAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,6LAAC,oIAAA,CAAA,QAAK;gFAAW,SAAQ;gFAAU,WAAU;0FAC1C;+EADS;;;;;;;;;;;+DAxCX,QAAQ,EAAE;;;;;sEAgDnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,WAAU;;sFAEV,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGvC,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;;sFAEV,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ5C,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;sEAGxD,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAgB;;;;;;;;;;;;8DAI/C,6LAAC,mIAAA,CAAA,cAAW;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;4EAA2B;;;;;;;kFAGnD,6LAAC;wEAAI,WAAU;kFACZ,YAAY,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BAC5C,6LAAC;gFAA2B,WAAU;;kGACpC,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GAAW,YAAY,KAAK;;;;;;0GAC3C,6LAAC;;kHACC,6LAAC;wGAAI,WAAU;kHAAkC,YAAY,IAAI;;;;;;kHACjE,6LAAC;wGAAI,WAAU;;4GAAyB,YAAY,aAAa;4GAAC;;;;;;;;;;;;;;;;;;;kGAGtE,6LAAC;wFAAI,WAAU;;4FAAoC,YAAY,MAAM;4FAAC;;;;;;;;+EAR9D,YAAY,IAAI;;;;;;;;;;;;;;;;0EAehC,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAA4B;;;;;;;kFAG/C,6LAAC;wEAAI,WAAU;kFACZ,YAAY,YAAY,CAAC,GAAG,CAAC,CAAC,uBAC7B,6LAAC;gFAAsB,WAAU;;kGAC/B,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GAAW,OAAO,KAAK;;;;;;0GACtC,6LAAC;gGAAI,WAAU;0GAAkC,OAAO,IAAI;;;;;;;;;;;;kGAE9D,6LAAC;wFAAI,WAAU;;4FAAqC,OAAO,MAAM;4FAAC;4FAAE,OAAO,IAAI;;;;;;;;+EALvE,OAAO,IAAI;;;;;;;;;;;;;;;;0EAY3B,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAA4B;;;;;;;kFAGhD,6LAAC;wEAAI,WAAU;kFACZ,YAAY,iBAAiB,CAAC,GAAG,CAAC,CAAC,yBAClC,6LAAC;gFAAwB,WAAU;;kGACjC,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GAAW,SAAS,KAAK;;;;;;0GACxC,6LAAC;;kHACC,6LAAC;wGAAI,WAAU;kHAAkC,SAAS,IAAI;;;;;;kHAC9D,6LAAC;wGAAI,WAAU;;4GAAyB,SAAS,SAAS;4GAAC;;;;;;;;;;;;;;;;;;;kGAG/D,6LAAC;wFAAI,WAAU;;4FAAqC,SAAS,KAAK;4FAAC;;;;;;;;+EAR3D,SAAS,IAAI;;;;;;;;;;;;;;;;0EAc/B,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACN,WAAU;;kFAEV,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAW/C,cAAc,6BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;wCAC5C,CAAC,WAAW,UAAU,mBACrB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMzC,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,mIAAA,CAAA,OAAI;4CAAe,WAAU;sDAC5B,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA0B,KAAK,IAAI;;;;;;sFACjD,6LAAC;4EAAE,WAAU;;gFACV,KAAK,IAAI;gFAAC;gFAAgB,KAAK,WAAW;gFAAC;gFAAI,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;sEAInG,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAU,WAAU;8EAC5C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC,qIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAU,WAAU;8EAC5C,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAnBd,KAAK,EAAE;;;;;;;;;;;;;;;;wBA+BzB,cAAc,iCACb,6LAAC,0JAAA,CAAA,kBAAe;4BACd,UAAU,OAAO,EAAE;4BACnB,MAAM;4BACN,YAAY;;;;;;wBAKd,cAAc,6BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ9C,cAAc,+BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMrC,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;;;;;;sDAIlD,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAyB;;;;;;;;;;;;sEAE3C,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,OAAO;4DAAI,WAAU;;;;;;;;;;;;8DAGjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgC;;;;;;8EAC/C,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgC;;;;;;8EAC/C,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgC;;;;;;8EAC/C,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;wCAGhD;4CACC;gDACE,IAAI;gDACJ,MAAM;gDACN,OAAO;gDACP,aAAa;gDACb,QAAQ;gDACR,WAAW;gDACX,WAAW;4CACb;4CACA;gDACE,IAAI;gDACJ,MAAM;gDACN,OAAO;gDACP,aAAa;gDACb,QAAQ;gDACR,WAAW;gDACX,WAAW;4CACb;4CACA;gDACE,IAAI;gDACJ,MAAM;gDACN,OAAO;gDACP,aAAa;gDACb,QAAQ;gDACR,WAAW;gDACX,WAAW;4CACb;yCACD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC,mIAAA,CAAA,OAAI;gDAAe,WAAU;0DAC5B,cAAA,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAW,AAAC,yDAGhB,OAFC,KAAK,MAAM,KAAK,cAAc,oBAC9B,KAAK,MAAM,KAAK,gBAAgB,qBAAqB;kFAEpD,KAAK,IAAI,KAAK,uBACb,6LAAC,iNAAA,CAAA,WAAQ;4EAAC,WAAW,AAAC,WAGrB,OAFC,KAAK,MAAM,KAAK,cAAc,mBAC9B,KAAK,MAAM,KAAK,gBAAgB,oBAAoB;;;;;iGAGtD,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAW,AAAC,WAGlB,OAFC,KAAK,MAAM,KAAK,cAAc,mBAC9B,KAAK,MAAM,KAAK,gBAAgB,oBAAoB;;;;;;;;;;;kFAI1D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAG,WAAU;kGAA0B,KAAK,KAAK;;;;;;kGAClD,6LAAC,oIAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAU,WAAW,AAAC,0BAGpC,OAFC,KAAK,MAAM,KAAK,cAAc,oCAC9B,KAAK,MAAM,KAAK,gBAAgB,sCAAsC;kGAErE,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0FAG9B,6LAAC;gFAAE,WAAU;0FAA8B,KAAK,WAAW;;;;;;0FAC3D,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;;4FAAK;4FAAO,KAAK,SAAS;;;;;;;kGAC3B,6LAAC;;4FAAK;4FAAa,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;0EAIvC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEAAC,MAAK;wEAAK,SAAQ;wEAAU,WAAU;kFAC5C,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEAAC,MAAK;wEAAK,SAAQ;wEAAU,WAAU;kFAC3C,KAAK,MAAM,KAAK,4BACf,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;iGACjB,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAGtB,6LAAC,qIAAA,CAAA,SAAM;wEAAC,MAAK;wEAAK,SAAQ;wEAAU,WAAU;kFAC5C,cAAA,6LAAC,6NAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAhDvB,KAAK,EAAE;;;;;sDAyDpB,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,aAAa;;kFAE5B,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGrC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,aAAa;;kFAE5B,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAWhD,cAAc,4BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;;4CAA+B;4CAAoB,OAAO,gBAAgB;4CAAC;;;;;;;;;;;;8CAG3F,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC;4CAMP;6DALX,6LAAC,mIAAA,CAAA,OAAI;4CAAqB,WAAU;sDAClC,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,EAAA,4BAAA,WAAW,QAAQ,CAAC,IAAI,cAAxB,gDAAA,0BAA0B,MAAM,CAAC,OAAM;;;;;;;;;;;sEAG5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,WAAW,QAAQ,CAAC,kBAAkB,GACnC,WAAW,QAAQ,CAAC,QAAQ,GAC5B,WAAW,QAAQ,CAAC,IAAI,IAAI;;;;;;gEAGjC,WAAW,QAAQ,kBAClB,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAA4C;;;;;;;;;;;;;;;;;;;;;;;2CAhB9E,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC1C;GAj8BgB;;QACC,qIAAA,CAAA,YAAS;;;KADV", "debugId": null}}, {"offset": {"line": 11159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/ai_projekts/StudentsHub/studentshub/src/app/courses/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect, useState } from \"react\"\r\nimport { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from \"next/navigation\"\r\nimport { auth, db } from \"@/lib/supabase\"\r\nimport { Loader2 } from \"lucide-react\"\r\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\"\r\nimport { NavigationSidebar } from \"@/components/layout/navigation-sidebar\"\r\nimport { AIChatSidebar } from \"@/components/layout/ai-chat-sidebar\"\r\nimport { CourseContent } from \"@/components/course/course-content\"\r\n\r\ninterface Course {\r\n  id: string\r\n  title: string\r\n  description: string\r\n  university: string\r\n  is_public: boolean\r\n  course_code: string\r\n  semester: string\r\n  professor_name: string\r\n  enrollment_count: number\r\n  user_id: string\r\n  created_at: string\r\n  color: string\r\n  course_enrollments: {\r\n    id: string\r\n    user_id: string\r\n    is_tutor: boolean\r\n    profiles: {\r\n      name: string\r\n      username: string\r\n      is_username_public: boolean\r\n    }\r\n  }[]\r\n}\r\n\r\nexport default function CoursePage() {\r\n  const router = useRouter()\r\n  const params = useParams()\r\n  const courseId = params.id as string\r\n  \r\n  const [user, setUser] = useState<any>(null)\r\n  const [course, setCourse] = useState<Course | null>(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [isEnrolled, setIsEnrolled] = useState(false)\r\n  const [isOwner, setIsOwner] = useState(false)\r\n\r\n  useEffect(() => {\r\n    checkUserAndLoadCourse()\r\n  }, [courseId])\r\n\r\n  // Track user session when they're on the course page\r\n  useEffect(() => {\r\n    if (user && courseId && isEnrolled) {\r\n      // Update session immediately\r\n      db.updateUserSession(user.id, courseId)\r\n      \r\n      // Update session every 30 seconds while on the page\r\n      const interval = setInterval(() => {\r\n        db.updateUserSession(user.id, courseId)\r\n      }, 30000) // 30 seconds\r\n\r\n      // Cleanup function to mark user as offline when leaving\r\n      return () => {\r\n        clearInterval(interval)\r\n        // Mark user as offline when leaving the course page\r\n        db.updateUserSession(user.id, courseId, false) // false = offline\r\n      }\r\n    }\r\n  }, [user, courseId, isEnrolled])\r\n\r\n  const checkUserAndLoadCourse = async () => {\r\n    const { user, error } = await auth.getUser()\r\n    \r\n    if (error || !user) {\r\n      router.push(\"/login\")\r\n      return\r\n    }\r\n\r\n    setUser(user)\r\n    await loadCourse(user.id)\r\n    setLoading(false)\r\n  }\r\n\r\n  const loadCourse = async (userId: string) => {\r\n    try {\r\n      // Load course details\r\n      const { data: courseData, error: courseError } = await db.getCourse(courseId)\r\n      \r\n      if (courseError || !courseData) {\r\n        console.error('Course not found:', courseError)\r\n        router.push(\"/courses\")\r\n        return\r\n      }\r\n\r\n      setCourse(courseData)\r\n      setIsOwner(courseData.user_id === userId)\r\n\r\n      // Check if user is enrolled\r\n      const { enrolled } = await db.isUserEnrolled(courseId, userId)\r\n      setIsEnrolled(enrolled || courseData.user_id === userId) // Owner is always \"enrolled\"\r\n      \r\n    } catch (err) {\r\n      console.error('Error loading course:', err)\r\n      router.push(\"/courses\")\r\n    }\r\n  }\r\n\r\n  const handleSignOut = async () => {\r\n    await auth.signOut()\r\n    router.push(\"/\")\r\n  }\r\n\r\n  const handleExpandAIChat = () => {\r\n    router.push(\"/ai-assistant\")\r\n  }\r\n\r\n  const handleEnroll = async () => {\r\n    if (!user || !course) return\r\n    \r\n    try {\r\n      const { error } = await db.enrollInCourse(course.id, user.id)\r\n      if (!error) {\r\n        setIsEnrolled(true)\r\n        // Reload course to update enrollment count\r\n        await loadCourse(user.id)\r\n      }\r\n    } catch (err) {\r\n      console.error('Error enrolling:', err)\r\n    }\r\n  }\r\n\r\n  const handleUnenroll = async () => {\r\n    if (!user || !course || isOwner) return\r\n    \r\n    try {\r\n      const { error } = await db.unenrollFromCourse(course.id, user.id)\r\n      if (!error) {\r\n        setIsEnrolled(false)\r\n        // Reload course to update enrollment count\r\n        await loadCourse(user.id)\r\n      }\r\n    } catch (err) {\r\n      console.error('Error unenrolling:', err)\r\n    }\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <Loader2 className=\"w-8 h-8 animate-spin text-white mx-auto mb-4\" />\r\n          <p className=\"text-white\">Loading course...</p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!course) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <p className=\"text-white\">Course not found</p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <DashboardLayout\r\n      leftSidebar={\r\n        <NavigationSidebar \r\n          user={user} \r\n          profile={null} \r\n          onSignOut={handleSignOut}\r\n        />\r\n      }\r\n      rightSidebar={\r\n        <AIChatSidebar \r\n          onExpand={handleExpandAIChat}\r\n        />\r\n      }\r\n    >\r\n      <CourseContent \r\n        course={course}\r\n        user={user}\r\n        isEnrolled={isEnrolled}\r\n        isOwner={isOwner}\r\n        onEnroll={handleEnroll}\r\n        onUnenroll={handleUnenroll}\r\n      />\r\n    </DashboardLayout>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAoCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC;KAAS;IAEb,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,QAAQ,YAAY,YAAY;gBAClC,6BAA6B;gBAC7B,yHAAA,CAAA,KAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE;gBAE9B,oDAAoD;gBACpD,MAAM,WAAW;qDAAY;wBAC3B,yHAAA,CAAA,KAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE;oBAChC;oDAAG,OAAO,aAAa;;gBAEvB,wDAAwD;gBACxD;4CAAO;wBACL,cAAc;wBACd,oDAAoD;wBACpD,yHAAA,CAAA,KAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE,UAAU,QAAO,kBAAkB;oBACnE;;YACF;QACF;+BAAG;QAAC;QAAM;QAAU;KAAW;IAE/B,MAAM,yBAAyB;QAC7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,OAAO;QAE1C,IAAI,SAAS,CAAC,MAAM;YAClB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,QAAQ;QACR,MAAM,WAAW,KAAK,EAAE;QACxB,WAAW;IACb;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,sBAAsB;YACtB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,KAAE,CAAC,SAAS,CAAC;YAEpE,IAAI,eAAe,CAAC,YAAY;gBAC9B,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,UAAU;YACV,WAAW,WAAW,OAAO,KAAK;YAElC,4BAA4B;YAC5B,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,KAAE,CAAC,cAAc,CAAC,UAAU;YACvD,cAAc,YAAY,WAAW,OAAO,KAAK,SAAQ,6BAA6B;QAExF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,yHAAA,CAAA,OAAI,CAAC,OAAO;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAEtB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,KAAE,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;YAC5D,IAAI,CAAC,OAAO;gBACV,cAAc;gBACd,2CAA2C;gBAC3C,MAAM,WAAW,KAAK,EAAE;YAC1B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,UAAU,SAAS;QAEjC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,KAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;YAChE,IAAI,CAAC,OAAO;gBACV,cAAc;gBACd,2CAA2C;gBAC3C,MAAM,WAAW,KAAK,EAAE;YAC1B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAa;;;;;;;;;;;;;;;;IAIlC;IAEA,qBACE,6LAAC,sJAAA,CAAA,kBAAe;QACd,2BACE,6LAAC,wJAAA,CAAA,oBAAiB;YAChB,MAAM;YACN,SAAS;YACT,WAAW;;;;;;QAGf,4BACE,6LAAC,wJAAA,CAAA,gBAAa;YACZ,UAAU;;;;;;kBAId,cAAA,6LAAC,oJAAA,CAAA,gBAAa;YACZ,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,SAAS;YACT,UAAU;YACV,YAAY;;;;;;;;;;;AAIpB;GA7JwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}