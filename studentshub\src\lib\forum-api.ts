import { supabase } from './supabase'
import type { 
  ForumCategory, 
  ForumTopic, 
  ForumMessage,
  CreateMessageInput,
  CreateTopicInput,
  ForumCategoryWithStats,
  ForumTopicWithDetails
} from '@/types/forum'

// =========================================
// FORUM CATEGORIES
// =========================================

export async function getForumCategories(courseId: string): Promise<{ data: ForumCategoryWithStats[] | null; error: any }> {
  try {
    // Simplified query to avoid complex nested relationships
    const { data, error } = await supabase
      .from('forum_categories')
      .select('*')
      .eq('course_id', courseId)
      .order('created_at', { ascending: true })

    if (error) throw error

    // Process the data to add basic stats
    const categoriesWithStats = data?.map(category => ({
      ...category,
      message_count: 0, // Will be populated separately if needed
      online_users: 0   // Will be populated separately if needed
    })) || []

    return { data: categoriesWithStats, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// =========================================
// FORUM TOPICS
// =========================================

export async function getForumTopics(categoryId: string): Promise<{ data: ForumTopic[] | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('forum_topics')
      .select(`
        *,
        profiles:user_id(name, username),
        last_message_user:last_message_by(name, username)
      `)
      .eq('category_id', categoryId)
      .order('is_pinned', { ascending: false })
      .order('last_message_at', { ascending: false })

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

export async function getForumTopic(topicId: string): Promise<{ data: ForumTopicWithDetails | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('forum_topics')
      .select(`
        *,
        profiles:user_id(name, username),
        forum_topic_participants(
          user_id,
          joined_at,
          notifications_enabled,
          profiles(name, username)
        ),
        forum_messages(
          id,
          content,
          created_at,
          user_id,
          profiles(name, username, avatar_url)
        )
      `)
      .eq('id', topicId)
      .single()

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

export async function createForumTopic(input: CreateTopicInput): Promise<{ data: ForumTopic | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // Create the topic
    const { data: topic, error: topicError } = await supabase
      .from('forum_topics')
      .insert({
        category_id: input.category_id,
        user_id: user.id,
        title: input.title,
        description: input.description
      })
      .select()
      .single()

    if (topicError) throw topicError

    // Create the initial message
    const { error: messageError } = await supabase
      .from('forum_messages')
      .insert({
        category_id: input.category_id,
        topic_id: topic.id,
        user_id: user.id,
        content: input.initial_message,
        message_type: 'text'
      })

    if (messageError) throw messageError

    return { data: topic, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// =========================================
// FORUM MESSAGES
// =========================================

export async function getForumMessages(
  categoryId: string, 
  topicId?: string, 
  limit = 50,
  offset = 0
): Promise<{ data: ForumMessage[] | null; error: any }> {
  try {
    let query = supabase
      .from('forum_messages')
      .select(`
        *,
        profiles:user_id(name, username, avatar_url),
        parent_message:parent_message_id(
          id,
          content,
          profiles:user_id(name, username)
        )
      `)
      .eq('category_id', categoryId)

    if (topicId) {
      query = query.eq('topic_id', topicId)
    } else {
      query = query.is('topic_id', null)
    }

    const { data, error } = await query
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1)

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

export async function createForumMessage(input: CreateMessageInput): Promise<{ data: ForumMessage | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    let imageUrl = null

    // Handle image upload if provided
    if (input.image_file) {
      const fileExt = input.image_file.name.split('.').pop()
      const fileName = `${user.id}/${Date.now()}.${fileExt}`
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('forum-images')
        .upload(fileName, input.image_file)

      if (uploadError) throw uploadError

      const { data: { publicUrl } } = supabase.storage
        .from('forum-images')
        .getPublicUrl(uploadData.path)

      imageUrl = publicUrl
    }

    // Check daily limit for announcements
    if (input.category_id) {
      const { data: category } = await supabase
        .from('forum_categories')
        .select('settings')
        .eq('id', input.category_id)
        .single()

      if (category?.settings?.daily_limit) {
        const today = new Date().toISOString().split('T')[0]
        
        const { data: dailyLimit } = await supabase
          .from('forum_daily_limits')
          .select('message_count')
          .eq('user_id', user.id)
          .eq('category_id', input.category_id)
          .eq('post_date', today)
          .single()

        if (dailyLimit && dailyLimit.message_count >= category.settings.daily_limit) {
          throw new Error('Daily posting limit reached for this category')
        }
      }
    }

    // Use the directly passed image URL if available
    const finalImageUrl = imageUrl || input.image_url || null

    // Determine message type
    const messageType = finalImageUrl ? 'image' : 'text'

    // Create the message
    const { data, error } = await supabase
      .from('forum_messages')
      .insert({
        category_id: input.category_id,
        topic_id: input.topic_id || null,
        user_id: user.id,
        parent_message_id: input.parent_message_id || null,
        content: input.content,
        message_type: messageType,
        referenced_files: input.referenced_files || [],
        image_url: finalImageUrl
      })
      .select(`
        *,
        profiles:user_id(name, username, avatar_url)
      `)
      .single()

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

export async function updateForumMessage(messageId: string, content: string): Promise<{ data: ForumMessage | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // Get the current message to check for images that might be removed
    const { data: currentMessage, error: fetchError } = await supabase
      .from('forum_messages')
      .select('content, image_url, user_id')
      .eq('id', messageId)
      .single()

    if (fetchError) throw fetchError

    // Check if user owns the message
    if (currentMessage.user_id !== user.id) {
      throw new Error('You can only edit your own messages')
    }

    // If the message had an image but the new content doesn't contain image references,
    // we should delete the image from storage
    if (currentMessage.image_url && !content.includes('<img')) {
      try {
        // Extract file path from Supabase Storage URL
        const url = new URL(currentMessage.image_url)
        const pathParts = url.pathname.split('/')
        const bucketIndex = pathParts.indexOf('forum-images')
        
        if (bucketIndex !== -1 && bucketIndex + 1 < pathParts.length) {
          const filePath = pathParts.slice(bucketIndex + 1).join('/')
          console.log('Deleting removed image from edit:', filePath)
          
          const { error: storageError } = await supabase.storage
            .from('forum-images')
            .remove([filePath])

          if (storageError) {
            console.error('Error deleting image from storage during edit:', storageError)
          } else {
            console.log('Successfully deleted removed image from storage')
          }
        }
      } catch (error) {
        console.error('Error deleting image during edit:', error)
      }
    }

    // Update the message with edited flag
    const updateData: any = { 
      content,
      is_edited: true,
      edited_at: new Date().toISOString()
    }

    // If image was removed, clear the image_url field
    if (currentMessage.image_url && !content.includes('<img')) {
      updateData.image_url = null
      updateData.message_type = 'text'
    }

    const { data, error } = await supabase
      .from('forum_messages')
      .update(updateData)
      .eq('id', messageId)
      .eq('user_id', user.id) // Ensure user can only edit their own messages
      .select(`
        *,
        profiles:user_id(name, username, avatar_url)
      `)
      .single()

    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

export async function deleteForumMessage(messageId: string): Promise<{ error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // First get the message to check if it has an image
    const { data: message, error: fetchError } = await supabase
      .from('forum_messages')
      .select('image_url, user_id')
      .eq('id', messageId)
      .single()

    if (fetchError) throw fetchError

    // Check if user owns the message
    if (message.user_id !== user.id) {
      throw new Error('You can only delete your own messages')
    }

    // Delete image from storage if it exists
    if (message.image_url) {
      try {
        // Extract file path from Supabase Storage URL
        // URL format: https://[project].supabase.co/storage/v1/object/public/forum-images/[user_id]/[filename]
        const url = new URL(message.image_url)
        const pathParts = url.pathname.split('/')
        const bucketIndex = pathParts.indexOf('forum-images')
        
        if (bucketIndex !== -1 && bucketIndex + 1 < pathParts.length) {
          // Get the path after 'forum-images/'
          const filePath = pathParts.slice(bucketIndex + 1).join('/')
          console.log('Attempting to delete file:', filePath)
          
          const { error: storageError } = await supabase.storage
            .from('forum-images')
            .remove([filePath])

          if (storageError) {
            console.error('Error deleting image from storage:', storageError)
          } else {
            console.log('Successfully deleted image from storage:', filePath)
          }
        } else {
          console.error('Could not extract file path from URL:', message.image_url)
        }
      } catch (error) {
        console.error('Error parsing image URL:', error)
      }
    }

    // Delete the message
    const { error } = await supabase
      .from('forum_messages')
      .delete()
      .eq('id', messageId)
      .eq('user_id', user.id) // Double-check user ownership

    return { error }
  } catch (error) {
    return { error }
  }
}



// =========================================
// REACTIONS
// =========================================

export async function addReaction(messageId: string, emoji: string): Promise<{ error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // Get current reactions
    const { data: message, error: fetchError } = await supabase
      .from('forum_messages')
      .select('reactions')
      .eq('id', messageId)
      .single()

    if (fetchError) throw fetchError

    const reactions = message.reactions || {}
    
    // Add user to emoji reaction
    if (!reactions[emoji]) {
      reactions[emoji] = []
    }
    
    if (!reactions[emoji].includes(user.id)) {
      reactions[emoji].push(user.id)
    }

    // Update the message
    const { error } = await supabase
      .from('forum_messages')
      .update({ reactions })
      .eq('id', messageId)

    return { error }
  } catch (error) {
    return { error }
  }
}

export async function removeReaction(messageId: string, emoji: string): Promise<{ error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // Get current reactions
    const { data: message, error: fetchError } = await supabase
      .from('forum_messages')
      .select('reactions')
      .eq('id', messageId)
      .single()

    if (fetchError) throw fetchError

    const reactions = message.reactions || {}
    
    // Remove user from emoji reaction
    if (reactions[emoji]) {
      reactions[emoji] = reactions[emoji].filter((userId: string) => userId !== user.id)
      
      // Remove emoji if no users
      if (reactions[emoji].length === 0) {
        delete reactions[emoji]
      }
    }

    // Update the message
    const { error } = await supabase
      .from('forum_messages')
      .update({ reactions })
      .eq('id', messageId)

    return { error }
  } catch (error) {
    return { error }
  }
}

// =========================================
// REAL-TIME SUBSCRIPTIONS
// =========================================

export function subscribeToForumMessages(
  categoryId: string,
  topicId: string | null,
  callback: (payload: any) => void
) {
  const filter = topicId 
    ? `category_id=eq.${categoryId} AND topic_id=eq.${topicId}`
    : `category_id=eq.${categoryId} AND topic_id=is.null`

  return supabase
    .channel(`forum:${categoryId}:${topicId || 'general'}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'forum_messages',
        filter
      },
      callback
    )
    .subscribe()
}

 