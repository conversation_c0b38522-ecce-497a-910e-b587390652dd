# StudentsHub - Development Rules & Patterns

## Project Intelligence & Learned Patterns

### User Preferences
- **PowerShell Usage**: User works with PowerShell terminal - use `;;` instead of `&&` for command chaining
- **Documentation Style**: User prefers comprehensive documentation with clear memory bank structure
- **Planning Approach**: User values detailed roadmaps with phase-based development
- **Technology Choices**: User has chosen Supabase + Next.js + Gemini API stack

### Project-Specific Patterns

#### Memory Bank Maintenance
- Update memory bank files after major milestones
- Keep activeContext.md current with latest focus
- Document all architecture decisions in systemPatterns.md
- Track progress meticulously in progress.md

#### Development Workflow
- Follow 4-phase development approach: Foundation → AI Tools → Career Features → Community
- Prioritize MVP features: Auth → File Upload → RAG Pipeline → Basic Q&A
- Use TypeScript strict mode throughout
- Implement Row Level Security for all data access

#### AI Integration Guidelines
- Always implement provider abstraction layer for future flexibility
- Start with Gemini API but design for multi-provider support
- Implement proper error handling and fallbacks for AI services
- Use vector similarity search with pgvector in Supabase

#### Database Patterns
- Use UUID primary keys for all tables
- Implement soft deletes with deleted_at columns
- Create proper RLS policies for data isolation
- Use JSONB for flexible metadata storage

#### Security Requirements
- Implement Row Level Security on all tables
- Sanitize all user inputs before processing
- Use rate limiting on AI endpoints
- Follow zero-trust security model

#### Performance Considerations
- Implement chunked file uploads for large files
- Use proper indexing on vector similarity searches
- Cache frequently accessed data
- Optimize vector search with proper pgvector configuration

### Code Quality Standards
- Use TypeScript strict mode
- Implement comprehensive error boundaries
- Write tests for critical functionality
- Use consistent naming conventions
- Document complex business logic

### UI/UX Guidelines
- Mobile-first responsive design
- Use Shadcn/ui components for consistency
- Implement proper loading states
- Provide clear error messages
- Focus on student-friendly interface

### Deployment Practices
- Use Vercel for frontend deployment
- Leverage Supabase Edge Functions for backend logic
- Implement proper environment variable management
- Set up CI/CD pipeline with GitHub Actions

This file will evolve as we discover more project-specific patterns and preferences during development. 