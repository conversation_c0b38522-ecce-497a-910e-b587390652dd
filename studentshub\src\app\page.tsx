"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen, Brain, FileText, Sparkles, Users, Zap, ArrowRight, Star, GraduationCap, Upload, MessageSquare } from "lucide-react"
import Link from "next/link"

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800">
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-white/10 animate-pulse"
              style={{
                width: Math.random() * 4 + 1 + "px",
                height: Math.random() * 4 + 1 + "px",
                left: Math.random() * 100 + "%",
                top: Math.random() * 100 + "%",
                animationDelay: Math.random() * 5 + "s",
                animationDuration: Math.random() * 3 + 2 + "s"
              }}
            />
          ))}
        </div>
      </div>

      {/* Navigation */}
      <nav className="relative z-50 glass-ultra fixed top-0 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center animate-glow">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-gradient-ultra">StudentsHub</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#features" className="text-white/80 hover:text-white transition-all duration-300 hover:scale-110">
                Features
              </Link>
              <Link href="#about" className="text-white/80 hover:text-white transition-all duration-300 hover:scale-110">
                About
              </Link>
              <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-6 py-2 rounded-full shadow-ultra" asChild>
                <Link href="/signup">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="animate-slide-up">
            <h1 className="text-6xl md:text-8xl font-black tracking-tighter mb-6">
              <span className="text-gradient-ultra block">AI-Powered</span>
              <span className="text-white block mt-2">Learning Revolution</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/80 max-w-4xl mx-auto mb-10 leading-relaxed">
              Transform your university experience with cutting-edge AI technology. 
              Upload documents, get instant insights, and excel like never before.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-4 text-lg rounded-full shadow-ultra btn-ultra" asChild>
                <Link href="/signup">
                  Start Learning Free
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg rounded-full glass-ultra">
                Watch Demo
              </Button>
            </div>
          </div>

          {/* Floating stats */}
          <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: "10K+", label: "Students", icon: Users },
              { number: "500+", label: "Universities", icon: GraduationCap },
              { number: "1M+", label: "Documents", icon: FileText },
              { number: "95%", label: "Satisfaction", icon: Star }
            ].map((stat, index) => (
              <div key={index} className="glass-ultra p-6 rounded-2xl animate-slide-up depth-3d-hover" style={{ animationDelay: `${index * 0.1}s` }}>
                <stat.icon className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <div className="text-3xl font-bold text-white">{stat.number}</div>
                <div className="text-white/70">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative z-10 py-32 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-black text-white mb-6">
              Everything you need to <span className="text-gradient-ultra">succeed</span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Powerful AI tools designed specifically for university students to enhance learning, boost productivity, and achieve academic excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: "AI Study Assistant",
                description: "Get instant answers from your course materials with intelligent Q&A powered by advanced AI.",
                features: ["Context-aware responses", "Cross-document search", "Source citations"],
                color: "from-blue-400 to-cyan-400"
              },
              {
                icon: Sparkles,
                title: "Smart Content Generation",
                description: "Generate study notes, summaries, flashcards and learn prep materials from your documents.",
                features: ["Study notes & summaries", "Flashcard generation", "Exam preparation"],
                color: "from-purple-400 to-pink-400"
              },
              {
                icon: Upload,
                title: "Smart File Management",
                description: "Organize docs with smart processing for PDFs, documents, and presentations.",
                features: ["Multiple file formats", "Auto-categorization", "Cloud storage"],
                color: "from-green-400 to-emerald-400"
              },
              {
                icon: BookOpen,
                title: "Course Organization",
                description: "Keep track of multiple courses with intelligent organization and progress tracking.",
                features: ["Course organization", "Progress tracking", "Smart categorization"],
                color: "from-orange-400 to-red-400"
              },
              {
                icon: GraduationCap,
                title: "Career Development",
                description: "Build your professional profile with AI-powered resume building and career guidance.",
                features: ["Resume builder", "Career insights", "Skills assessment"],
                color: "from-teal-400 to-cyan-400"
              },
              {
                icon: Users,
                title: "Collaborative Learning",
                description: "Connect with classmates and share insights in real-time collaborative study sessions.",
                features: ["Study groups", "Real-time collaboration", "Knowledge sharing"],
                color: "from-indigo-400 to-purple-400"
              }
            ].map((feature, index) => (
              <Card key={index} className="glass-ultra border-0 rounded-2xl card-premium depth-3d-hover">
                <CardHeader>
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 neon-ultra`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-white mb-2">{feature.title}</CardTitle>
                  <CardDescription className="text-white/70 text-lg mb-4">{feature.description}</CardDescription>
                  <ul className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-center text-sm text-white/60">
                        <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2"></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative z-10 py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="glass-ultra p-12 rounded-3xl text-center">
            <h2 className="text-4xl font-black text-white mb-6">
              Trusted by Students <span className="text-gradient-ultra">Worldwide</span>
            </h2>
            <p className="text-xl text-white/70 mb-10">
              Join thousands of students who are already using AI to achieve academic excellence
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { number: "10,000+", label: "Active Students", description: "Learning daily" },
                { number: "500+", label: "Universities", description: "Worldwide" },
                { number: "1M+", label: "Documents Processed", description: "And growing" },
                { number: "95%", label: "Student Satisfaction", description: "Success rate" }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl font-bold text-gradient-ultra mb-2">{stat.number}</div>
                  <div className="text-lg font-semibold text-white">{stat.label}</div>
                  <div className="text-sm text-white/60">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-32 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="glass-ultra p-12 rounded-3xl">
            <h2 className="text-5xl font-black text-white mb-6">
              Ready to <span className="text-gradient-ultra">Transform</span> Your Learning?
            </h2>
            <p className="text-xl text-white/70 mb-10">
              Join thousands of students who are already using StudentsHub to excel in their studies and accelerate their careers.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-10 py-5 text-xl rounded-full shadow-ultra btn-ultra" asChild>
                <Link href="/signup">
                  Start Learning Today
                  <ArrowRight className="w-6 h-6 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white/10 px-10 py-5 text-xl rounded-full glass-ultra">
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 bg-black/30 backdrop-blur-xl border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-gradient-ultra">StudentsHub</span>
              </div>
              <p className="text-white/60">
                Empowering students with AI-powered learning tools and intelligent study assistance.
              </p>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/features" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/demo" className="hover:text-white transition-colors">Demo</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">Company</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/careers" className="hover:text-white transition-colors">Careers</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">Support</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-12 pt-8 text-center text-white/60">
            <p>&copy; 2024 StudentsHub. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
