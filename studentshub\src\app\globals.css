@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.75rem;
}

/* Ultra-modern animations and effects */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(139, 92, 246, 0.1);
  }
  50% { 
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6), 0 0 80px rgba(139, 92, 246, 0.3);
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes pulse-glow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes slide-up {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slide-in {
  from { 
    opacity: 0; 
    transform: translateX(-30px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.6s ease-out;
}

/* Ultra-premium gradient backgrounds */
.gradient-hero {
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 25%, 
    #f093fb 50%, 
    #f5576c 75%, 
    #4facfe 100%);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

.gradient-card {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.1) 50%, 
    rgba(240, 147, 251, 0.1) 100%);
}

/* Ultra-modern glass effects */
.glass-ultra {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-ultra-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 3D depth effects */
.depth-3d {
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.05),
    0 1px 0 rgba(255, 255, 255, 0.1) inset,
    0 -1px 0 rgba(0, 0, 0, 0.1) inset;
}

.depth-3d-hover {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.depth-3d-hover:hover {
  transform: translateY(-12px) scale(1.05);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 40px 80px rgba(0, 0, 0, 0.1),
    0 2px 0 rgba(255, 255, 255, 0.2) inset;
}

/* Neon effects */
.neon-ultra {
  box-shadow: 
    0 0 10px rgba(139, 92, 246, 0.8),
    0 0 20px rgba(139, 92, 246, 0.6),
    0 0 30px rgba(139, 92, 246, 0.4),
    0 0 40px rgba(139, 92, 246, 0.2);
}

.neon-text-ultra {
  text-shadow: 
    0 0 10px rgba(139, 92, 246, 1),
    0 0 20px rgba(139, 92, 246, 0.8),
    0 0 30px rgba(139, 92, 246, 0.6),
    0 0 40px rgba(139, 92, 246, 0.4);
}

/* Premium card effects */
.card-premium {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.card-premium:hover {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.15) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(31, 38, 135, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Animated text gradients */
.text-gradient-ultra {
  background: linear-gradient(135deg, 
    #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 5s ease infinite;
}

/* Particle background */
.particle-ultra {
  position: relative;
  overflow: hidden;
}

.particle-ultra::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 60% 60%, rgba(240, 147, 251, 0.3) 0%, transparent 50%);
  animation: float 25s ease-in-out infinite;
}

/* Ultra-modern buttons */
.btn-ultra {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-ultra::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.6s;
}

.btn-ultra:hover::before {
  left: 100%;
}

.btn-ultra:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 25px rgba(102, 126, 234, 0.4),
    0 0 30px rgba(102, 126, 234, 0.2);
}

/* Enhanced shadows */
.shadow-ultra {
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 30px rgba(102, 126, 234, 0.1);
}

/* Border glow effects */
.border-ultra {
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 
    0 0 0 1px rgba(139, 92, 246, 0.1),
    0 0 20px rgba(139, 92, 246, 0.2),
    0 0 40px rgba(139, 92, 246, 0.1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #764ba2, #f093fb);
}

/* =================================
   IMPROVED THEME BASED ON FEEDBACK
   ================================= */

/* Improved gradient with lighter midpoint for better readability */
.bg-gradient-improved {
  background: linear-gradient(135deg, 
    #1e293b 0%, 
    #475569 15%, 
    #7c3aed 35%, 
    #a855f7 55%, 
    #ec4899 75%, 
    #be185d 100%);
}

/* Better glassmorphism with improved depth */
.glass-premium {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 16px 40px rgba(80, 35, 175, 0.25);
}

/* Improved button animations (150ms scale-up) */
.btn-improved {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
}

.btn-improved:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 12px 28px rgba(168, 85, 247, 0.4);
}

/* Enhanced input fields with better contrast */
.input-premium {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.2s ease !important;
}

.input-premium:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(168, 85, 247, 0.6) !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.15) !important;
}

.input-premium::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Improved text contrast for accessibility */
.text-high-contrast {
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.6;
}

.text-medium-contrast {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.text-label-contrast {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Refined badges with better spacing */
.badge-premium {
  padding: 0.375rem 0.875rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  transition: all 0.15s ease;
}

.badge-premium:hover {
  transform: scale(1.05);
}

/* Sequential slide-in animations for badges */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-1 {
  animation: slideInFromRight 0.6s ease-out 0.1s forwards;
  opacity: 0;
}

.animate-slide-in-2 {
  animation: slideInFromRight 0.6s ease-out 0.2s forwards;
  opacity: 0;
}

.animate-slide-in-3 {
  animation: slideInFromRight 0.6s ease-out 0.3s forwards;
  opacity: 0;
}

.animate-slide-in-4 {
  animation: slideInFromRight 0.6s ease-out 0.4s forwards;
  opacity: 0;
}

/* Refined gradient text for AI-powered */
.text-gradient-ai {
  background: linear-gradient(135deg, #a855f7, #ec4899, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Improved mobile responsiveness */
@media (max-width: 640px) {
  .glass-premium {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .bg-gradient-improved {
    background: linear-gradient(180deg, 
      #1e293b 0%, 
      #475569 20%, 
      #7c3aed 50%, 
      #a855f7 80%, 
      #ec4899 100%);
  }
}

/* Soft hover effects for cards */
.card-hover-premium {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-premium:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(80, 35, 175, 0.3);
}
