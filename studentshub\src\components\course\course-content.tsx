"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ForumInterface } from "./forum-interface"
import { LectureNotesTab } from "./lecture-notes-tab"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { FadeIn } from "@/components/ui/fade-in"
import { 
  BookOpen,
  Users,
  UserPlus,
  UserMinus,
  Calendar,
  GraduationCap,
  Globe,
  Lock,
  Settings,
  Upload,
  FileText,
  Brain,
  ChevronLeft,
  ArrowLeft,
  Clock,
  Star,
  MoreVertical,
  Download,
  Eye,
  Edit,
  Trash2,
  Share2,
  Award,
  Target,
  TrendingUp
} from "lucide-react"

interface Course {
  id: string
  title: string
  description: string
  university: string
  is_public: boolean
  course_code: string
  semester: string
  professor_name: string
  enrollment_count: number
  user_id: string
  created_at: string
  color: string
  course_enrollments: {
    id: string
    user_id: string
    is_tutor: boolean
    profiles: {
      name: string
      username: string
      is_username_public: boolean
    }
  }[]
}

interface CourseContentProps {
  course: Course
  user: any
  isEnrolled: boolean
  isOwner: boolean
  onEnroll: () => void
  onUnenroll: () => void
}

export function CourseContent({ course, user, isEnrolled, isOwner, onEnroll, onUnenroll }: CourseContentProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("overview")
  const [files, setFiles] = useState<any[]>([])
  const [notes, setNotes] = useState<any[]>([])

  // Mock data for demonstration - will be replaced with real data
  const mockFiles = [
    {
      id: "1",
      name: "Lecture 1 - Introduction.pdf",
      type: "pdf",
      size: "2.4 MB",
      uploaded_at: "2024-01-15T10:00:00Z",
      uploaded_by: "Alex M."
    },
    {
      id: "2", 
      name: "Assignment 1.docx",
      type: "docx",
      size: "156 KB",
      uploaded_at: "2024-01-18T14:30:00Z",
      uploaded_by: "Sarah K."
    },
    {
      id: "3",
      name: "Neural Networks Slides.pptx", 
      type: "pptx",
      size: "5.8 MB",
      uploaded_at: "2024-01-20T09:15:00Z",
      uploaded_by: "Mike T."
    },
    {
      id: "4",
      name: "Algorithms Cheat Sheet.pdf",
      type: "pdf", 
      size: "891 KB",
      uploaded_at: "2024-01-22T16:45:00Z",
      uploaded_by: "John D."
    }
  ]

  const mockNotes = [
    {
      id: "1",
      title: "Introduction to Machine Learning",
      content: "Key concepts and fundamentals...",
      created_at: "2024-01-16T15:30:00Z",
      is_ai_generated: false,
      created_by: "You"
    },
    {
      id: "2",
      title: "Neural Network Basics - AI Summary",
      content: "Auto-generated summary from lecture materials...",
      created_at: "2024-01-21T11:45:00Z", 
      is_ai_generated: true,
      created_by: "AI Assistant"
    }
  ]

  // Community stats
  const communityStats = {
    filesShared: mockFiles.length,
    discussions: 8,
    exercises: 5,
    members: course.enrollment_count
  }

  // Mock discussion forums
  const discussionForums = [
    {
      id: "general",
      title: "General Discussion",
      description: "Main course discussions and casual chat",
      icon: "💬",
      messageCount: 34,
      lastActivity: "2 hours ago",
      color: "from-blue-500 to-cyan-500"
    },
    {
      id: "questions",
      title: "Questions & Help", 
      description: "Get help from classmates and ask questions",
      icon: "❓",
      messageCount: 18,
      lastActivity: "1 hour ago",
      color: "from-green-500 to-emerald-500"
    },
    {
      id: "study-groups",
      title: "Study Groups",
      description: "Organize and join study sessions",
      icon: "📚",
      messageCount: 12,
      lastActivity: "4 hours ago", 
      color: "from-purple-500 to-pink-500"
    },
    {
      id: "announcements",
      title: "Announcements",
      description: "Important course updates and news",
      icon: "📢",
      messageCount: 5,
      lastActivity: "1 day ago",
      color: "from-orange-500 to-red-500"
    }
  ]

  // Enhanced recent activity
  const recentActivity = [
    { 
      type: "file_upload",
      action: "uploaded", 
      item: "Algorithms Cheat Sheet.pdf", 
      time: "2 hours ago", 
      user: "John D.",
      icon: "📄"
    },
    { 
      type: "discussion",
      action: "started discussion", 
      item: "Midterm Study Group Formation", 
      time: "4 hours ago", 
      user: "Sarah K.",
      icon: "💬"
    },
    { 
      type: "exercise",
      action: "completed", 
      item: "Chapter 3 Quiz (Score: 85%)", 
      time: "6 hours ago", 
      user: "Alex M.",
      icon: "🎯"
    },
    { 
      type: "ai_generated",
      action: "generated", 
      item: "5 practice questions from Lecture 2", 
      time: "1 day ago", 
      user: "AI Assistant",
      icon: "🤖"
    },
    { 
      type: "member_join",
      action: "joined the course", 
      item: "", 
      time: "1 day ago", 
      user: "3 new students",
      icon: "👥"
    }
  ]

  // Mock data for new features
  const pinnedResources = [
    {
      id: "1",
      title: "Complete ML Study Guide",
      type: "pdf",
      likes: 47,
      downloads: 123,
      uploadedBy: "Sarah K.",
      uploadedAt: "2024-01-20T10:00:00Z",
      isPinned: true
    },
    {
      id: "2", 
      title: "Python Cheat Sheet Collection",
      type: "pdf",
      likes: 34,
      downloads: 89,
      uploadedBy: "Alex M.",
      uploadedAt: "2024-01-18T14:30:00Z",
      isPinned: true
    },
    {
      id: "3",
      title: "Final Exam Practice Questions",
      type: "docx",
      likes: 28,
      downloads: 67,
      uploadedBy: "Mike T.",
      uploadedAt: "2024-01-22T09:15:00Z",
      isPinned: true
    }
  ]

  const studySessions = [
    {
      id: "1",
      title: "Midterm Prep Session",
      type: "scheduled",
      host: "Sarah K.",
      participants: 8,
      maxParticipants: 12,
      startTime: "2024-01-28T14:00:00Z",
      duration: "2 hours",
      location: "Virtual Room A",
      description: "Group study for upcoming midterm exam",
      tags: ["midterm", "exam-prep"]
    },
    {
      id: "2",
      title: "Algorithm Problem Solving",
      type: "live",
      host: "Alex M.",
      participants: 5,
      maxParticipants: 8,
      startTime: "2024-01-25T16:00:00Z",
      duration: "1.5 hours",
      location: "Virtual Room B",
      description: "Working through coding challenges together",
      tags: ["algorithms", "coding"]
    },
    {
      id: "3",
      title: "Weekly Study Group",
      type: "recurring",
      host: "John D.",
      participants: 6,
      maxParticipants: 10,
      startTime: "2024-01-26T18:00:00Z",
      duration: "2 hours",
      location: "Library - Room 203",
      description: "Regular weekly meetup for course discussions",
      tags: ["weekly", "discussion"]
    }
  ]

  const leaderboard = {
    topContributors: [
      { rank: 1, name: "Sarah K.", contributions: 23, badge: "🏆", points: 1420 },
      { rank: 2, name: "Alex M.", contributions: 19, badge: "🥈", points: 1180 },
      { rank: 3, name: "Mike T.", contributions: 15, badge: "🥉", points: 950 },
      { rank: 4, name: "John D.", contributions: 12, badge: "⭐", points: 780 },
      { rank: 5, name: "Emma L.", contributions: 9, badge: "⭐", points: 560 }
    ],
    studyStreaks: [
      { rank: 1, name: "Alex M.", streak: 15, badge: "🔥", days: "days" },
      { rank: 2, name: "Sarah K.", streak: 12, badge: "🔥", days: "days" },
      { rank: 3, name: "Mike T.", streak: 8, badge: "⚡", days: "days" }
    ],
    exerciseChampions: [
      { rank: 1, name: "John D.", score: 98, badge: "🎯", exercises: 12 },
      { rank: 2, name: "Emma L.", score: 95, badge: "🎯", exercises: 10 },
      { rank: 3, name: "Sarah K.", score: 92, badge: "🏹", exercises: 8 }
    ]
  }

  const enrolledUsers = course.course_enrollments?.slice(0, 8) || []

  return (
    <div className="p-4 md:p-6 space-y-8">
      {/* Course Header - Simplified */}
      <FadeIn delay={0.1} direction="down">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push("/courses")}
              className="border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Courses
            </Button>
            
            <div className="flex items-center gap-3">
              <div 
                className="w-12 h-12 rounded-xl flex items-center justify-center transform hover:scale-105 transition-all duration-200"
                style={{ backgroundColor: course.color }}
              >
                <BookOpen className="w-7 h-7 text-white" />
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <h1 className="text-2xl md:text-3xl font-bold text-white">{course.title}</h1>
                  {course.course_code && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-200">
                      {course.course_code}
                    </Badge>
                  )}
                  {course.is_public ? (
                    <Globe className="w-5 h-5 text-green-400" />
                  ) : (
                    <Lock className="w-5 h-5 text-yellow-400" />
                  )}
                  {isOwner && (
                    <Badge variant="outline" className="border-blue-400 text-blue-400">
                      Owner
                    </Badge>
                  )}
                </div>
                <p className="text-white/70 text-base">
                  {course.description || "Join this collaborative learning community"}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            {!isOwner && (
              <Button
                onClick={isEnrolled ? onUnenroll : onEnroll}
                className={cn(
                  "transform hover:scale-105 transition-all duration-200",
                  isEnrolled
                    ? "bg-red-500 hover:bg-red-600 text-white"
                    : "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
                )}
              >
                {isEnrolled ? (
                  <>
                    <UserMinus className="w-4 h-4 mr-2" />
                    Unenroll
                  </>
                ) : (
                  <>
                    <UserPlus className="w-4 h-4 mr-2" />
                    Enroll
                  </>
                )}
              </Button>
            )}
            
            {isOwner && (
              <Button
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200"
              >
                <Settings className="w-4 h-4 mr-2" />
                Manage Course
              </Button>
            )}
            
            <Button
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </div>
      </FadeIn>

      {/* Content Tabs */}
      <FadeIn delay={0.3}>
        <div className="space-y-6">
          <div className="flex items-center gap-3 border-b border-white/20 pb-4">
            {["overview", "lecture-notes", "exercises", "all-files", "my-learning", "students"].map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? "default" : "outline"}
                size="sm"
                className={cn(
                  "capitalize transition-all duration-200",
                  activeTab === tab
                    ? "bg-purple-500 text-white scale-105 shadow-lg"
                    : "border-white/20 text-white/80 hover:bg-white/10 hover:scale-105"
                )}
                onClick={() => setActiveTab(tab)}
              >
                                  {tab === "overview" && <BookOpen className="h-3 w-3 mr-1" />}
                  {tab === "lecture-notes" && <FileText className="h-3 w-3 mr-1" />}
                  {tab === "exercises" && <Brain className="h-3 w-3 mr-1" />}
                  {tab === "all-files" && <Upload className="h-3 w-3 mr-1" />}
                  {tab === "my-learning" && <Star className="h-3 w-3 mr-1" />}
                  {tab === "students" && <Users className="h-3 w-3 mr-1" />}
                {tab.replace("-", " ")}
              </Button>
            ))}
          </div>

          {/* Overview Tab - Redesigned */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* Course Information Section */}
              <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-purple-400" />
                    Course Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-white/60 text-sm">Professor</p>
                      <p className="text-white font-medium">{course.professor_name || "TBA"}</p>
                    </div>
                    <div>
                      <p className="text-white/60 text-sm">Semester</p>
                      <p className="text-white font-medium">{course.semester}</p>
                    </div>
                    <div>
                      <p className="text-white/60 text-sm">University</p>
                      <p className="text-white font-medium">{course.university}</p>
                    </div>
                  </div>
                  {course.description && (
                    <div>
                      <p className="text-white/60 text-sm mb-2">Description</p>
                      <p className="text-white/80 text-sm leading-relaxed">{course.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recent Activity & Pinned Resources Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Activity */}
                <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Clock className="h-5 w-5 text-purple-400" />
                      Recent Activity
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentActivity.map((activity, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                          <div className="text-lg mt-0.5">{activity.icon}</div>
                          <div className="flex-1">
                            <p className="text-white text-sm">
                              <span className="font-medium">{activity.user}</span>
                              <span className="text-white/80"> {activity.action} </span>
                              {activity.item && (
                                <span className="text-purple-300 font-medium">{activity.item}</span>
                              )}
                            </p>
                            <p className="text-white/50 text-xs mt-1">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Pinned Resources */}
                <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      📌 Pinned Resources
                    </CardTitle>
                    <CardDescription className="text-white/70">
                      Most liked and popular study materials
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {pinnedResources.map((resource) => (
                      <div 
                        key={resource.id}
                        className="p-3 rounded-lg bg-gradient-to-r from-amber-500/10 to-yellow-500/10 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 cursor-pointer group"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-lg flex items-center justify-center text-white">
                              <FileText className="h-5 w-5" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="text-white font-medium group-hover:text-amber-300 transition-colors text-sm">
                                  {resource.title}
                              </h4>
                                <Badge variant="outline" className="border-amber-400 text-amber-400 text-xs">
                                  📌
                                </Badge>
                            </div>
                              <div className="flex items-center gap-4 text-xs text-white/60">
                                <span>❤️ {resource.likes}</span>
                                <span>📥 {resource.downloads}</span>
                                <span>By {resource.uploadedBy}</span>
                          </div>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Button size="sm" variant="outline" className="border-amber-400/30 text-amber-400 hover:bg-amber-400/10 h-8 w-8 p-0">
                              <Download className="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="outline" className="border-amber-400/30 text-amber-400 hover:bg-amber-400/10 h-8 w-8 p-0">
                              <Eye className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    <Button 
                      variant="outline"
                      className="w-full border-amber-400/30 text-amber-400 hover:bg-amber-400/10 mt-4"
                    >
                      <Star className="w-4 h-4 mr-2" />
                      View All Popular Resources
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Discussion Forums - Functional Interface */}
              <ForumInterface courseId={course.id} user={user} />



              {/* Study Session Organizer */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      📚 Study Sessions
                    </CardTitle>
                    <CardDescription className="text-white/70">
                      Join or organize study groups
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {studySessions.map((session) => (
                      <div 
                        key={session.id}
                        className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer group ${
                          session.type === 'live' 
                            ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-400/20 hover:border-green-400/40' 
                            : session.type === 'scheduled'
                            ? 'bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border-blue-400/20 hover:border-blue-400/40'
                            : 'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-400/20 hover:border-purple-400/40'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                              session.type === 'live' 
                                ? 'bg-green-500/20 text-green-400' 
                                : session.type === 'scheduled'
                                ? 'bg-blue-500/20 text-blue-400'
                                : 'bg-purple-500/20 text-purple-400'
                            }`}>
                              {session.type === 'live' ? '🔴 LIVE' : session.type === 'scheduled' ? '📅 Scheduled' : '🔄 Recurring'}
                          </div>
                            <h4 className="text-white font-medium">{session.title}</h4>
                        </div>
                          <div className="text-white/60 text-sm">
                            {session.participants}/{session.maxParticipants}
                          </div>
                        </div>
                        
                        <p className="text-white/70 text-sm mb-2">{session.description}</p>
                        
                        <div className="flex items-center justify-between text-xs">
                          <div className="text-white/60">
                            📍 {session.location} • ⏱️ {session.duration}
                          </div>
                          <div className="text-white/60">
                            Host: {session.host}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 mt-3">
                          {session.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="border-white/20 text-white/60 text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                    
                    <div className="flex gap-2 mt-4">
                      <Button 
                        className="flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white"
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        Schedule Session
                      </Button>
                      <Button 
                        variant="outline"
                        className="flex-1 border-white/20 text-white hover:bg-white/10"
                      >
                        <Users className="w-4 h-4 mr-2" />
                        Find Study Buddy
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Course Leaderboard */}
              <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                      🏆 Course Leaderboard
                  </CardTitle>
                    <CardDescription className="text-white/70">
                      Top contributors and achievers
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                      {/* Top Contributors */}
                      <div>
                        <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-green-400" />
                          Top Contributors
                        </h5>
                        <div className="space-y-2">
                          {leaderboard.topContributors.slice(0, 3).map((contributor) => (
                            <div key={contributor.rank} className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                              <div className="flex items-center gap-3">
                                <div className="text-lg">{contributor.badge}</div>
                                <div>
                                  <div className="text-white font-medium text-sm">{contributor.name}</div>
                                  <div className="text-white/60 text-xs">{contributor.contributions} contributions</div>
                                </div>
                              </div>
                              <div className="text-green-400 font-bold text-sm">{contributor.points}pts</div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Study Streaks */}
                      <div>
                        <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                          <Award className="h-4 w-4 text-orange-400" />
                          Study Streaks
                        </h5>
                        <div className="space-y-2">
                          {leaderboard.studyStreaks.map((streak) => (
                            <div key={streak.rank} className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                              <div className="flex items-center gap-3">
                                <div className="text-lg">{streak.badge}</div>
                                <div className="text-white font-medium text-sm">{streak.name}</div>
                              </div>
                              <div className="text-orange-400 font-bold text-sm">{streak.streak} {streak.days}</div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Exercise Champions */}
                      <div>
                        <h5 className="text-white font-medium mb-3 flex items-center gap-2">
                          <Target className="h-4 w-4 text-purple-400" />
                          Exercise Champions
                        </h5>
                        <div className="space-y-2">
                          {leaderboard.exerciseChampions.map((champion) => (
                            <div key={champion.rank} className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                              <div className="flex items-center gap-3">
                                <div className="text-lg">{champion.badge}</div>
                                <div>
                                  <div className="text-white font-medium text-sm">{champion.name}</div>
                                  <div className="text-white/60 text-xs">{champion.exercises} exercises</div>
                                </div>
                              </div>
                              <div className="text-purple-400 font-bold text-sm">{champion.score}%</div>
                            </div>
                          ))}
                        </div>
                      </div>

                    <Button 
                      variant="outline"
                        className="w-full border-white/20 text-white hover:bg-white/10 mt-4"
                    >
                        <Award className="w-4 h-4 mr-2" />
                        View Full Leaderboard
                    </Button>
                  </div>
                </CardContent>
              </Card>
              </div>
            </div>
          )}

          {/* Files Tab */}
          {activeTab === "all-files" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-white">Course Materials</h3>
                {(isOwner || isEnrolled) && (
                  <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload File
                  </Button>
                )}
              </div>
              
              <div className="grid gap-4">
                {mockFiles.map((file) => (
                  <Card key={file.id} className="bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <FileText className="h-5 w-5 text-blue-400" />
                          </div>
                          <div>
                            <h4 className="text-white font-medium">{file.name}</h4>
                            <p className="text-white/70 text-sm">
                              {file.size} • Uploaded by {file.uploaded_by} • {new Date(file.uploaded_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10">
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Lecture Notes Tab */}
          {activeTab === "lecture-notes" && (
            <LectureNotesTab 
              courseId={course.id}
              user={user}
              isEnrolled={isEnrolled}
            />
          )}

                     {/* Exercises Tab */}
           {activeTab === "exercises" && (
             <div className="space-y-6">
               <div className="flex items-center justify-between">
                 <h3 className="text-xl font-bold text-white">Course Exercises</h3>
                 <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
                   <Target className="w-4 h-4 mr-2" />
                   Create Exercise
                 </Button>
               </div>
               
               <div className="grid gap-4">
                 {/* Mock exercises would go here */}
                 <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                   <CardContent className="p-4 text-center">
                     <h4 className="text-white font-medium">No exercises yet!</h4>
                     <p className="text-white/70 text-sm">Be the first to create one.</p>
                   </CardContent>
                 </Card>
               </div>
             </div>
           )}

           {/* My Learning Tab */}
           {activeTab === "my-learning" && (
             <div className="space-y-6">
               <div className="flex items-center justify-between">
                 <div>
                   <h3 className="text-xl font-bold text-white">My Learning Path</h3>
                   <p className="text-white/70 text-sm">Your personalized collection of study materials</p>
                 </div>
                 <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
                   <Star className="w-4 h-4 mr-2" />
                   Create Custom Path
                 </Button>
               </div>

               {/* Learning Progress Overview */}
               <Card className="bg-white/5 backdrop-blur-xl border-white/10">
                 <CardHeader>
                   <CardTitle className="text-white flex items-center gap-2">
                     <Target className="h-5 w-5 text-purple-400" />
                     Your Progress
                   </CardTitle>
                 </CardHeader>
                 <CardContent className="space-y-4">
                   <div className="space-y-2">
                     <div className="flex justify-between text-sm">
                       <span className="text-white/80">Learning Path Completion</span>
                       <span className="text-white font-medium">3/8 items (38%)</span>
                     </div>
                     <Progress value={38} className="h-3" />
                   </div>
                   
                   <div className="grid grid-cols-3 gap-4 pt-4">
                     <div className="text-center">
                       <div className="text-2xl font-bold text-white">5</div>
                       <div className="text-xs text-white/70">Files Saved</div>
                     </div>
                     <div className="text-center">
                       <div className="text-2xl font-bold text-white">2</div>
                       <div className="text-xs text-white/70">Exercises Added</div>
                     </div>
                     <div className="text-center">
                       <div className="text-2xl font-bold text-white">4.2h</div>
                       <div className="text-xs text-white/70">Time Spent</div>
                     </div>
                   </div>
                 </CardContent>
               </Card>

               {/* Learning Path Items */}
               <div className="space-y-4">
                 <h4 className="text-lg font-semibold text-white">My Saved Materials</h4>
                 
                 {/* Mock learning path items */}
                 {[
                   {
                     id: "1",
                     type: "file",
                     title: "Introduction to Algorithms.pdf",
                     description: "Essential reading for understanding basic concepts",
                     status: "completed",
                     timeSpent: "45 min",
                     addedDate: "2 days ago"
                   },
                   {
                     id: "2", 
                     type: "exercise",
                     title: "Chapter 1 Quiz",
                     description: "Practice questions to test understanding",
                     status: "in_progress",
                     timeSpent: "15 min",
                     addedDate: "1 day ago"
                   },
                   {
                     id: "3",
                     type: "file", 
                     title: "Advanced Data Structures.pptx",
                     description: "Slides covering trees, graphs, and hash tables",
                     status: "not_started",
                     timeSpent: "0 min",
                     addedDate: "6 hours ago"
                   }
                 ].map((item) => (
                   <Card key={item.id} className="bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-200">
                     <CardContent className="p-4">
                       <div className="flex items-center justify-between">
                         <div className="flex items-center gap-4">
                           <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                             item.status === 'completed' ? 'bg-green-500/20' :
                             item.status === 'in_progress' ? 'bg-yellow-500/20' : 'bg-gray-500/20'
                           }`}>
                             {item.type === 'file' ? (
                               <FileText className={`h-6 w-6 ${
                                 item.status === 'completed' ? 'text-green-400' :
                                 item.status === 'in_progress' ? 'text-yellow-400' : 'text-gray-400'
                               }`} />
                             ) : (
                               <Brain className={`h-6 w-6 ${
                                 item.status === 'completed' ? 'text-green-400' :
                                 item.status === 'in_progress' ? 'text-yellow-400' : 'text-gray-400'
                               }`} />
                             )}
                           </div>
                           <div className="flex-1">
                             <div className="flex items-center gap-2 mb-1">
                               <h4 className="text-white font-medium">{item.title}</h4>
                               <Badge variant="outline" className={`text-xs border-current ${
                                 item.status === 'completed' ? 'text-green-400 border-green-400' :
                                 item.status === 'in_progress' ? 'text-yellow-400 border-yellow-400' : 'text-gray-400 border-gray-400'
                               }`}>
                                 {item.status.replace('_', ' ')}
                               </Badge>
                             </div>
                             <p className="text-white/70 text-sm mb-2">{item.description}</p>
                             <div className="flex items-center gap-4 text-xs text-white/50">
                               <span>Added {item.addedDate}</span>
                               <span>Time spent: {item.timeSpent}</span>
                             </div>
                           </div>
                         </div>
                         <div className="flex items-center gap-2">
                           <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10">
                             <Eye className="w-4 h-4" />
                           </Button>
                           <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10">
                             {item.status === 'completed' ? 
                               <Award className="w-4 h-4" /> : 
                               <Target className="w-4 h-4" />
                             }
                           </Button>
                           <Button size="sm" variant="outline" className="border-white/20 text-white hover:bg-white/10">
                             <MoreVertical className="w-4 h-4" />
                           </Button>
                         </div>
                       </div>
                     </CardContent>
                   </Card>
                 ))}

                 {/* Empty State */}
                 <Card className="bg-white/5 backdrop-blur-xl border-white/10 border-dashed">
                   <CardContent className="p-8 text-center">
                     <Star className="w-12 h-12 text-white/30 mx-auto mb-4" />
                     <h4 className="text-white font-medium mb-2">Build Your Learning Path</h4>
                     <p className="text-white/70 text-sm mb-4">
                       Browse course materials and add them to your personal learning collection
                     </p>
                     <div className="flex gap-3 justify-center">
                       <Button 
                         variant="outline"
                         className="border-white/20 text-white hover:bg-white/10"
                         onClick={() => setActiveTab("all-files")}
                       >
                         <Upload className="w-4 h-4 mr-2" />
                         Browse Files
                       </Button>
                       <Button 
                         variant="outline"
                         className="border-white/20 text-white hover:bg-white/10"
                         onClick={() => setActiveTab("exercises")}
                       >
                         <Brain className="w-4 h-4 mr-2" />
                         Browse Exercises
                       </Button>
                     </div>
                   </CardContent>
                 </Card>
               </div>
             </div>
           )}

          {/* Students Tab */}
          {activeTab === "students" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-white">Enrolled Students ({course.enrollment_count})</h3>
              </div>
              
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {enrolledUsers.map((enrollment) => (
                  <Card key={enrollment.id} className="bg-white/5 backdrop-blur-xl border-white/10">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {enrollment.profiles.name?.charAt(0) || 'A'}
                          </span>
                        </div>
                        <div className="flex-1">
                          <h4 className="text-white font-medium">
                            {enrollment.profiles.is_username_public 
                              ? enrollment.profiles.username 
                              : enrollment.profiles.name || 'Anonymous'
                            }
                          </h4>
                          {enrollment.is_tutor && (
                            <Badge variant="outline" className="border-yellow-400 text-yellow-400 text-xs">
                              Tutor
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>
      </FadeIn>
    </div>
  )
} 