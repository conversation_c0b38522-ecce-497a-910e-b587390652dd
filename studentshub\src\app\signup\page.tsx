"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { auth, db } from "@/lib/supabase"
import { 
  Brain, 
  Eye, 
  EyeOff, 
  Github, 
  Mail, 
  Users, 
  FileText, 
  Sparkles,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react"

export default function SignUpPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    university: "",
    yearOfStudy: "",
    major: "",
    bio: ""
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")
    setSuccess("")

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match")
      setIsLoading(false)
      return
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters")
      setIsLoading(false)
      return
    }

    try {
      // Sign up user with Supabase Auth
      const { data: authData, error: authError } = await auth.signUp(
        formData.email,
        formData.password,
        {
          name: formData.name,
          university: formData.university,
          year_of_study: formData.yearOfStudy,
          major: formData.major,
          bio: formData.bio
        }
      )

      if (authError) {
        setError(authError.message)
        setIsLoading(false)
        return
      }

      if (authData.user) {
        // Create profile in our profiles table
        const { error: profileError } = await db.createProfile(authData.user.id, {
          name: formData.name,
          university: formData.university,
          year_of_study: formData.yearOfStudy,
          major: formData.major,
          bio: formData.bio,
          avatar_url: null
        })

        if (profileError) {
          console.error("Profile creation error:", profileError)
          // Don't show this error to user as auth was successful
        }

        setSuccess("Account created successfully! Please check your email to verify your account.")
        
        // Redirect to login page after 2 seconds
        setTimeout(() => {
          router.push("/login")
        }, 2000)
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignUp = async () => {
    try {
      setError("")
      const { error } = await auth.signInWithGoogle()
      if (error) {
        setError(error.message)
      }
      // Note: User will be redirected by OAuth flow
    } catch (err) {
      setError("Failed to sign up with Google")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center p-4">
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-white/10 animate-pulse"
              style={{
                width: Math.random() * 4 + 1 + "px",
                height: Math.random() * 4 + 1 + "px",
                left: Math.random() * 100 + "%",
                top: Math.random() * 100 + "%",
                animationDelay: Math.random() * 5 + "s",
                animationDuration: Math.random() * 3 + 2 + "s"
              }}
            />
          ))}
        </div>
      </div>

      <div className="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 relative z-10">
        {/* Left Side - Form */}
        <Card className="glass-ultra border-0 backdrop-blur-xl">
          <CardHeader className="space-y-1">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center animate-glow">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-gradient-ultra">StudentsHub</span>
            </div>
            <CardTitle className="text-2xl font-bold text-white">Create your account</CardTitle>
            <CardDescription className="text-white/70">
              Join thousands of students already using AI to excel in their studies
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {error && (
              <div className="mb-4 p-3 rounded-lg bg-red-500/20 border border-red-500/30 flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-red-400" />
                <span className="text-red-300 text-sm">{error}</span>
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 rounded-lg bg-green-500/20 border border-green-500/30 flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-green-300 text-sm">{success}</span>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name" className="text-white">Full Name</Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-label-contrast">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="university" className="text-label-contrast">University</Label>
                  <Input
                    id="university"
                    name="university"
                    type="text"
                    required
                    value={formData.university}
                    onChange={handleInputChange}
                    className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="Harvard University"
                  />
                </div>
                <div>
                  <Label htmlFor="yearOfStudy" className="text-white">Year of Study</Label>
                  <Input
                    id="yearOfStudy"
                    name="yearOfStudy"
                    type="text"
                    required
                    value={formData.yearOfStudy}
                    onChange={handleInputChange}
                    className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="2nd Year"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="major" className="text-white">Major/Field of Study</Label>
                <Input
                  id="major"
                  name="major"
                  type="text"
                  required
                  value={formData.major}
                  onChange={handleInputChange}
                  className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                  placeholder="Computer Science"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="password" className="text-white">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      required
                      value={formData.password}
                      onChange={handleInputChange}
                      className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50 pr-10"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
                <div>
                  <Label htmlFor="confirmPassword" className="text-white">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    placeholder="••••••••"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bio" className="text-label-contrast">Bio (Optional)</Label>
                <textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  className="mt-1 w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder:text-white/50 resize-none"
                  placeholder="Tell us a bit about yourself..."
                  rows={2}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white py-3 rounded-lg shadow-ultra btn-improved"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full border-white/20" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-transparent px-2 text-white/50">Or continue with</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <Button 
                  onClick={handleGoogleSignUp}
                  variant="outline" 
                  className="border-white/25 text-high-contrast hover:bg-white/15 glass-premium btn-improved"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Google
                </Button>
                <Button variant="outline" className="border-white/25 text-high-contrast hover:bg-white/15 glass-premium btn-improved">
                  <Github className="w-4 h-4 mr-2" />
                  GitHub
                </Button>
              </div>
            </div>

            <div className="mt-6 text-center">
              <p className="text-white/70">
                Already have an account?{" "}
                <Link href="/login" className="text-purple-400 hover:text-purple-300 font-medium">
                  Sign in
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Right Side - Features */}
        <div className="space-y-6 lg:pl-8">
          <div className="text-center lg:text-left">
            <h2 className="text-4xl font-bold text-white mb-4">
              Start your <span className="text-gradient-ultra">AI-powered</span> learning journey
            </h2>
            <p className="text-white/70 text-lg">
              Join a community of students using AI to enhance their academic success
            </p>
          </div>

          <div className="grid gap-4">
            {[
              {
                icon: Brain,
                title: "AI Study Assistant",
                description: "Get instant answers from your course materials",
                badge: "Free"
              },
              {
                icon: FileText,
                title: "Smart File Processing",
                description: "Upload and organize your documents with AI",
                badge: "Popular"
              },
              {
                icon: Sparkles,
                title: "Content Generation",
                description: "Auto-generate notes, summaries, and flashcards",
                badge: "New"
              },
              {
                icon: Users,
                title: "Collaborative Learning",
                description: "Study with peers and share knowledge",
                badge: "Pro"
              }
            ].map((feature, index) => (
              <div 
                key={index} 
                className={`glass-premium p-4 rounded-xl card-hover-premium animate-slide-in-${index + 1}`}
              >
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <feature.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-high-contrast">{feature.title}</h3>
                      <Badge variant="secondary" className="badge-premium text-xs bg-purple-500/25 text-purple-200 border-purple-500/40">
                        {feature.badge}
                      </Badge>
                    </div>
                    <p className="text-medium-contrast text-sm" style={{ lineHeight: '1.5' }}>{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="glass-ultra p-6 rounded-xl text-center">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="text-2xl font-bold text-gradient-ultra">10K+</div>
                <div className="text-white/70 text-sm">Students</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gradient-ultra">500+</div>
                <div className="text-white/70 text-sm">Universities</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gradient-ultra">95%</div>
                <div className="text-white/70 text-sm">Satisfaction</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 