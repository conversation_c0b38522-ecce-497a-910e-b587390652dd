"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { FadeIn, StaggeredFadeIn } from "@/components/ui/fade-in"
import { 
  BookOpen,
  Plus,
  Search,
  Filter,
  Clock,
  Users,
  FileText,
  Brain,
  TrendingUp,
  Calendar,
  Star,
  MoreVertical,
  Upload,
  Play,
  Award,
  Target,
  Zap,
  Sparkles,
  ChevronRight,
  GraduationCap,
  BarChart3
} from "lucide-react"

interface Course {
  id: string
  title: string
  description: string
  progress: number
  totalFiles: number
  lastAccessed: string
  color: string
  professor: string
  semester: string
  enrollmentCount: number
  isStarred: boolean
}

interface DashboardContentProps {
  user?: any
  profile?: any
}

export function DashboardContent({ user, profile }: DashboardContentProps) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")

  // Mock data - in real app, this would come from API
  const courses: Course[] = [
    {
      id: "1",
      title: "Advanced Machine Learning",
      description: "Deep dive into neural networks, reinforcement learning, and modern AI techniques.",
      progress: 75,
      totalFiles: 24,
      lastAccessed: "2 hours ago",
      color: "from-blue-500 to-cyan-500",
      professor: "Prof. Dr. Schmidt",
      semester: "Winter 2024",
      enrollmentCount: 156,
      isStarred: true
    },
    {
      id: "2",
      title: "Data Structures & Algorithms",
      description: "Comprehensive study of efficient data structures and algorithmic problem solving.",
      progress: 92,
      totalFiles: 18,
      lastAccessed: "1 day ago",
      color: "from-purple-500 to-pink-500",
      professor: "Prof. Dr. Weber",
      semester: "Winter 2024",
      enrollmentCount: 203,
      isStarred: false
    },
    {
      id: "3",
      title: "Software Engineering",
      description: "Best practices in software development, testing, and project management.",
      progress: 45,
      totalFiles: 31,
      lastAccessed: "3 days ago",
      color: "from-green-500 to-emerald-500",
      professor: "Prof. Dr. Mueller",
      semester: "Winter 2024",
      enrollmentCount: 89,
      isStarred: true
    }
  ]

  const recentActivity = [
    {
      type: "file_upload",
      title: "Uploaded Neural Networks.pdf",
      course: "Advanced Machine Learning",
      time: "2 hours ago"
    },
    {
      type: "ai_query",
      title: "Asked about backpropagation",
      course: "Advanced Machine Learning", 
      time: "3 hours ago"
    },
    {
      type: "note_created",
      title: "Created study notes for midterm",
      course: "Data Structures & Algorithms",
      time: "1 day ago"
    }
  ]

  const stats = {
    totalCourses: courses.length,
    avgProgress: Math.round(courses.reduce((acc, course) => acc + course.progress, 0) / courses.length),
    totalFiles: courses.reduce((acc, course) => acc + course.totalFiles, 0),
    studyTime: "12.5h"
  }

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.professor.toLowerCase().includes(searchTerm.toLowerCase())
    
    if (selectedFilter === "all") return matchesSearch
    if (selectedFilter === "starred") return matchesSearch && course.isStarred
    if (selectedFilter === "recent") return matchesSearch && course.lastAccessed.includes("hour")
    
    return matchesSearch
  })

  const handleCourseClick = (courseId: string) => {
    router.push(`/courses/${courseId}`)
  }

  const handleBrowseCourses = () => {
    router.push("/courses")
  }

  return (
    <div className="p-4 md:p-6 space-y-8">
      {/* Welcome Header with Search */}
      <FadeIn delay={0.1} direction="down">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
              Welcome back, {profile?.name || user?.email?.split('@')[0] || 'Student'}! 👋
            </h1>
            <p className="text-white/70 text-base md:text-lg">
              Ready to continue your learning journey?
            </p>
          </div>
          <div className="flex items-center gap-3">
            {/* Search Bar */}
            <div className="relative w-80">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-white/50" />
              </div>
              <Input
                type="text"
                placeholder="Search courses, notes, files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-3 py-2 border border-white/20 rounded-lg bg-white/10 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
              />
            </div>
            <Button
              onClick={handleBrowseCourses}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white transform hover:scale-105 transition-all duration-200"
            >
              <Search className="h-4 w-4 mr-2" />
              Browse Courses
            </Button>
          </div>
        </div>
      </FadeIn>

      {/* Stats Overview */}
      <FadeIn delay={0.3}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          <Card className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border-blue-400/20 backdrop-blur-xl transform hover:scale-105 transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-200 text-sm font-medium">Total Courses</p>
                  <p className="text-2xl font-bold text-white">{stats.totalCourses}</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border-purple-400/20 backdrop-blur-xl transform hover:scale-105 transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-200 text-sm font-medium">Avg Progress</p>
                  <p className="text-2xl font-bold text-white">{stats.avgProgress}%</p>
                </div>
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-400/20 backdrop-blur-xl transform hover:scale-105 transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-200 text-sm font-medium">Online Students</p>
                  <p className="text-2xl font-bold text-white">247</p>
                </div>
                <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border-orange-400/20 backdrop-blur-xl transform hover:scale-105 transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-200 text-sm font-medium">Study Time</p>
                  <p className="text-2xl font-bold text-white">{stats.studyTime}</p>
                </div>
                <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </FadeIn>

      {/* Filters */}
      <FadeIn delay={0.4}>
        <div className="flex items-center justify-center px-4 py-2">
          <div className="flex items-center gap-3 px-2 py-1">
            {["all", "starred", "recent"].map((filter) => (
              <Button
                key={filter}
                variant={selectedFilter === filter ? "default" : "outline"}
                size="sm"
                className={cn(
                  "capitalize whitespace-nowrap transition-all duration-200 relative focus:outline-none focus:ring-0 hover:no-underline border-0 hover:border-0 focus:border-0",
                  "mx-1", // Add horizontal margin to prevent overlap during scaling
                  selectedFilter === filter
                    ? "bg-purple-500 text-white scale-105 shadow-lg"
                    : "border-white/20 text-white/80 hover:bg-white/10 hover:scale-105 hover:shadow-md"
                )}
                onClick={() => setSelectedFilter(filter)}
              >
                {filter === "starred" && <Star className="h-3 w-3 mr-1" />}
                {filter === "recent" && <Clock className="h-3 w-3 mr-1" />}
                {filter}
              </Button>
            ))}
          </div>
        </div>
      </FadeIn>

      {/* Courses Grid */}
      <FadeIn delay={0.5}>
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl md:text-2xl font-bold text-white">Your Courses</h2>
            <Button
              variant="outline"
              size="sm"
              className="border-white/20 text-white/80 hover:bg-white/10 transform hover:scale-105 transition-all duration-200"
              onClick={() => router.push("/courses")}
            >
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
          {filteredCourses.map((course) => (
            <Card
              key={course.id}
              className="bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/20 transition-all duration-300 cursor-pointer group hover:scale-[1.02] hover:bg-white/10"
              onClick={() => handleCourseClick(course.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <div className={cn(
                        "w-3 h-3 rounded-full bg-gradient-to-r",
                        course.color
                      )} />
                      {course.isStarred && (
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                      )}
                    </div>
                    <CardTitle className="text-white group-hover:text-white text-lg leading-tight">
                      {course.title}
                    </CardTitle>
                    <CardDescription className="text-white/70 mt-1 line-clamp-2">
                      {course.description}
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-white/60 hover:text-white hover:bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Progress */}
                  <div>
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span className="text-white/80">Progress</span>
                      <span className="text-white font-medium">{course.progress}%</span>
                    </div>
                    <Progress value={course.progress} className="h-2" />
                  </div>

                  {/* Course Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2 text-white/70">
                      <GraduationCap className="h-4 w-4" />
                      <span className="truncate">{course.professor}</span>
                    </div>
                    <div className="flex items-center gap-2 text-white/70">
                      <FileText className="h-4 w-4" />
                      <span>{course.totalFiles} files</span>
                    </div>
                    <div className="flex items-center gap-2 text-white/70">
                      <Calendar className="h-4 w-4" />
                      <span>{course.semester}</span>
                    </div>
                    <div className="flex items-center gap-2 text-white/70">
                      <Users className="h-4 w-4" />
                      <span className="flex items-center gap-1">
                        {course.enrollmentCount}
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                      </span>
                    </div>
                  </div>

                  {/* Last Accessed */}
                  <div className="flex items-center justify-between pt-2 border-t border-white/10">
                    <span className="text-xs text-white/60">
                      Last accessed {course.lastAccessed}
                    </span>
                    <Button
                      size="sm"
                      className="h-8 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                    >
                      <Play className="h-3 w-3 mr-1" />
                      Continue
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredCourses.length === 0 && (
          <Card className="bg-white/5 backdrop-blur-xl border-white/10 border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-purple-500/20 rounded-2xl flex items-center justify-center mb-4">
                <BookOpen className="h-8 w-8 text-purple-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No courses found</h3>
              <p className="text-white/70 text-center mb-6 max-w-md">
                {searchTerm ? "Try adjusting your search terms or filters." : "Start your learning journey by creating your first course."}
              </p>
              <Button
                onClick={handleBrowseCourses}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Course
              </Button>
            </CardContent>
          </Card>
        )}
        </div>
      </FadeIn>

      {/* Recent Activity */}
      <FadeIn delay={0.6}>
        <div>
          <h2 className="text-xl md:text-2xl font-bold text-white mb-6">Recent Activity</h2>
        <Card className="bg-white/5 backdrop-blur-xl border-white/10">
          <CardContent className="p-6">
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/5 transition-colors">
                  <div className={cn(
                    "w-10 h-10 rounded-xl flex items-center justify-center",
                    activity.type === "file_upload" && "bg-blue-500/20",
                    activity.type === "ai_query" && "bg-purple-500/20",
                    activity.type === "note_created" && "bg-green-500/20"
                  )}>
                    {activity.type === "file_upload" && <Upload className="h-5 w-5 text-blue-400" />}
                    {activity.type === "ai_query" && <Brain className="h-5 w-5 text-purple-400" />}
                    {activity.type === "note_created" && <FileText className="h-5 w-5 text-green-400" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-white font-medium">{activity.title}</p>
                    <p className="text-white/70 text-sm">{activity.course}</p>
                  </div>
                  <span className="text-white/60 text-sm">{activity.time}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        </div>
      </FadeIn>
    </div>
  )
} 