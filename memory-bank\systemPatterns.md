# StudentsHub - System Patterns & Architecture

## Overall Architecture Pattern

### Microservices-Inspired Modular Architecture

Frontend (Next.js) ↔ API Layer (Supabase) ↔ AI Services (Gemini + Others)
↓
Vector Database (pgvector)
↓
File Storage (Supabase Storage)

## 🆕 Course Management System Patterns

### 🔥 LATEST: Collaborative Learning Hub Patterns

#### 1. Student-Centric Course Page Pattern
**Purpose**: Transform course pages from professor-led to student-collaborative learning hubs

**Implementation**:
```
Course Page Architecture:
Overview (Community Hub) → Lecture Notes → Exercises → All Files → My Learning → Students
    ↓                        ↓              ↓           ↓            ↓             ↓
Forums & Activity     Student Materials  AI-Generated  File Sharing  Personal    Community
Discussion Spaces     Collaborative      Practice      & Preview     Study       Members
Quick Actions         Content            Exercises     Downloads     Paths       Tracking
```

**Key Features**:
- **Community-First**: Discussion forums and collaborative spaces take priority
- **Student Content**: Emphasizes student-uploaded materials over professor content
- **Personal Learning**: Individual study path curation and progress tracking
- **AI Integration**: Smart exercise generation and content recommendations

#### 2. My Learning Path Pattern
**Purpose**: Personal study collection and progress tracking system

**Implementation**:
```typescript
// Learning path structure
LearningPath {
  id: UUID
  user_id: UUID  
  course_id: UUID
  title: string
  items: LearningPathItem[]
  progress: ProgressTracking
}

LearningPathItem {
  item_type: 'file' | 'exercise' | 'note' | 'external_link'
  item_id: UUID
  progress_status: 'not_started' | 'in_progress' | 'completed' | 'skipped'
  time_spent: number
  custom_notes: string
}
```

**Database Pattern**:
- **Auto-Creation**: Default learning path created on course enrollment
- **Multi-Content Support**: Files, exercises, notes, external links
- **Progress Tracking**: Automatic calculation of completion percentages
- **Time Analytics**: Detailed time spent monitoring per item

#### 3. Community Discussion Pattern
**Purpose**: Foster collaborative learning through structured discussions

**Implementation**:
```typescript
// Forum structure for course collaboration
DiscussionForums = {
  'general': 'Main course discussions and casual chat',
  'questions': 'Q&A support and peer help',
  'study-groups': 'Study session organization',
  'announcements': 'Important course updates'
}
```

**Features**:
- **Threaded Discussions**: Organized conversation topics
- **Real-time Activity**: Live activity feed with collaborative contributions
- **Peer Support**: Student-to-student help and knowledge sharing
- **Community Stats**: Track engagement and participation levels

#### 4. AI Exercise Generation Pattern
**Purpose**: Create personalized practice exercises from course materials

**Implementation**:
```typescript
// Exercise generation workflow
File Upload → Text Extraction → Content Analysis → AI Processing → Exercise Creation
      ↓              ↓               ↓               ↓              ↓
   PDF/DOCX      Extract Text    Topic Detection   Gemini API    Multiple Choice
   PowerPoint    OCR Images      Difficulty Level  Question Gen   True/False
   Images/Notes  Structure Data  Learning Goals    Answer Keys    Open Questions
```

**Exercise Types Supported**:
- **Multiple Choice**: AI generates questions with distractors
- **True/False**: Statement validation from content
- **Open Questions**: Essay-style questions for deeper understanding
- **Fill in the Blank**: Cloze-style exercises for key concepts
- **Matching**: Connect concepts, terms, and definitions

### 5. Rich Text Forum Editor Pattern
**Purpose**: Professional forum messaging with rich formatting and media support

**Implementation**:
```typescript
// ContentEditable-based rich text editor
<div
  contentEditable
  ref={editorRef}
  className="rich-text-editor"
  onInput={updateContent}
  dangerouslySetInnerHTML={{ __html: content }}
/>

// Formatting toolbar
const formatButtons = [
  { cmd: 'bold', icon: Bold },
  { cmd: 'italic', icon: Italic },
  { cmd: 'underline', icon: Underline },
  { cmd: 'insertUnorderedList', icon: List },
  { cmd: 'insertOrderedList', icon: ListOrdered },
  { cmd: 'formatBlock', value: 'blockquote', icon: Quote }
]
```

**Features**:
- **WYSIWYG Editing**: Real-time HTML formatting with visual feedback
- **Image Embedding**: Direct Supabase Storage upload with blob URL management
- **File References**: Link to course files without duplicate storage
- **Emoji Picker**: 80+ emojis in organized grid layout
- **Auto-resize**: Dynamic height adjustment based on content
- **Mobile Optimized**: Touch-friendly controls and responsive design

### 6. Image Storage Pattern
**Purpose**: Efficient image handling with local preview and cloud storage

**Implementation**:
```typescript
// Image upload workflow
File Selection → Local Blob URL → Preview → Send Message → Upload to Supabase → Replace URLs

const uploadImageToSupabase = async (file: File): Promise<string | null> => {
  const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36)}.${ext}`
  const { data } = await supabase.storage
    .from('forum-images')
    .upload(fileName, file)
  
  return supabase.storage
    .from('forum-images')
    .getPublicUrl(fileName).data.publicUrl
}

// Blob management for local preview
const createLocalPreview = (file: File) => {
  const localUrl = URL.createObjectURL(file)
  // Store in pendingImages for later cleanup
  setPendingImages(prev => [...prev, { file, localUrl }])
  return localUrl
}
```

**Key Features**:
- **Local Preview**: Instant image display using blob URLs
- **Deferred Upload**: Images only uploaded when message is sent
- **Error Recovery**: Graceful handling of upload failures
- **Memory Management**: Automatic blob URL cleanup to prevent memory leaks
- **User Organization**: Images stored in user-specific folders

### 7. File Reference Pattern  
**Purpose**: Link to existing course materials without storage duplication

**Implementation**:
```typescript
// File reference insertion
const insertFileReference = (file: CourseFile) => {
  const fileRef = document.createElement('span')
  fileRef.className = 'file-reference'
  fileRef.innerHTML = `📄 ${file.name}`
  fileRef.setAttribute('data-file-id', file.id)
  
  // Insert at cursor position
  const selection = window.getSelection()
  const range = selection.getRangeAt(0)
  range.insertNode(fileRef)
}

// Database storage
{
  content: "Check out this resource: <span class='file-reference' data-file-id='123'>📄 Lecture Notes.pdf</span>",
  referenced_files: [{ file_id: '123', file_name: 'Lecture Notes.pdf' }]
}
```

**Benefits**:
- **Storage Efficiency**: No duplicate file uploads
- **Contextual Links**: Visual file references in messages
- **Access Control**: Leverages existing course file permissions
- **Rich Display**: Icons and metadata for better UX

### 8. Three-Tier Forum Layout Pattern
**Purpose**: Organized forum interface with flexible visibility

**Layout Structure**:
```
[Categories: 272px] [Topics: 264px (hideable)] [Chat: flex-1]
      ↓                    ↓                      ↓
   Announcements      Topic Search           Message Area
   General Chat       Topic List             Rich Text Editor
   Q&A Forum         Create Topic           Action Buttons
   Online Users      Topic Status           Emoji/File Tools
```

**Responsive Behavior**:
- **Desktop**: All three panels visible
- **Tablet**: Topics panel hideable with eye icon
- **Mobile**: Stack panels with tab navigation
- **Touch Optimized**: Minimum 44px touch targets
- **True/False**: Statement validation from content
- **Open Questions**: Essay-style questions for deeper understanding
- **Fill in the Blank**: Cloze-style exercises for key concepts
- **Matching**: Connect concepts, terms, and definitions

**Database Pattern**:
- **Flexible Content Storage**: JSONB for various exercise formats
- **Source Tracking**: Links exercises back to source files
- **Difficulty Levels**: Easy, Medium, Hard for progressive learning
- **Attempt Tracking**: Student performance and progress monitoring
- **AI Attribution**: Mark AI-generated vs. student-created exercises

#### 5. "Add to My Learning" Pattern
**Purpose**: Seamless integration of course materials into personal study collections

**Implementation**:
```typescript
// Add to learning workflow
[File/Exercise Card] → "Add to My Learning" → Select Learning Path → Add with Notes
         ↓                      ↓                    ↓                ↓
   Course Material      User Action          Choose Destination    Optional Description
   Display Context      Button Click         Default or Custom     Save to Collection
```

**Features**:
- **Contextual Actions**: Add buttons throughout course interface
- **Smart Defaults**: Auto-add to default learning path
- **Custom Organization**: Multiple learning paths per course
- **Progress Integration**: Automatic status tracking when items accessed

### 1. Two-Tab Discovery Pattern
**Purpose**: Separate course discovery from personal course management

**Implementation**:
```typescript
// Tab structure for optimal user flow
[Discover Courses] [My Courses]
      ↓                ↓
  Public courses    Enrolled courses
  Search/Filter     Personal progress
  Enrollment        Management
```

**Key Features**:
- **Discover Tab**: Search public courses, enroll, create new
- **My Courses Tab**: View enrolled courses, track progress, manage
- **Unified Interface**: Consistent UI across both tabs
- **Real-time Counts**: Dynamic enrollment numbers

### 2. Smart Enrollment Pattern
**Purpose**: Automatic course creator enrollment and proper button states

**Implementation**:
```typescript
// Auto-enrollment workflow
Course Creation → Auto-enroll Creator → Update UI State
      ↓
Button Logic: isOwner ? "Open Course" : isEnrolled ? "Open Course" : "Enroll"
```

**Business Rules**:
- **Creator Auto-enrollment**: Course creators automatically enrolled
- **Dynamic Buttons**: Context-aware action buttons
- **State Consistency**: Enrollment state reflected across all components
- **Unenroll Protection**: Course owners cannot unenroll from own courses

### 3. Dynamic Data Counting Pattern
**Purpose**: Real-time enrollment counts without stale cache data

**Implementation**:
```typescript
// Dynamic enrollment counting
getPublicCourses() {
  SELECT courses.*, 
         COUNT(course_enrollments.id) as enrollment_count
  FROM courses 
  LEFT JOIN course_enrollments ON courses.id = course_enrollments.course_id
  GROUP BY courses.id
}

getEnrolledCourses() {
  // Same pattern for consistency
  SELECT courses.*, 
         COUNT(ce.id) as enrollment_count
  FROM course_enrollments enrollment
  JOIN courses ON enrollment.course_id = courses.id
  LEFT JOIN course_enrollments ce ON courses.id = ce.course_id
  GROUP BY courses.id
}
```

### 4. Flexible Course Code Pattern
**Purpose**: Support university integration while allowing private courses

**Validation Logic**:
```typescript
// Dynamic validation based on course visibility
if (isPublic) {
  courseCode: required("Course code required for public courses")
  duplicateCheck: checkCourseExists(courseCode, university)
} else {
  courseCode: optional("Course code optional for private courses")
}
```

**Benefits**:
- **University Integration**: Public courses require standard codes
- **Private Flexibility**: Personal courses don't need institutional codes
- **Duplicate Prevention**: Automatic checking for public course conflicts
- **International Support**: Multiple format support (TU format, standard format)

### 5. Real-time Session Tracking Pattern
**Purpose**: Track active users on course pages for community engagement

**Implementation**:
```typescript
// Session tracking lifecycle
Course Page Load → updateUserSession(userId, courseId, true)
      ↓
Every 30 seconds → updateUserSession(userId, courseId, true)
      ↓
Page Unload → updateUserSession(userId, courseId, false)

// Online user query
getOnlineUsers(courseId) {
  SELECT * FROM user_sessions 
  WHERE course_id = courseId 
    AND is_online = true 
    AND last_seen > (NOW() - INTERVAL '5 minutes')
}
```

**Features**:
- **Real-time Presence**: 30-second update intervals
- **Activity Window**: 5-minute threshold for online status
- **Automatic Cleanup**: Offline marking on page leave
- **Course-specific**: Track presence per course, not globally

### 6. Smart Filter System Pattern
**Purpose**: Intelligent course filtering with dynamic thresholds

**Filter Logic**:
```typescript
// Popular course algorithm
const popularThreshold = Math.max(
  Math.ceil(totalCourses * 0.25), // Top 25% by enrollment
  10 // Minimum 10 students for popular status
)

const filters = {
  all: () => true,
  enrolled: (course) => userEnrollments.includes(course.id),
  popular: (course) => course.enrollment_count >= popularThreshold,
  new: (course) => isWithinDays(course.created_at, 7)
}
```

**Advanced Features**:
- **Dynamic Thresholds**: Popular based on course ecosystem size
- **Multiple Criteria**: Time-based, enrollment-based, user-based filters
- **Visual Feedback**: Clear indication of active filters
- **Search Integration**: Filters work with real-time search

## 🆕 UI/UX Design Patterns

### 1. True Three-Column Dashboard Layout
**Purpose**: Organize functionality into logical, accessible sections with proper space allocation

**Implementation**:
```
[Navigation Sidebar] [Main Content] [AI Chat Sidebar]
     264px              Flexible         384px
     (w-64)           (flex-1)         (w-96)
```

**Architecture Details**:
- **No Overlays**: Sidebars take actual width space, content flows naturally
- **Fixed Height**: h-screen prevents browser scrollbar conflicts
- **Individual Scrolling**: Only middle section scrolls with positioned scrollbar
- **Floating Toggles**: Bottom-left circular buttons for elegant UX

**Key Features**:
- **Left Panel**: User profile, navigation menu, quick actions
- **Center Panel**: Primary content (courses, stats, search) with own scrollbar
- **Right Panel**: AI assistant with MessageCircle icon and chat interface
- **Responsive Behavior**: Collapsible on mobile with overlay
- **State Management**: Individual sidebar visibility control with proper transitions

### 2. Floating Action System Pattern
**Purpose**: Provide elegant, non-intrusive access to secondary actions

**Implementation**:
- **Position**: Fixed bottom-left with z-index layering
- **Navigation Toggle**: Menu icon for left sidebar access
- **AI Toggle**: MessageCircle icon for right sidebar access
- **Visual Design**: Circular buttons with gradient backgrounds and hover effects
- **Responsive**: Only appears when corresponding sidebar is closed

**Benefits**:
- **Non-Overlapping**: Doesn't interfere with main content
- **Intuitive**: Clear iconography and positioning
- **Elegant**: Smooth animations and professional appearance

### 3. Progressive Disclosure Pattern
**Purpose**: Show information hierarchically to reduce cognitive load

**Implementation**:
- **Stats Cards**: Overview metrics at top level
- **Course Cards**: Detailed info revealed on hover/focus
- **Filter Tabs**: Content filtered based on user selection
- **Expandable Sections**: Additional details available on demand

### 3. Visual Hierarchy Pattern
**Purpose**: Guide user attention to most important elements

**Implementation**:
```
Welcome Header (Large, Bold)
  ↓
Stats Overview (Prominent Cards)
  ↓
Course Grid (Primary Content)
  ↓
Recent Activity (Secondary)
```

### 4. State Indication Pattern
**Purpose**: Provide clear visual feedback for system status

**Visual Indicators**:
- **Online Status**: Green pulsing dots for active students
- **Progress Bars**: Course completion visualization
- **Loading States**: Skeleton screens and spinners
- **Hover Effects**: Scale transforms and color changes
- **Active States**: Selected filters and buttons

### 5. Responsive Adaptation Pattern
**Purpose**: Maintain functionality across all device sizes

**Breakpoint Strategy**:
```typescript
// Tailwind breakpoints used
sm: 640px   // Small tablets
md: 768px   // Tablets
lg: 1024px  // Small laptops (sidebar visibility)
xl: 1280px  // Desktop
```

**Adaptive Behaviors**:
- **Sidebars**: Auto-collapse below 1024px
- **Grid Layout**: Responsive column counts
- **Search Bar**: Adjusts width on smaller screens
- **Touch Targets**: Minimum 44px for mobile accessibility

## Core Design Patterns

### 1. RAG (Retrieval-Augmented Generation) Pattern
**Purpose**: Enhance AI responses with relevant context from user's documents

**Implementation**:
```typescript
// RAG Pipeline Flow
Document Upload → Text Extraction → Chunking → Embedding → Vector Storage
User Query → Query Embedding → Vector Search → Context Retrieval → LLM + Context → Response
```

**Components**:
- Document Processor: Extracts and chunks text from various file formats
- Embedding Generator: Creates vector representations of text chunks
- Vector Store: pgvector database for similarity search
- Context Retriever: Finds most relevant chunks for queries
- Response Generator: Combines context with LLM to create answers

### 2. Provider Abstraction Pattern
**Purpose**: Support multiple AI providers without vendor lock-in

**Implementation**:
```typescript
interface AIProvider {
  generateResponse(prompt: string, context: string[]): Promise<AIResponse>
  generateEmbedding(text: string): Promise<number[]>
  summarize(content: string): Promise<string>
}

class GeminiProvider implements AIProvider { ... }
class OpenAIProvider implements AIProvider { ... }
class AnthropicProvider implements AIProvider { ... }
```

### 3. Repository Pattern for Data Access
**Purpose**: Abstract database operations and enable easy testing

**Implementation**:
```typescript
interface CourseRepository {
  create(course: Course): Promise<Course>
  findByUserId(userId: string): Promise<Course[]>
  update(id: string, updates: Partial<Course>): Promise<Course>
  delete(id: string): Promise<void>
}

class SupabaseCourseRepository implements CourseRepository { ... }
```

### 4. Event-Driven Architecture for File Processing
**Purpose**: Handle file uploads and processing asynchronously

**Flow**:
File Upload → Supabase Storage → Trigger → Edge Function → Processing Queue
↓
Document Processing ← Background Job ← Queue Processing ← Processing Queue
↓
Embedding Generation → Vector Storage → Update Status → Notify Frontend



## Database Patterns

### 1. Multi-Tenant Data Isolation
**Pattern**: Row Level Security (RLS) in Supabase
```sql
-- Ensure users can only access their own data
CREATE POLICY "Users can only see own courses" ON courses
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only see own files" ON files
  FOR ALL USING (auth.uid() = (SELECT user_id FROM courses WHERE id = course_id));
```

### 2. Vector Similarity Search Pattern
**Implementation**:
```sql
-- Find similar content using pgvector
SELECT file_id, chunk_text, 
       1 - (embedding <=> query_embedding) as similarity
FROM embeddings
WHERE 1 - (embedding <=> query_embedding) > 0.7
ORDER BY embedding <=> query_embedding
LIMIT 10;
```

### 3. Soft Delete Pattern
**Purpose**: Maintain data integrity and enable recovery
```sql
-- Add deleted_at column to important tables
ALTER TABLE courses ADD COLUMN deleted_at TIMESTAMP;
ALTER TABLE files ADD COLUMN deleted_at TIMESTAMP;

-- Filter out deleted records in queries
SELECT * FROM courses WHERE deleted_at IS NULL;
```

## Frontend Patterns

### 1. Container-Presenter Pattern
**Purpose**: Separate business logic from UI components

**Structure**:
```typescript
// Container Component (Business Logic)
const CourseListContainer = () => {
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)
  
  // Data fetching and state management
  return <CourseListPresenter courses={courses} loading={loading} />
}

// Presenter Component (Pure UI)
const CourseListPresenter = ({ courses, loading }: Props) => {
  // Only UI rendering logic
}
```

### 2. Custom Hooks Pattern
**Purpose**: Reusable business logic across components

**Examples**:
```typescript
// Custom hooks for common operations
const useCourses = (userId: string) => { ... }
const useFileUpload = () => { ... }
const useAIQuery = () => { ... }
const useAuth = () => { ... }
```

### 3. Error Boundary Pattern
**Purpose**: Graceful error handling in React components

**Implementation**:
```typescript
class ErrorBoundary extends React.Component {
  // Catch and handle component errors gracefully
  // Log errors to monitoring service
  // Show user-friendly error messages
}
```

## Security Patterns

### 1. Zero-Trust Security Model
**Principles**:
- Verify every request and user
- Use Row Level Security for data access
- Implement rate limiting on AI endpoints
- Sanitize and validate all user inputs

### 2. Authentication & Authorization Pattern
**Implementation**:
```typescript
// Middleware pattern for route protection
const withAuth = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const user = await verifyToken(req)
    if (!user) return res.status(401).json({ error: 'Unauthorized' })
    
    req.user = user
    return handler(req, res)
  }
}
```

### 3. Input Sanitization Pattern
**Purpose**: Prevent injection attacks and ensure data integrity

**Implementation**:
- Sanitize file uploads before processing
- Validate AI queries before sending to providers
- Escape user content before storage
- Use parameterized queries for database operations

## Performance Patterns

### 1. Caching Strategy
**Levels**:
- Browser Cache: Static assets and API responses
- CDN Cache: Global content distribution
- Application Cache: Frequently accessed data
- Database Cache: Query result caching

### 2. Lazy Loading Pattern
**Implementation**:
```typescript
// Component lazy loading
const CourseDetails = lazy(() => import('./CourseDetails'))

// Data lazy loading
const useInfiniteScroll = () => {
  // Load data as user scrolls
}
```

### 3. Optimistic Updates Pattern
**Purpose**: Improve perceived performance

**Example**:
```typescript
// Update UI immediately, rollback if API fails
const createNote = async (noteData: Note) => {
  // Optimistically add to UI
  addNoteToUI(noteData)
  
  try {
    await api.createNote(noteData)
  } catch (error) {
    // Rollback UI changes
    removeNoteFromUI(noteData.id)
    showError('Failed to create note')
  }
}
```

## AI Integration Patterns

### 1. Circuit Breaker Pattern
**Purpose**: Handle AI service failures gracefully

**Implementation**:
```typescript
class AICircuitBreaker {
  // Monitor AI service health
  // Fail fast when service is down
  // Gradually recover when service returns
}
```

### 2. Retry with Exponential Backoff
**Purpose**: Handle temporary AI service issues

**Implementation**:
```typescript
const retryWithBackoff = async (operation: () => Promise<any>, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await sleep(Math.pow(2, i) * 1000) // Exponential backoff
    }
  }
}
```

### 3. Response Streaming Pattern
**Purpose**: Improve user experience for long AI responses

**Implementation**:
```typescript
// Stream AI responses as they're generated
const streamAIResponse = async (query: string) => {
  const stream = await ai.generateStream(query)
  
  for await (const chunk of stream) {
    updateUIWithChunk(chunk)
  }
}
```

## Monitoring & Observability Patterns

### 1. Structured Logging Pattern
```typescript
const logger = {
  info: (message: string, context: object) => { ... },
  error: (message: string, error: Error, context: object) => { ... },
  warn: (message: string, context: object) => { ... }
}

// Usage
logger.info('AI query processed', { 
  userId, 
  queryType: 'document_search',
  responseTime: 1200,
  tokensUsed: 150 
})
```

### 2. Metrics Collection Pattern
**Key Metrics**:
- AI response quality scores
- Query response times
- User engagement metrics
- Error rates and types
- Resource usage patterns

### 3. Health Check Pattern
**Implementation**:
```typescript
// Regular health checks for all services
const healthChecks = {
  database: () => checkDatabaseConnection(),
  storage: () => checkStorageAccess(),
  ai: () => checkAIProviderStatus(),
  embeddings: () => checkVectorSearch()
}
```

These patterns provide a solid foundation for building a scalable, maintainable, and robust learning platform.
