"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { fileOperations } from "@/lib/supabase"
import { cn } from "@/lib/utils"
import {
  Upload,
  FileText,
  Image as ImageIcon,
  X,
  Loader2,
  BookOpen,
  Target,
  GraduationCap,
  MoreVertical
} from "lucide-react"

interface FileWithMetadata extends File {
  id: string
  category: string
  description: string
  page_count?: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  progress: number
  error?: string
  preview?: string
  uploadAsAnonymous?: boolean
  displayName?: string // Custom name for display/upload
}

interface EnhancedUploadModalProps {
  courseId: string
  onClose: () => void
  onUploadComplete: () => void
}

export function EnhancedUploadModal({ courseId, onClose, onUploadComplete }: EnhancedUploadModalProps) {
  const [files, setFiles] = useState<FileWithMetadata[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [currentStep, setCurrentStep] = useState<'select' | 'configure'>('select')

  const categories = [
    { id: 'lecture_notes', label: 'Lecture Notes', icon: BookOpen, description: 'Course lectures and presentations' },
    { id: 'summaries', label: 'Summaries', icon: FileText, description: 'Study summaries and notes' },
    { id: 'practice_materials', label: 'Practice Materials', icon: Target, description: 'Exercises and practice problems' },
    { id: 'exams', label: 'Exams', icon: GraduationCap, description: 'Past exams and test materials' },
    { id: 'others', label: 'Others', icon: MoreVertical, description: 'Other course materials' }
  ]

  // Accepted file types: PDF, Word documents, Images
  const acceptedFileTypes = {
    'application/pdf': ['.pdf'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp']
  }

  const maxFileSize = 50 * 1024 * 1024 // 50MB

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      console.warn('Some files were rejected:', rejectedFiles)
      // Could show toast notifications here
    }

    // Convert accepted files to our format
    const newFiles: FileWithMetadata[] = acceptedFiles.map((file) => ({
      ...file,
      id: Math.random().toString(36).substring(7),
      category: '', // No default category - user must select
      description: '',
      page_count: file.type === 'application/pdf' ? undefined : undefined,
      status: 'pending' as const,
      progress: 0,
      preview: file.type && file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
      uploadAsAnonymous: false,
      displayName: file.name // Use displayName for editable name
    }))

    setFiles(prev => [...prev, ...newFiles])

    // Move to configure step when files are added
    if (newFiles.length > 0) {
      setCurrentStep('configure')
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxFiles: 10,
    maxSize: maxFileSize,
    multiple: true
  })

  const updateFileMetadata = (fileId: string, updates: Partial<FileWithMetadata>) => {
    setFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, ...updates } : file
    ))
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const uploadFiles = async () => {
    if (files.length === 0) return

    setIsUploading(true)

    for (const file of files) {
      if (file.status === 'completed') continue

      try {
        // Update status to uploading
        updateFileMetadata(file.id, { status: 'uploading', progress: 0 })

        // Simulate progress
        for (let progress = 0; progress <= 90; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100))
          updateFileMetadata(file.id, { progress })
        }

        // Upload to Supabase
        const { error } = await fileOperations.uploadFile(courseId, file, {
          category: file.category,
          description: file.description || undefined,
          page_count: file.page_count || undefined,
          displayName: file.displayName || undefined
        })

                 if (error) {
           updateFileMetadata(file.id, { 
             status: 'error', 
             error: (error as any)?.message || 'Upload failed',
             progress: 0
           })
         } else {
           updateFileMetadata(file.id, { status: 'completed', progress: 100 })
         }
       } catch (error) {
         updateFileMetadata(file.id, { 
           status: 'error', 
           error: 'Upload failed',
           progress: 0
         })
      }
    }

    setIsUploading(false)

    // Check if all files uploaded successfully
    const allCompleted = files.every(f => f.status === 'completed')
    if (allCompleted) {
      onUploadComplete()
    }
  }

  const getFileIcon = (file: FileWithMetadata) => {
    // Add null/undefined check for file.type
    if (!file.type) return <FileText className="h-5 w-5 text-gray-500" />

    if (file.type.startsWith('image/')) return <ImageIcon className="h-5 w-5 text-green-500" />
    if (file.type.includes('pdf')) return <FileText className="h-5 w-5 text-red-500" />
    if (file.type.includes('word') || file.type.includes('document')) return <FileText className="h-5 w-5 text-blue-500" />
    return <FileText className="h-5 w-5 text-gray-500" />
  }



  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const canUpload = files.length > 0 && files.every(f => 
    f.category && f.category !== '' && f.status !== 'uploading'
  )

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-900 border-0 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-white text-xl font-bold">
            {currentStep === 'select' ? 'Upload Course Materials' : 'Configure Your Files'}
          </DialogTitle>
          <DialogDescription className="text-white/80 text-base">
            {currentStep === 'select'
              ? 'Upload PDF documents, Word files, and images to share with your classmates'
              : 'Set names, categories, and descriptions for your selected files'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* File Selection Step */}
          {currentStep === 'select' && (
            <Card className={cn(
              "border-2 border-dashed transition-colors bg-white/10 backdrop-blur-sm",
              isDragActive ? "border-purple-300 bg-purple-500/20" : "border-white/30 hover:border-purple-300"
            )}>
              <CardContent className="p-8">
                <div {...getRootProps()} className="cursor-pointer">
                  <input {...getInputProps()} />
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className={cn(
                      "rounded-full p-6 mb-4",
                      isDragActive ? "bg-purple-400/30" : "bg-white/20"
                    )}>
                      <Upload className={cn(
                        "h-12 w-12",
                        isDragActive ? "text-purple-200" : "text-white"
                      )} />
                    </div>

                    <h3 className="text-xl font-semibold mb-2 text-white">
                      {isDragActive ? 'Drop files here' : 'Select Files to Upload'}
                    </h3>

                    <p className="text-white/90 mb-4 max-w-md">
                      Drag and drop your files here, or click to browse.
                      Supports PDF, Word documents, and images.
                    </p>

                    <div className="flex flex-wrap justify-center gap-2 mb-4">
                      <Badge variant="secondary" className="bg-white/20 text-white border-white/30">PDF</Badge>
                      <Badge variant="secondary" className="bg-white/20 text-white border-white/30">Word</Badge>
                      <Badge variant="secondary" className="bg-white/20 text-white border-white/30">Images</Badge>
                    </div>

                    <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white">
                      <Upload className="h-5 w-5 mr-2" />
                      Choose Files
                    </Button>

                    <p className="text-white/60 text-sm mt-4">
                      Max file size: 50 MB • Max 10 files
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* File Configuration Step */}
          {currentStep === 'configure' && files.length > 0 && (
            <div className="space-y-4">
              {/* Back to file selection button */}
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  onClick={() => setCurrentStep('select')}
                  className="text-white hover:bg-white/10"
                >
                  ← Back to File Selection
                </Button>
                <span className="text-white/70 text-sm">
                  {files.length} file{files.length !== 1 ? 's' : ''} selected
                </span>
              </div>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <FileText className="h-5 w-5" />
                    Configure Your Files ({files.length})
                  </CardTitle>
                  <CardDescription className="text-white/80">
                    Set names, categories, and descriptions for each file
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {files.map((file) => (
                      <div key={file.id} className="border border-white/20 rounded-lg p-4 bg-white/5">
                        {/* Compact File Header */}
                        <div className="flex items-center gap-3 mb-3">
                          <div className="flex-shrink-0">
                            {file.preview ? (
                              <img
                                src={file.preview}
                                alt={file.name}
                                className="h-10 w-10 object-cover rounded"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded bg-white/20 flex items-center justify-center">
                                {getFileIcon(file)}
                              </div>
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <Input
                                value={file.displayName || file.name}
                                onChange={(e) => updateFileMetadata(file.id, { displayName: e.target.value })}
                                className="bg-transparent border-none text-white font-medium p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0"
                                disabled={isUploading}
                              />
                            </div>
                            <p className="text-white/60 text-xs">{formatFileSize(file.size)}</p>
                          </div>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 text-white/60 hover:text-white hover:bg-white/10"
                            onClick={() => removeFile(file.id)}
                            disabled={isUploading}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>

                        {/* Compact Configuration */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {/* Category Dropdown */}
                          <div className="space-y-1">
                            <Label className="text-white text-xs font-medium">Category *</Label>
                            <select
                              value={file.category}
                              onChange={(e) => updateFileMetadata(file.id, { category: e.target.value })}
                              disabled={isUploading}
                              className="w-full h-8 rounded-md border border-white/20 bg-white/10 text-white text-sm px-2 focus:outline-none focus:ring-1 focus:ring-purple-400"
                            >
                              <option value="" className="bg-gray-800">Select category</option>
                              {categories.map((category) => (
                                <option key={category.id} value={category.id} className="bg-gray-800">
                                  {category.label}
                                </option>
                              ))}
                            </select>
                          </div>

                          {/* Page Count for PDFs */}
                          {file.type === 'application/pdf' && (
                            <div className="space-y-1">
                              <Label className="text-white text-xs font-medium">Pages</Label>
                              <Input
                                type="number"
                                placeholder="Optional"
                                value={file.page_count || ''}
                                onChange={(e) => updateFileMetadata(file.id, {
                                  page_count: e.target.value ? parseInt(e.target.value) : undefined
                                })}
                                className="h-8 bg-white/10 border-white/20 text-white text-sm"
                                disabled={isUploading}
                              />
                            </div>
                          )}
                        </div>

                        {/* Description */}
                        <div className="mt-3 space-y-1">
                          <Label className="text-white text-xs font-medium">Description</Label>
                          <Textarea
                            value={file.description}
                            onChange={(e) => updateFileMetadata(file.id, { description: e.target.value })}
                            className="bg-white/10 border-white/20 text-white text-sm min-h-[60px] resize-none"
                            placeholder="Optional description..."
                            disabled={isUploading}
                          />
                        </div>

                        {/* Anonymous Upload Toggle - Clean */}
                        <div className="mt-3 flex items-center gap-2">
                          <input
                            type="checkbox"
                            id={`anonymous-${file.id}`}
                            checked={file.uploadAsAnonymous || false}
                            onChange={(e) => updateFileMetadata(file.id, { uploadAsAnonymous: e.target.checked })}
                            disabled={isUploading}
                            className="h-3 w-3 rounded border-white/30 bg-white/10 text-purple-500 focus:ring-purple-400"
                          />
                          <div>
                            <Label htmlFor={`anonymous-${file.id}`} className="text-white text-sm font-medium cursor-pointer">
                              Upload anonymously
                            </Label>
                            <p className="text-white/50 text-xs">Hide your name from other students</p>
                          </div>
                        </div>
                      </div>
                  ))}
                </div>

                {/* Upload Button */}
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-white/20">
                  <div className="text-sm text-white/80">
                    {files.filter(f => f.status === 'completed').length} of {files.length} files uploaded
                  </div>

                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={onClose}
                      disabled={isUploading}
                      className="border-white/30 text-white hover:bg-white/10"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={uploadFiles}
                      disabled={!canUpload || isUploading}
                      className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
                    >
                      {isUploading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload {files.length} File{files.length !== 1 ? 's' : ''}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}