"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { fileOperations } from "@/lib/supabase"
import { cn } from "@/lib/utils"
import {
  Upload,
  FileText,
  Image as ImageIcon,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  BookOpen,
  Target,
  GraduationCap,
  MoreVertical
} from "lucide-react"

interface FileWithMetadata extends File {
  id: string
  category: string
  description: string
  page_count?: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  progress: number
  error?: string
  preview?: string
}

interface EnhancedUploadModalProps {
  courseId: string
  onClose: () => void
  onUploadComplete: () => void
}

export function EnhancedUploadModal({ courseId, onClose, onUploadComplete }: EnhancedUploadModalProps) {
  const [files, setFiles] = useState<FileWithMetadata[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const categories = [
    { id: 'lecture_notes', label: 'Lecture Notes', icon: BookOpen, description: 'Course lectures and presentations' },
    { id: 'summaries', label: 'Summaries', icon: FileText, description: 'Study summaries and notes' },
    { id: 'practice_materials', label: 'Practice Materials', icon: Target, description: 'Exercises and practice problems' },
    { id: 'exams', label: 'Exams', icon: GraduationCap, description: 'Past exams and test materials' },
    { id: 'others', label: 'Others', icon: MoreVertical, description: 'Other course materials' }
  ]

  // Accepted file types: PDF, Word documents, Images
  const acceptedFileTypes = {
    'application/pdf': ['.pdf'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp']
  }

  const maxFileSize = 50 * 1024 * 1024 // 50MB

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      console.warn('Some files were rejected:', rejectedFiles)
      // Could show toast notifications here
    }

    // Convert accepted files to our format
    const newFiles: FileWithMetadata[] = acceptedFiles.map((file) => ({
      ...file,
      id: Math.random().toString(36).substring(7),
      category: 'others', // Default category
      description: '',
      page_count: file.type === 'application/pdf' ? undefined : undefined,
      status: 'pending' as const,
      progress: 0,
      preview: file.type && file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
    }))

    setFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxFiles: 10,
    maxSize: maxFileSize,
    multiple: true
  })

  const updateFileMetadata = (fileId: string, updates: Partial<FileWithMetadata>) => {
    setFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, ...updates } : file
    ))
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const uploadFiles = async () => {
    if (files.length === 0) return

    setIsUploading(true)

    for (const file of files) {
      if (file.status === 'completed') continue

      try {
        // Update status to uploading
        updateFileMetadata(file.id, { status: 'uploading', progress: 0 })

        // Simulate progress
        for (let progress = 0; progress <= 90; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100))
          updateFileMetadata(file.id, { progress })
        }

        // Upload to Supabase
        const { data, error } = await fileOperations.uploadFile(courseId, file, {
          category: file.category,
          description: file.description || undefined,
          page_count: file.page_count || undefined
        })

                 if (error) {
           updateFileMetadata(file.id, { 
             status: 'error', 
             error: (error as any)?.message || 'Upload failed',
             progress: 0
           })
         } else {
           updateFileMetadata(file.id, { status: 'completed', progress: 100 })
         }
       } catch (error) {
         updateFileMetadata(file.id, { 
           status: 'error', 
           error: 'Upload failed',
           progress: 0
         })
      }
    }

    setIsUploading(false)

    // Check if all files uploaded successfully
    const allCompleted = files.every(f => f.status === 'completed')
    if (allCompleted) {
      onUploadComplete()
    }
  }

  const getFileIcon = (file: FileWithMetadata) => {
    // Add null/undefined check for file.type
    if (!file.type) return <FileText className="h-5 w-5 text-gray-500" />

    if (file.type.startsWith('image/')) return <ImageIcon className="h-5 w-5 text-green-500" />
    if (file.type.includes('pdf')) return <FileText className="h-5 w-5 text-red-500" />
    if (file.type.includes('word') || file.type.includes('document')) return <FileText className="h-5 w-5 text-blue-500" />
    return <FileText className="h-5 w-5 text-gray-500" />
  }

  const getStatusIcon = (status: FileWithMetadata['status']) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'uploading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const canUpload = files.length > 0 && files.every(f => 
    f.category && f.category !== '' && f.status !== 'uploading'
  )

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-900 border-0 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-white text-xl font-bold">Upload Course Materials</DialogTitle>
          <DialogDescription className="text-white/80 text-base">
            Upload PDF documents, Word files, and images to share with your classmates
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Area */}
          <Card className={cn(
            "border-2 border-dashed transition-colors bg-white/10 backdrop-blur-sm",
            isDragActive ? "border-purple-300 bg-purple-500/20" : "border-white/30 hover:border-purple-300"
          )}>
            <CardContent className="p-8">
              <div {...getRootProps()} className="cursor-pointer">
                <input {...getInputProps()} />
                <div className="flex flex-col items-center justify-center text-center">
                  <div className={cn(
                    "rounded-full p-6 mb-4",
                    isDragActive ? "bg-purple-400/30" : "bg-white/20"
                  )}>
                    <Upload className={cn(
                      "h-12 w-12",
                      isDragActive ? "text-purple-200" : "text-white"
                    )} />
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-2 text-white">
                    {isDragActive ? 'Drop files here' : 'Upload Course Materials'}
                  </h3>
                  
                  <p className="text-white/90 mb-4 max-w-md">
                    Drag and drop your files here, or click to browse. 
                    Supports PDF, Word documents, and images.
                  </p>
                  
                  <div className="flex flex-wrap justify-center gap-2 mb-4">
                    <Badge variant="secondary" className="bg-white/20 text-white border-white/30">PDF</Badge>
                    <Badge variant="secondary" className="bg-white/20 text-white border-white/30">Word</Badge>
                    <Badge variant="secondary" className="bg-white/20 text-white border-white/30">Images</Badge>
                  </div>
                  
                  <Button size="lg" disabled={isUploading} className="bg-purple-500 hover:bg-purple-600 text-white border-0">
                    <Upload className="h-5 w-5 mr-2" />
                    Choose Files
                  </Button>
                  
                  <p className="text-xs text-white/70 mt-4">
                    Max file size: {formatFileSize(maxFileSize)} • Max 10 files
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Files List */}
          {files.length > 0 && (
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <FileText className="h-5 w-5" />
                  Files to Upload ({files.length})
                </CardTitle>
                <CardDescription className="text-white/80">
                  Configure categories and descriptions for your files
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {files.map((file) => (
                    <div key={file.id} className="border border-white/20 rounded-lg p-4 space-y-4 bg-white/5">
                      {/* File Header */}
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0">
                          {file.preview ? (
                            <img 
                              src={file.preview} 
                              alt={file.name}
                              className="h-12 w-12 object-cover rounded"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded bg-white/20 flex items-center justify-center">
                              {getFileIcon(file)}
                            </div>
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-medium truncate text-white">{file.name}</h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => removeFile(file.id)}
                              disabled={isUploading}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-white/70 mb-2">
                            <span>{formatFileSize(file.size)}</span>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(file.status)}
                              <span className="capitalize">{file.status}</span>
                            </div>
                          </div>

                          {/* Progress Bar */}
                          {file.status === 'uploading' && (
                            <div className="space-y-1">
                              <Progress value={file.progress} className="h-2" />
                              <p className="text-xs text-white/70">
                                Uploading... {file.progress}%
                              </p>
                            </div>
                          )}

                          {/* Error Message */}
                          {file.status === 'error' && file.error && (
                            <p className="text-xs text-red-300">{file.error}</p>
                          )}
                        </div>
                      </div>

                                              {/* Metadata Form */}
                        {file.status !== 'completed' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-white/20">
                            {/* Category Selection */}
                            <div className="space-y-2">
                              <Label htmlFor={`category-${file.id}`} className="text-white font-medium">Category *</Label>
                                                           <select
                                id={`category-${file.id}`}
                                value={file.category} 
                                onChange={(e) => updateFileMetadata(file.id, { category: e.target.value })}
                                disabled={isUploading}
                                className="flex h-10 w-full rounded-md border border-white/30 bg-white/10 backdrop-blur-sm text-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:cursor-not-allowed disabled:opacity-50"
                              >
                               <option value="">Select category</option>
                               {categories.map((category) => (
                                 <option key={category.id} value={category.id}>
                                   {category.label} - {category.description}
                                 </option>
                               ))}
                             </select>
                           </div>

                          {/* Page Count (for PDFs) */}
                          {file.type === 'application/pdf' && (
                            <div className="space-y-2">
                              <Label htmlFor={`pages-${file.id}`} className="text-white font-medium">Page Count</Label>
                              <Input
                                id={`pages-${file.id}`}
                                type="number"
                                placeholder="Optional"
                                value={file.page_count || ''}
                                onChange={(e) => updateFileMetadata(file.id, { 
                                  page_count: e.target.value ? parseInt(e.target.value) : undefined 
                                })}
                                disabled={isUploading}
                              />
                            </div>
                          )}

                          {/* Description */}
                          <div className="space-y-2 md:col-span-2">
                                                         <Label htmlFor={`description-${file.id}`} className="text-white font-medium">Description</Label>
                            <Textarea
                              id={`description-${file.id}`}
                              placeholder="Optional description of the file content..."
                              value={file.description}
                              onChange={(e) => updateFileMetadata(file.id, { description: e.target.value })}
                              disabled={isUploading}
                              rows={2}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Upload Button */}
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-white/20">
                  <div className="text-sm text-white/80">
                    {files.filter(f => f.status === 'completed').length} of {files.length} files uploaded
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={onClose} disabled={isUploading} className="border-white/30 text-white hover:bg-white/10">
                      Cancel
                    </Button>
                    <Button 
                      onClick={uploadFiles} 
                      disabled={!canUpload || isUploading}
                      className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
                    >
                      {isUploading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Files
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
} 