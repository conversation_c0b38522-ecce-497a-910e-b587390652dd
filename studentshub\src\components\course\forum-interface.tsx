"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { 
  Send, 
  Search, 
  Plus, 
  Image as ImageIcon,
  FileText,
  Pin,
  Lock,
  CheckCircle,
  Eye,
  EyeOff,
  Bold,
  Italic,
  Underline,
  Smile,
  List,
  ListOrdered,
  Quote,
  Type,
  ChevronDown,
  Edit2,
  Trash2,
  Heart,
  MoreHorizontal
} from "lucide-react"
import type { 
  ForumCategory, 
  ForumTopic, 
  ForumMessage,
  CreateMessageInput,
  CreateTopicInput 
} from "@/types/forum"
import {
  getForumCategories,
  getForumTopics,
  getForumMessages,
  createForumMessage,
  createForumTopic,
  updateForumMessage,
  deleteForumMessage,
  addReaction,
  removeReaction
} from "@/lib/forum-api"
import { supabase } from "@/lib/supabase"

interface ForumInterfaceProps {
  courseId: string
  user: any
}

export function ForumInterface({ courseId, user }: ForumInterfaceProps) {
  // State Management
  const [categories, setCategories] = useState<ForumCategory[]>([])
  const [selectedCategory, setSelectedCategory] = useState<ForumCategory | null>(null)
  const [topics, setTopics] = useState<ForumTopic[]>([])
  const [selectedTopic, setSelectedTopic] = useState<ForumTopic | null>(null)
  const [messages, setMessages] = useState<ForumMessage[]>([])

  
  // UI State
  const [messageContent, setMessageContent] = useState("")
  const [isTopicsVisible, setIsTopicsVisible] = useState(true)
  const [isCreatingTopic, setIsCreatingTopic] = useState(false)
  const [newTopicTitle, setNewTopicTitle] = useState("")
  const [newTopicDescription, setNewTopicDescription] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [showFormatting, setShowFormatting] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [showFileDialog, setShowFileDialog] = useState(false)
  const [pendingImages, setPendingImages] = useState<{file: File, localUrl: string}[]>([])
  const [isSending, setIsSending] = useState(false)
  
  // Message Actions State
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null)
  const [editingContent, setEditingContent] = useState("")
  const [hoveredMessageId, setHoveredMessageId] = useState<string | null>(null)
  const [showReactionPicker, setShowReactionPicker] = useState<string | null>(null)
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const editorRef = useRef<HTMLDivElement>(null)
  const editEditorRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Data Loading Functions
  const loadCategories = async () => {
    if (!courseId) return
    const { data, error } = await getForumCategories(courseId)
    if (data && !error) {
      setCategories(data)
    }
  }

  const loadTopics = async (categoryId: string) => {
    const { data, error } = await getForumTopics(categoryId)
    if (data && !error) {
      setTopics(data)
    }
  }

  const loadMessages = async (categoryId: string, topicId?: string) => {
    const { data, error } = await getForumMessages(categoryId, topicId)
    if (data && !error) {
      setMessages(data)
    }
  }



  // Load forum categories on mount
  useEffect(() => {
    if (courseId) {
      loadCategories()
    }
  }, [courseId])

  // Auto-select first category
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0])
    }
  }, [categories, selectedCategory])

  // Load topics when category changes
  useEffect(() => {
    if (selectedCategory) {
      if (selectedCategory.type === 'forum') {
        loadTopics(selectedCategory.id)
      } else {
        setTopics([])
        setSelectedTopic(null)
        loadMessages(selectedCategory.id)
      }
    }
  }, [selectedCategory])

  // Load messages when topic changes
  useEffect(() => {
    if (selectedTopic) {
      loadMessages(selectedCategory!.id, selectedTopic.id)
    }
  }, [selectedTopic])

  // Smart scroll to bottom - only within the messages container
  const [shouldAutoScroll, setShouldAutoScroll] = useState(false)
  const [isNearBottom, setIsNearBottom] = useState(true)
  const [isFirstLoad, setIsFirstLoad] = useState(true)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Check if user is near bottom of messages
  const checkScrollPosition = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current
      const isNear = scrollHeight - scrollTop - clientHeight < 100 // Within 100px of bottom
      setIsNearBottom(isNear)
    }
  }

  // Scroll to bottom within the messages container only
  const scrollToBottomInContainer = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight
    }
  }

  // Auto-scroll logic for messages
  useEffect(() => {
    if (messages.length > 0) {
      if (isFirstLoad || shouldAutoScroll || isNearBottom) {
        // Small delay to ensure DOM is updated
        setTimeout(() => {
          scrollToBottomInContainer()
          if (isFirstLoad) {
            setIsFirstLoad(false)
          }
          if (shouldAutoScroll) {
            setShouldAutoScroll(false)
          }
        }, 100)
      }
    }
  }, [messages, shouldAutoScroll, isNearBottom, isFirstLoad])

  // Reset first load when switching categories/topics
  useEffect(() => {
    setIsFirstLoad(true)
  }, [selectedCategory, selectedTopic])

  // Set cursor to end of content
  const setCursorToEnd = (element: HTMLElement) => {
    const range = document.createRange()
    const selection = window.getSelection()
    
    // Set range to end of element
    range.selectNodeContents(element)
    range.collapse(false)
    
    // Clear existing selection and add new range
    selection?.removeAllRanges()
    selection?.addRange(range)
  }

  // Set initial content when editing starts (only once!)
  useEffect(() => {
    if (editingMessageId && editEditorRef.current) {
      // Set content only once when editing starts
      editEditorRef.current.innerHTML = editingContent
      
      // Focus the editor and set cursor to end
      setTimeout(() => {
        if (editEditorRef.current) {
          editEditorRef.current.focus()
          setCursorToEnd(editEditorRef.current)
        }
      }, 50) // Slightly longer delay
    }
  }, [editingMessageId]) // Only depend on editingMessageId, NOT editingContent!

  // Auto-resize editor when content changes
  useEffect(() => {
    autoResizeEditor()
  }, [messageContent])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.emoji-picker') && !target.closest('.file-dialog')) {
        setShowEmojiPicker(false)
        setShowFileDialog(false)
      }
      if (!target.closest('.reaction-picker')) {
        setShowReactionPicker(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Clean up on unmount
  useEffect(() => {
    return () => {
      cleanupPendingImages()
    }
  }, [])

  // Clean up pending images
  const cleanupPendingImages = () => {
    console.log('Cleaning up pending images:', pendingImages.length)
    pendingImages.forEach(({ localUrl }) => {
      console.log('Revoking blob URL:', localUrl)
      URL.revokeObjectURL(localUrl)
    })
    setPendingImages([])
  }

  // Helper function to extract text content from HTML, replacing images with placeholder text
  const extractEditableContent = (htmlContent: string) => {
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlContent
    
    // Replace images with placeholder text
    const images = tempDiv.querySelectorAll('img')
    images.forEach((img, index) => {
      const placeholder = document.createTextNode(`[Image ${index + 1}]`)
      img.parentNode?.replaceChild(placeholder, img)
    })
    
    // Extract clean text content
    return tempDiv.textContent || tempDiv.innerText || ''
  }

  // Message Action Functions
  const handleEditMessage = (message: ForumMessage) => {
    setEditingMessageId(message.id)
    // Reconstruct the HTML content with the image embedded for editing
    let editableContent = message.content
    
    // If there's an image_url, make sure it's embedded in the content for editing
    if (message.image_url && !message.content.includes('<img')) {
      editableContent = message.content + `<img src="${message.image_url}" style="max-width:300px; border-radius:8px; margin:8px 0; display:block;">`
    }
    
    setEditingContent(editableContent)
  }

  const handleSaveEdit = async (messageId: string) => {
    if (!editingContent.trim()) return

    try {
      // Get the HTML content from the edit editor
      let htmlContent = editEditorRef.current?.innerHTML || editingContent

      // Remove any image tags from content since we store images separately
      const cleanContent = htmlContent.replace(/<img[^>]*>/g, '').trim()

      if (!cleanContent) return

      const { data, error } = await updateForumMessage(messageId, cleanContent)
      if (data && !error) {
        setEditingMessageId(null)
        setEditingContent("")
        // Clear the edit editor content
        if (editEditorRef.current) {
          editEditorRef.current.innerHTML = ""
        }
        loadMessages(selectedCategory!.id, selectedTopic?.id)
      } else {
        console.error('Failed to update message:', error)
      }
    } catch (error) {
      console.error('Error updating message:', error)
    }
  }

  const handleCancelEdit = () => {
    setEditingMessageId(null)
    setEditingContent("")
    // Clear the edit editor content
    if (editEditorRef.current) {
      editEditorRef.current.innerHTML = ""
    }
  }

  const handleDeleteMessage = async (messageId: string) => {
    if (!confirm('Are you sure you want to delete this message?')) return

    try {
      const { error } = await deleteForumMessage(messageId)
      if (!error) {
        loadMessages(selectedCategory!.id, selectedTopic?.id)
      } else {
        console.error('Failed to delete message:', error)
      }
    } catch (error) {
      console.error('Error deleting message:', error)
    }
  }

  const handleQuickReaction = async (messageId: string, emoji: string) => {
    try {
      const message = messages.find(m => m.id === messageId)
      if (!message) return

      // Check if user already reacted with this specific emoji
      const userReacted = message.reactions[emoji]?.includes(user.id)
      
      if (userReacted) {
        // Remove this reaction
        await removeReaction(messageId, emoji)
      } else {
        // Remove any existing reaction from this user first (one reaction per user)
        for (const [existingEmoji, users] of Object.entries(message.reactions)) {
          if (users.includes(user.id)) {
            await removeReaction(messageId, existingEmoji)
            break
          }
        }
        
        // Add the new reaction
        await addReaction(messageId, emoji)
      }
      
      loadMessages(selectedCategory!.id, selectedTopic?.id)
      setShowReactionPicker(null)
    } catch (error) {
      console.error('Error adding reaction:', error)
    }
  }

  // Message Handling
  const handleSendMessage = async () => {
    if (!messageContent.trim() || !selectedCategory || isSending) return

    setIsSending(true)

    try {
      // Get HTML content for rich formatting
      let htmlContent = editorRef.current?.innerHTML || messageContent
      let uploadedImageUrl: string | null = null

      // Upload pending images and get URLs, but remove images from content
      if (pendingImages.length > 0) {
        for (const { file, localUrl } of pendingImages) {
          try {
            const supabaseUrl = await uploadImageToSupabase(file)
            if (supabaseUrl) {
              // Save the first uploaded image URL
              if (!uploadedImageUrl) {
                uploadedImageUrl = supabaseUrl
              }
            }
            // Clean up local URL
            URL.revokeObjectURL(localUrl)
          } catch (error) {
            console.error('Failed to upload image:', error)
          }
        }
        
        // Remove all image tags from content since we store images separately
        htmlContent = htmlContent.replace(/<img[^>]*>/g, '')
        
        // Clear pending images
        setPendingImages([])
      }

      const input: CreateMessageInput = {
        content: htmlContent, // Send HTML content with Supabase URLs
        category_id: selectedCategory.id,
        topic_id: selectedTopic?.id,
        image_url: uploadedImageUrl // Pass the image URL directly
      }

      const { data, error } = await createForumMessage(input)
      if (data && !error) {
        setMessageContent("")
        if (editorRef.current) {
          editorRef.current.innerHTML = ""
        }
        autoResizeEditor()
        setShouldAutoScroll(true) // Enable auto-scroll for user's own messages
        loadMessages(selectedCategory.id, selectedTopic?.id)
      } else {
        // If sending failed, clean up any uploaded images from this attempt
        console.error('Failed to send message:', error)
      }
    } finally {
      setIsSending(false)
    }
  }

  // Auto-resize editor
  const autoResizeEditor = () => {
    if (editorRef.current) {
      editorRef.current.style.height = 'auto'
      
      // Check if there are images in the content
      const hasImages = editorRef.current.querySelector('img')
      const maxHeight = hasImages ? 200 : 120 // Increase max height when images are present
      
      const newHeight = Math.min(editorRef.current.scrollHeight, maxHeight)
      editorRef.current.style.height = `${Math.max(newHeight, 44)}px` // Ensure minimum height
    }
  }

  // Rich text formatting functions
  const applyFormatting = (command: string, value?: string) => {
    document.execCommand(command, false, value)
    if (editorRef.current) {
      editorRef.current.focus()
      updateContent()
    }
  }

  // Update content from contentEditable
  const updateContent = () => {
    if (editorRef.current) {
      const htmlContent = editorRef.current.innerHTML
      // Convert HTML back to text for storage/sending
      const textContent = editorRef.current.innerText || editorRef.current.textContent || ''
      
      // Check if there are images even if text is empty
      const hasImages = editorRef.current.querySelector('img')
      const hasContent = textContent.trim() || hasImages
      
      setMessageContent(hasContent ? (textContent || ' ') : '')
    }
  }

  // No need for updateEditContent - we'll get content directly when saving

  // Insert custom formatting (for markdown-style)
  const insertCustomFormatting = (before: string, after: string = before) => {
    if (!editorRef.current) return
    
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const selectedText = range.toString()
      
      const wrapper = document.createElement('span')
      wrapper.innerHTML = before + selectedText + after
      
      range.deleteContents()
      range.insertNode(wrapper)
      
      // Move cursor to end
      selection.removeAllRanges()
      const newRange = document.createRange()
      newRange.setStartAfter(wrapper)
      newRange.collapse(true)
      selection.addRange(newRange)
      
      updateContent()
    }
  }

  // Emoji data
  const emojis = [
    '😀', '😃', '😄', '😁', '😅', '😂', '🤣', '😊', '😇', '🙂',
    '😉', '😌', '😍', '🥰', '😘', '😗', '😋', '😎', '🤔', '🤨',
    '😐', '😑', '😶', '🙄', '😏', '😣', '😥', '😮', '🤐', '😯',
    '😴', '😔', '😕', '🙃', '🤑', '😲', '☹️', '🙁', '😖', '😞',
    '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '👏', '🙌', '👐',
    '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻',
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
    '🔥', '⭐', '🌟', '✨', '⚡', '☄️', '💫', '🌈', '☀️', '⛅',
    '🎉', '🎊', '🎈', '🎁', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️'
  ]

  // Insert emoji into editor
  const insertEmoji = (emoji: string) => {
    if (editorRef.current) {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const textNode = document.createTextNode(emoji)
        range.insertNode(textNode)
        
        // Move cursor after emoji
        range.setStartAfter(textNode)
        range.collapse(true)
        selection.removeAllRanges()
        selection.addRange(range)
        
        updateContent()
        editorRef.current.focus()
      }
    }
    setShowEmojiPicker(false)
  }

  // Upload image to Supabase Storage
  const uploadImageToSupabase = async (file: File): Promise<string | null> => {
    try {
      // Check if user is authenticated
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        console.error('User not authenticated')
        return null
      }

      // Generate unique filename with user ID for organization
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`
      
      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('forum-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('Error uploading image:', error)
        return null
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('forum-images')
        .getPublicUrl(fileName)

      return publicUrl
    } catch (error) {
      console.error('Error uploading image:', error)
      return null
    }
  }

  // Handle image upload (show preview only, upload on send)
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      console.log('Creating blob URL for file:', file.name, file.size)
      
      // Create local preview URL
      const localUrl = URL.createObjectURL(file)
      console.log('Created blob URL:', localUrl)
      
      // Add to pending images
      setPendingImages(prev => {
        const newImages = [...prev, { file, localUrl }]
        console.log('Pending images updated:', newImages.length)
        return newImages
      })
      
      // Show local preview in editor
      if (editorRef.current) {
        const img = document.createElement('img')
        img.src = localUrl
        img.style.maxWidth = '300px'
        img.style.borderRadius = '8px'
        img.style.margin = '8px 0'
        img.style.display = 'block'
        img.setAttribute('data-pending-image', localUrl) // Mark as pending
        
        // Error handling for image load
        img.onerror = () => {
          console.error('Failed to load image preview:', localUrl)
          img.parentNode?.removeChild(img)
          updateContent()
        }
        
        // Resize editor when image loads
        img.onload = () => {
          console.log('Image loaded successfully:', localUrl)
          autoResizeEditor()
        }
        
        const selection = window.getSelection()
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          range.insertNode(img)
          range.setStartAfter(img)
          range.collapse(true)
          selection.removeAllRanges()
          selection.addRange(range)
          
          updateContent()
          
          // Auto-resize after image insertion
          setTimeout(() => {
            autoResizeEditor()
          }, 100) // Increased timeout
        }
      }
    }
    
    // Reset input
    if (event.target) {
      event.target.value = ''
    }
  }

  // Mock course files for reference
  const courseFiles = [
    { id: '1', name: 'Lecture 1 - Introduction.pdf', type: 'pdf' },
    { id: '2', name: 'Assignment 1.docx', type: 'docx' },
    { id: '3', name: 'Neural Networks Slides.pptx', type: 'pptx' },
    { id: '4', name: 'Algorithms Cheat Sheet.pdf', type: 'pdf' },
    { id: '5', name: 'Python Exercises.py', type: 'python' }
  ]

  // Insert file reference
  const insertFileReference = (file: { id: string; name: string; type: string }) => {
    if (editorRef.current) {
      const fileRef = document.createElement('span')
      fileRef.className = 'inline-flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-300 rounded text-sm border border-blue-400/30'
      fileRef.innerHTML = `📄 ${file.name}`
      fileRef.setAttribute('data-file-id', file.id)
      
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        range.insertNode(fileRef)
        range.setStartAfter(fileRef)
        range.collapse(true)
        selection.removeAllRanges()
        selection.addRange(range)
        
        updateContent()
        editorRef.current.focus()
      }
    }
    setShowFileDialog(false)
  }

  // Simple markdown renderer for messages
  const renderMessageContent = (content: string) => {
    // Basic markdown formatting
    let formatted = content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>')              // Italic  
      .replace(/`(.*?)`/g, '<code class="bg-white/10 px-1 rounded text-purple-300">$1</code>') // Code

    return <span dangerouslySetInnerHTML={{ __html: formatted }} />
  }

  const handleCreateTopic = async () => {
    if (!newTopicTitle.trim() || !selectedCategory) return

    const input: CreateTopicInput = {
      title: newTopicTitle,
      description: newTopicDescription,
      category_id: selectedCategory.id,
      initial_message: newTopicDescription || "Topic created"
    }

    const { data, error } = await createForumTopic(input)
    if (data && !error) {
      setNewTopicTitle("")
      setNewTopicDescription("")
      setIsCreatingTopic(false)
      loadTopics(selectedCategory.id)
    }
  }

  // Filter topics based on search
  const filteredTopics = topics.filter(topic =>
    topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    topic.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Category Icons
  const getCategoryIcon = (category: ForumCategory) => {
    switch (category.name) {
      case 'announcements': return '📢'
      case 'general': return '💬'
      case 'questions': return '❓'
      default: return '📁'
    }
  }

  const getCategoryColor = (category: ForumCategory) => {
    switch (category.name) {
      case 'announcements': return 'from-orange-500 to-red-500'
      case 'general': return 'from-blue-500 to-cyan-500'
      case 'questions': return 'from-green-500 to-emerald-500'
      default: return 'from-purple-500 to-pink-500'
    }
  }

  return (
    <Card className="bg-white/5 backdrop-blur-xl border-white/10 h-[700px] flex flex-col">
      <CardHeader className="py-3">
        <CardTitle className="text-white flex items-center gap-2">
          💬 Discussion Forums
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 flex-1 flex flex-col min-h-0">
        <div className="flex flex-1 min-h-0">
          {/* Categories Panel - Left */}
          <div className="w-72 border-r border-white/10 flex flex-col">
            <div className="p-3 border-b border-white/10">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-white font-medium">Categories</h3>
                <Button
                  size="sm"
                  variant="outline"
                  className="border-white/20 text-white hover:bg-white/10 h-7 px-2"
                >
                  <Plus className="w-3 h-3 mr-1" />
                  New
                </Button>
              </div>
              <div className="space-y-2">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    onClick={() => setSelectedCategory(category)}
                    className={cn(
                      "p-3 rounded-lg cursor-pointer transition-all duration-200",
                      selectedCategory?.id === category.id
                        ? `bg-gradient-to-r ${getCategoryColor(category)} text-white`
                        : "bg-white/5 hover:bg-white/10 text-white/80"
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{getCategoryIcon(category)}</span>
                        <div>
                          <h4 className="font-medium capitalize">{category.name.replace('_', ' ')}</h4>
                          <p className="text-xs opacity-70">{category.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs opacity-70">
                          {category.type === 'forum' ? `${topics.length} topics` : 'Chat'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Topics Panel - Middle (for Q&A) */}
          {selectedCategory?.type === 'forum' && (
            <div className={cn(
              "border-r border-white/10 flex flex-col transition-all duration-300",
              isTopicsVisible ? "w-64" : "w-12"
            )}>
              <div className="p-3 border-b border-white/10 flex items-center justify-between">
                {isTopicsVisible ? (
                  <>
                    <h3 className="text-white font-medium">Topics</h3>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => setIsCreatingTopic(true)}
                        className="bg-green-500 hover:bg-green-600 text-white h-8"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setIsTopicsVisible(false)}
                        className="border-white/20 text-white hover:bg-white/10 h-8 w-8 p-0"
                      >
                        <EyeOff className="w-4 h-4" />
                      </Button>
                    </div>
                  </>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setIsTopicsVisible(true)}
                    className="border-white/20 text-white hover:bg-white/10 h-8 w-8 p-0"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {isTopicsVisible && (
                <>
                  {/* Search Topics */}
                  <div className="p-3 border-b border-white/10">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                      <Input
                        placeholder="Search topics..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 bg-white/5 border-white/20 text-white placeholder:text-white/50"
                      />
                    </div>
                  </div>

                  {/* Topics List */}
                  <div className="flex-1 overflow-y-auto">
                    <div className="p-3 space-y-2">
                      {isCreatingTopic && (
                        <Card className="bg-white/10 border-white/20">
                          <CardContent className="p-3 space-y-2">
                            <Input
                              placeholder="Topic title..."
                              value={newTopicTitle}
                              onChange={(e) => setNewTopicTitle(e.target.value)}
                              className="bg-white/5 border-white/20 text-white placeholder:text-white/50"
                            />
                            <Textarea
                              placeholder="Topic description (optional)..."
                              value={newTopicDescription}
                              onChange={(e) => setNewTopicDescription(e.target.value)}
                              className="bg-white/5 border-white/20 text-white placeholder:text-white/50 h-20"
                            />
                            <div className="flex gap-2">
                              <Button size="sm" onClick={handleCreateTopic} className="bg-green-500 hover:bg-green-600">
                                Create
                              </Button>
                              <Button size="sm" variant="outline" onClick={() => setIsCreatingTopic(false)} className="border-white/20 text-white">
                                Cancel
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {filteredTopics.map((topic) => (
                        <div
                          key={topic.id}
                          onClick={() => setSelectedTopic(topic)}
                          className={cn(
                            "p-3 rounded-lg cursor-pointer transition-all duration-200 border",
                            selectedTopic?.id === topic.id
                              ? "bg-purple-500/20 border-purple-400/40"
                              : "bg-white/5 border-white/10 hover:bg-white/10"
                          )}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                {topic.is_pinned && <Pin className="h-3 w-3 text-yellow-400" />}
                                {topic.is_solved && <CheckCircle className="h-3 w-3 text-green-400" />}
                                {topic.is_locked && <Lock className="h-3 w-3 text-red-400" />}
                                <h4 className="text-white font-medium text-sm line-clamp-1">
                                  {topic.title}
                                </h4>
                              </div>
                              <p className="text-white/60 text-xs line-clamp-2 mb-2">
                                {topic.description}
                              </p>
                              <div className="flex items-center gap-2 text-xs text-white/50">
                                <span>{topic.message_count} replies</span>
                                <span>•</span>
                                <span>{topic.participant_count} participants</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {filteredTopics.length === 0 && !isCreatingTopic && (
                        <div className="text-center py-8">
                          <p className="text-white/60 mb-4">No topics found</p>
                          <Button
                            size="sm"
                            onClick={() => setIsCreatingTopic(true)}
                            className="bg-green-500 hover:bg-green-600"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Create First Topic
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Chat Interface - Right */}
          <div className="flex-1 flex flex-col h-full">
            {/* Chat Header */}
            <div className="p-3 border-b border-white/10 flex-shrink-0">
              <div>
                <h3 className="text-white font-medium">
                  {selectedCategory?.name === 'announcements' 
                    ? '📢 Announcements' 
                    : selectedCategory?.name === 'general'
                    ? '💬 General Chat'
                    : selectedTopic 
                    ? `❓ ${selectedTopic.title}`
                    : '❓ Select a Topic'
                  }
                </h3>
                <p className="text-white/60 text-sm">
                  {selectedCategory?.name === 'announcements' 
                    ? 'One message per day limit'
                    : selectedCategory?.type === 'chat'
                    ? 'Chat freely with classmates'
                    : selectedTopic
                    ? selectedTopic.description
                    : 'Choose a topic to start chatting'
                  }
                </p>
              </div>
            </div>

            {/* Messages Area - Fixed Height with Scroll */}
            <div 
              ref={messagesContainerRef}
              className="flex-1 overflow-y-auto min-h-0"
              onScroll={checkScrollPosition}
            >
              <div className="p-3">
              <div className="space-y-1">
                {messages.map((message) => {
                  const isCurrentUser = message.user_id === user.id
                  const isEditing = editingMessageId === message.id
                  
                  return (
                    <div 
                      key={message.id} 
                      className="flex gap-3 group"
                      onMouseEnter={() => setHoveredMessageId(message.id)}
                      onMouseLeave={() => setHoveredMessageId(null)}
                    >
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-sm font-medium">
                          {(message.user?.name || user?.name || user?.email || 'User')?.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-1 relative">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-white font-medium text-sm">
                            {message.user?.name || user?.name || user?.email?.split('@')[0] || 'Student'}
                          </span>
                          <span className="text-white/50 text-xs">
                            {new Date(message.created_at).toLocaleTimeString()}
                          </span>
                          {message.is_edited && (
                            <Badge variant="outline" className="border-white/20 text-white/60 text-xs">
                              edited
                            </Badge>
                          )}
                        </div>



                        <div className="bg-white/5 rounded-lg p-3">
                          {/* Edit Mode */}
                          {isEditing ? (
                            <div className="space-y-2">
                              {/* Rich text editor for editing */}
                              <div className="relative bg-white/5 rounded-lg border border-white/20 focus-within:border-purple-400/50 transition-colors">
                                <div
                                  ref={editEditorRef}
                                  contentEditable
                                  suppressContentEditableWarning={true}
                                  className="w-full bg-transparent border-none outline-none text-white resize-none min-h-[60px] overflow-y-auto p-3 text-sm leading-5 [&_strong]:font-bold [&_em]:italic [&_u]:underline [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_li]:ml-4 [&_blockquote]:border-l-2 [&_blockquote]:border-white/30 [&_blockquote]:pl-3 [&_blockquote]:italic [&_img]:block [&_img]:my-2 empty:before:content-[attr(data-placeholder)] empty:before:text-white/50"
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' && e.ctrlKey) {
                                      e.preventDefault()
                                      handleSaveEdit(message.id)
                                    }
                                  }}
                                  data-placeholder="Edit your message..."
                                />
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveEdit(message.id)}
                                  className="bg-green-500 hover:bg-green-600 text-white"
                                >
                                  Save
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={handleCancelEdit}
                                  className="border-white/20 text-white hover:bg-white/10"
                                >
                                  Cancel
                                </Button>
                              </div>
                              <div className="text-xs text-white/40">
                                Press Ctrl + Enter to save quickly
                              </div>
                            </div>
                          ) : (
                            <>
                              {/* Normal Message Display */}
                              <div 
                                className="text-white text-sm [&_strong]:font-bold [&_em]:italic [&_u]:underline [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_li]:ml-4 [&_blockquote]:border-l-2 [&_blockquote]:border-white/30 [&_blockquote]:pl-3 [&_blockquote]:italic"
                                dangerouslySetInnerHTML={{ __html: message.content }}
                              />
                              
                              {/* Referenced Files */}
                              {message.referenced_files.length > 0 && (
                                <div className="mt-2 space-y-1">
                                  {message.referenced_files.map((file, index) => (
                                    <div key={index} className="flex items-center gap-2 p-2 bg-white/5 rounded text-xs">
                                      <FileText className="h-3 w-3 text-blue-400" />
                                      <span className="text-white/80">{file.file_name}</span>
                                    </div>
                                  ))}
                                </div>
                              )}

                              {/* Image */}
                              {message.image_url && (
                                <div className="mt-2">
                                  <img 
                                    src={message.image_url} 
                                    alt="Shared image" 
                                    className="max-w-xs rounded-lg"
                                  />
                                </div>
                              )}

                              {/* Reactions */}
                              {Object.keys(message.reactions).length > 0 && (
                                <div className="flex gap-2 mt-2">
                                  {Object.entries(message.reactions).map(([emoji, users]) => (
                                    <button
                                      key={emoji}
                                      onClick={() => handleQuickReaction(message.id, emoji)}
                                      className={cn(
                                        "px-2 py-1 rounded text-xs transition-colors",
                                        users.includes(user.id)
                                          ? "bg-purple-500/20 text-purple-300"
                                          : "bg-white/5 text-white/60 hover:bg-white/10"
                                      )}
                                    >
                                      {emoji} {users.length}
                                    </button>
                                  ))}
                                </div>
                              )}
                            </>
                          )}
                        </div>

                        {/* Message Actions - Always reserve space, show on hover */}
                        {!isEditing && (
                          <div className="flex items-center gap-1 mt-1 relative h-6"> {/* Fixed height to prevent layout shift */}
                            <TooltipProvider>
                              {/* React Button for All Users */}
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => setShowReactionPicker(message.id)}
                                    className={cn(
                                      "h-6 w-6 p-0 rounded-md transition-all duration-200 ease-in-out",
                                      hoveredMessageId === message.id 
                                        ? "text-white/70 hover:text-white hover:bg-white/10 opacity-100" 
                                        : "text-white/20 opacity-0 pointer-events-none"
                                    )}
                                  >
                                    <Smile className="w-3 h-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent className="bg-gray-900 text-white border-gray-700">
                                  <p>Add reaction</p>
                                </TooltipContent>
                              </Tooltip>

                              {/* Edit and Delete for Current User */}
                              {isCurrentUser && (
                                <>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => handleEditMessage(message)}
                                        className={cn(
                                          "h-6 w-6 p-0 rounded-md transition-all duration-200 ease-in-out",
                                          hoveredMessageId === message.id 
                                            ? "text-white/70 hover:text-white hover:bg-white/10 opacity-100" 
                                            : "text-white/20 opacity-0 pointer-events-none"
                                        )}
                                      >
                                        <Edit2 className="w-3 h-3" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent className="bg-gray-900 text-white border-gray-700">
                                      <p>Edit message</p>
                                    </TooltipContent>
                                  </Tooltip>

                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => handleDeleteMessage(message.id)}
                                        className={cn(
                                          "h-6 w-6 p-0 rounded-md transition-all duration-200 ease-in-out",
                                          hoveredMessageId === message.id 
                                            ? "text-white/70 hover:text-red-400 hover:bg-white/10 opacity-100" 
                                            : "text-white/20 opacity-0 pointer-events-none"
                                        )}
                                      >
                                        <Trash2 className="w-3 h-3" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent className="bg-gray-900 text-white border-gray-700">
                                      <p>Delete message</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </>
                              )}
                            </TooltipProvider>

                            {/* Reaction Picker */}
                            {showReactionPicker === message.id && (
                              <div className="reaction-picker absolute bottom-full left-0 mb-1 bg-gray-900 border border-gray-700 rounded-lg p-2 z-20 shadow-xl">
                                <div className="grid grid-cols-4 gap-1">
                                  {['❤️', '👍', '👎', '😂', '😮', '😢', '🔥', '👏'].map((emoji) => (
                                    <button
                                      key={emoji}
                                      onClick={() => handleQuickReaction(message.id, emoji)}
                                      className="text-lg hover:bg-gray-800 rounded p-1 transition-colors"
                                    >
                                      {emoji}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
                <div ref={messagesEndRef} />
              </div>
            </div>
            </div>

            {/* Message Input - Fixed at Bottom */}
            {(selectedCategory?.type !== 'forum' || selectedTopic) && (
              <TooltipProvider>
                <div className="p-3 border-t border-white/10 flex-shrink-0">
                  {/* Rich Text Editor */}
                  <div className="relative bg-white/5 rounded-lg border border-white/20 focus-within:border-purple-400/50 transition-colors">
                    <div
                      ref={editorRef}
                      contentEditable
                      suppressContentEditableWarning={true}
                      className="w-full bg-transparent border-none outline-none text-white resize-none min-h-[44px] overflow-y-auto p-3 text-sm leading-5 [&_strong]:font-bold [&_em]:italic [&_u]:underline [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_li]:ml-4 [&_blockquote]:border-l-2 [&_blockquote]:border-white/30 [&_blockquote]:pl-3 [&_blockquote]:italic [&_img]:block [&_img]:my-2 empty:before:content-[attr(data-placeholder)] empty:before:text-white/50"
                      style={{ height: '44px' }}
                      onInput={updateContent}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault()
                          handleSendMessage()
                        }
                      }}
                      data-placeholder={
                        selectedCategory?.name === 'announcements'
                          ? "Share an important announcement..."
                          : "Type your message..."
                      }
                    />
                  </div>

                  {/* Hidden file input */}
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    className="hidden"
                  />

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center gap-1 relative">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => fileInputRef.current?.click()}
                            className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                          >
                            <ImageIcon className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-gray-900 text-white border-gray-700">
                          <p>Add Images</p>
                        </TooltipContent>
                      </Tooltip>
                      
                      <div className="relative">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setShowFileDialog(!showFileDialog)}
                              className={cn(
                                "h-8 w-8 p-0 rounded-md transition-colors",
                                showFileDialog 
                                  ? "text-blue-400 bg-blue-400/10" 
                                  : "text-white/60 hover:text-white hover:bg-white/10"
                              )}
                            >
                              <FileText className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent className="bg-gray-900 text-white border-gray-700">
                            <p>Reference Files</p>
                          </TooltipContent>
                        </Tooltip>
                        
                        {/* File Dialog */}
                        {showFileDialog && (
                          <div className="file-dialog absolute bottom-full left-0 mb-2 w-64 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50">
                            <div className="p-3">
                              <h4 className="text-white text-sm font-medium mb-2">Course Files</h4>
                              <div className="max-h-32 overflow-y-auto space-y-1">
                                {courseFiles.map((file) => (
                                  <button
                                    key={file.id}
                                    onClick={() => insertFileReference(file)}
                                    className="w-full text-left p-2 rounded hover:bg-gray-800 transition-colors"
                                  >
                                    <div className="flex items-center gap-2">
                                      <FileText className="w-3 h-3 text-blue-400" />
                                      <span className="text-white text-xs truncate">{file.name}</span>
                                    </div>
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="relative">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                              className={cn(
                                "h-8 w-8 p-0 rounded-md transition-colors",
                                showEmojiPicker 
                                  ? "text-yellow-400 bg-yellow-400/10" 
                                  : "text-white/60 hover:text-white hover:bg-white/10"
                              )}
                            >
                              <Smile className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent className="bg-gray-900 text-white border-gray-700">
                            <p>Add Emoji</p>
                          </TooltipContent>
                        </Tooltip>
                        
                        {/* Emoji Picker */}
                        {showEmojiPicker && (
                          <div className="emoji-picker absolute bottom-full left-0 mb-2 w-64 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50">
                            <div className="p-3">
                              <h4 className="text-white text-sm font-medium mb-2">Choose Emoji</h4>
                              <div className="grid grid-cols-8 gap-1 max-h-32 overflow-y-auto">
                                {emojis.map((emoji, index) => (
                                  <button
                                    key={index}
                                    onClick={() => insertEmoji(emoji)}
                                    className="w-6 h-6 text-lg hover:bg-gray-800 rounded transition-colors flex items-center justify-center"
                                    title={emoji}
                                  >
                                    {emoji}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => setShowFormatting(!showFormatting)}
                            className={cn(
                              "h-8 w-8 p-0 rounded-md transition-colors",
                              showFormatting 
                                ? "text-purple-400 bg-purple-400/10" 
                                : "text-white/60 hover:text-white hover:bg-white/10"
                            )}
                          >
                            {showFormatting ? <ChevronDown className="w-4 h-4" /> : <Type className="w-4 h-4" />}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-gray-900 text-white border-gray-700">
                          <p>Text Formatting</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* Horizontal Formatting Options */}
                      {showFormatting && (
                        <>
                          <div className="w-px h-4 bg-white/20 mx-1" />
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => applyFormatting('bold')}
                                className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                              >
                                <Bold className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white border-gray-700">
                              <p>Bold</p>
                            </TooltipContent>
                          </Tooltip>
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => applyFormatting('italic')}
                                className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                              >
                                <Italic className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white border-gray-700">
                              <p>Italic</p>
                            </TooltipContent>
                          </Tooltip>
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => applyFormatting('underline')}
                                className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                              >
                                <Underline className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white border-gray-700">
                              <p>Underline</p>
                            </TooltipContent>
                          </Tooltip>
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => applyFormatting('insertUnorderedList')}
                                className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                              >
                                <List className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white border-gray-700">
                              <p>Bullet List</p>
                            </TooltipContent>
                          </Tooltip>
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => applyFormatting('insertOrderedList')}
                                className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                              >
                                <ListOrdered className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white border-gray-700">
                              <p>Numbered List</p>
                            </TooltipContent>
                          </Tooltip>
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => applyFormatting('formatBlock', 'blockquote')}
                                className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10 rounded-md"
                              >
                                <Quote className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white border-gray-700">
                              <p>Quote</p>
                            </TooltipContent>
                          </Tooltip>
                        </>
                      )}
                    </div>
                    
                    <Button
                      onClick={handleSendMessage}
                      disabled={!messageContent.trim() || isSending}
                      size="sm"
                      className={cn(
                        "h-8 px-3 transition-all rounded-md",
                        messageContent.trim() && !isSending
                          ? "bg-purple-500 hover:bg-purple-600 text-white"
                          : "bg-white/10 text-white/40 cursor-not-allowed"
                      )}
                    >
                      {isSending ? (
                        <>
                          <div className="w-4 h-4 mr-1 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          {pendingImages.length > 0 ? 'Uploading...' : 'Sending...'}
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-1" />
                          Send
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {/* Helper Text */}
                  <div className="mt-2 text-xs text-white/40">
                    Press Enter to send, Shift + Enter for new line
                  </div>
                </div>
              </TooltipProvider>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 