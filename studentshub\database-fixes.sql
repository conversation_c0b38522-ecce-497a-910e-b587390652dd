-- StudentsHub Database Fixes for Lecture Notes
-- Run this SQL script in your Supabase SQL editor

-- Ensure uuid extension is available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Fix files table structure
ALTER TABLE files 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Ensure files table has all required columns for lecture notes
ALTER TABLE files 
ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'others' 
    CHECK (category IN ('lecture_notes', 'summaries', 'practice_materials', 'exams', 'others'));

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS description TEXT;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS page_count INTEGER;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS like_count INTEGER DEFAULT 0;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS download_count INTEGER DEFAULT 0;

ALTER TABLE files 
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE;

-- Create file likes table
CREATE TABLE IF NOT EXISTS file_likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  file_id UUID NOT NULL REFERENCES files(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(file_id, user_id)
);

-- Create function to update file like count
CREATE OR REPLACE FUNCTION update_file_like_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE files SET like_count = like_count + 1 WHERE id = NEW.file_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE files SET like_count = GREATEST(like_count - 1, 0) WHERE id = OLD.file_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic like count updates
DROP TRIGGER IF EXISTS file_like_count_trigger ON file_likes;
CREATE TRIGGER file_like_count_trigger
  AFTER INSERT OR DELETE ON file_likes
  FOR EACH ROW EXECUTE FUNCTION update_file_like_count();

-- Function to increment download count
CREATE OR REPLACE FUNCTION increment_download_count(file_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE files SET download_count = download_count + 1 WHERE id = file_id;
END;
$$ LANGUAGE plpgsql;

-- Ensure RLS policies are set up for files
ALTER TABLE files ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can see files from courses they have access to
DROP POLICY IF EXISTS "Users can see course files" ON files;
CREATE POLICY "Users can see course files" ON files
  FOR SELECT USING (
    course_id IN (
      SELECT id FROM courses 
      WHERE user_id = auth.uid() OR 
            id IN (SELECT course_id FROM course_enrollments WHERE user_id = auth.uid())
    )
  );

-- RLS Policy: Users can upload files to courses they own or are enrolled in
DROP POLICY IF EXISTS "Users can upload course files" ON files;
CREATE POLICY "Users can upload course files" ON files
  FOR INSERT WITH CHECK (
    course_id IN (
      SELECT id FROM courses 
      WHERE user_id = auth.uid() OR 
            id IN (SELECT course_id FROM course_enrollments WHERE user_id = auth.uid())
    )
  );

-- RLS Policy: Users can update their own files
DROP POLICY IF EXISTS "Users can update own files" ON files;
CREATE POLICY "Users can update own files" ON files
  FOR UPDATE USING (user_id = auth.uid());

-- RLS Policy: Users can delete their own files
DROP POLICY IF EXISTS "Users can delete own files" ON files;
CREATE POLICY "Users can delete own files" ON files
  FOR DELETE USING (user_id = auth.uid());

-- RLS policies for file_likes
ALTER TABLE file_likes ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can see all likes for files they can access
DROP POLICY IF EXISTS "Users can see file likes" ON file_likes;
CREATE POLICY "Users can see file likes" ON file_likes
  FOR SELECT USING (
    file_id IN (
      SELECT id FROM files 
      WHERE course_id IN (
        SELECT id FROM courses 
        WHERE user_id = auth.uid() OR 
              id IN (SELECT course_id FROM course_enrollments WHERE user_id = auth.uid())
      )
    )
  );

-- RLS Policy: Users can like files they can access
DROP POLICY IF EXISTS "Users can like files" ON file_likes;
CREATE POLICY "Users can like files" ON file_likes
  FOR INSERT WITH CHECK (
    file_id IN (
      SELECT id FROM files 
      WHERE course_id IN (
        SELECT id FROM courses 
        WHERE user_id = auth.uid() OR 
              id IN (SELECT course_id FROM course_enrollments WHERE user_id = auth.uid())
      )
    ) AND user_id = auth.uid()
  );

-- RLS Policy: Users can unlike their own likes
DROP POLICY IF EXISTS "Users can unlike own likes" ON file_likes;
CREATE POLICY "Users can unlike own likes" ON file_likes
  FOR DELETE USING (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_files_course_id ON files(course_id);
CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
CREATE INDEX IF NOT EXISTS idx_files_category ON files(category);
CREATE INDEX IF NOT EXISTS idx_files_like_count ON files(like_count);
CREATE INDEX IF NOT EXISTS idx_file_likes_file_id ON file_likes(file_id);
CREATE INDEX IF NOT EXISTS idx_file_likes_user_id ON file_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_file_likes_composite ON file_likes(file_id, user_id);

-- Ensure storage bucket exists for course files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('course-files', 'course-files', true, 52428800, ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png', 'image/gif', 'image/webp'])
ON CONFLICT (id) DO NOTHING;

-- Storage policies for course-files bucket
CREATE POLICY "Users can upload course files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'course-files' AND
    (auth.uid() IS NOT NULL)
  );

CREATE POLICY "Users can read course files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'course-files'
  );

CREATE POLICY "Users can update own course files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'course-files' AND
    (storage.foldername(name))[1] = (SELECT university FROM profiles WHERE id = auth.uid())
  );

CREATE POLICY "Users can delete own course files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'course-files' AND
    (storage.foldername(name))[1] = (SELECT university FROM profiles WHERE id = auth.uid())
  );

-- Grant necessary permissions
GRANT ALL ON files TO authenticated;
GRANT ALL ON file_likes TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
