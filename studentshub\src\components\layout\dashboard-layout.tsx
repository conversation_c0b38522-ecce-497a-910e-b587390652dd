"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { 
  ChevronLeft, 
  ChevronRight,
  Menu,
  MessageCircle,
  X
} from "lucide-react"

interface DashboardLayoutProps {
  children: React.ReactNode
  leftSidebar: React.ReactNode
  rightSidebar: React.ReactNode
}

export function DashboardLayout({ children, leftSidebar, rightSidebar }: DashboardLayoutProps) {
  const [isLeftSidebarOpen, setIsLeftSidebarOpen] = useState(true)
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(true)
  const [isMobile, setIsMobile] = useState(false)

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024
      setIsMobile(mobile)
      
      // Auto-collapse sidebars on mobile
      if (mobile) {
        setIsLeftSidebarOpen(false)
        setIsRightSidebarOpen(false)
      } else {
        setIsLeftSidebarOpen(true)
        setIsRightSidebarOpen(true)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex overflow-hidden">
      {/* Mobile Overlay */}
      {isMobile && (isLeftSidebarOpen || isRightSidebarOpen) && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => {
            setIsLeftSidebarOpen(false)
            setIsRightSidebarOpen(false)
          }}
        />
      )}

      {/* Left Sidebar */}
      <div className={cn(
        "transition-all duration-300 ease-in-out flex-shrink-0 relative",
        isLeftSidebarOpen ? "w-64" : "w-0",
        isMobile && isLeftSidebarOpen && "fixed inset-y-0 left-0 z-50 w-64"
      )}>
        <div className={cn(
          "h-full bg-slate-900/95 backdrop-blur-xl border-r border-white/10 overflow-hidden",
          "transition-all duration-300 ease-in-out",
          isLeftSidebarOpen ? "w-64" : "w-0"
        )}>
          {isLeftSidebarOpen && (
            <>
              {leftSidebar}
              {/* Left Sidebar Close Button */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-2 z-10 h-8 w-8 bg-slate-800/50 hover:bg-slate-700/50 text-white/60 hover:text-white transition-all duration-300"
                onClick={() => setIsLeftSidebarOpen(false)}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className={cn(
        "flex-1 flex flex-col min-w-0 relative transition-all duration-300 ease-in-out h-full",
        // Add margins when sidebars are closed
        !isLeftSidebarOpen && !isMobile && "ml-4",
        !isRightSidebarOpen && !isMobile && "mr-4"
      )}>
        {/* Main Content - Only this should scroll */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden">
          <div className={cn(
            "min-h-full",
            // Add padding to prevent overlap with floating buttons
            !isLeftSidebarOpen && "pl-8"
          )}>
            {children}
          </div>
        </div>
      </div>

      {/* Right Sidebar */}
      <div className={cn(
        "transition-all duration-300 ease-in-out flex-shrink-0 relative",
        isRightSidebarOpen ? "w-96" : "w-0",
        isMobile && isRightSidebarOpen && "fixed inset-y-0 right-0 z-50 w-96"
      )}>
        <div className={cn(
          "h-full bg-slate-900/95 backdrop-blur-xl border-l border-white/10 overflow-hidden",
          "transition-all duration-300 ease-in-out",
          isRightSidebarOpen ? "w-96" : "w-0"
        )}>
          {isRightSidebarOpen && (
            <>
              {rightSidebar}
              {/* Right Sidebar Close Button */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 left-2 z-10 h-8 w-8 bg-slate-800/50 hover:bg-slate-700/50 text-white/60 hover:text-white transition-all duration-300"
                onClick={() => setIsRightSidebarOpen(false)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Floating Toggle Buttons - Bottom Left */}
      <div className="fixed bottom-6 left-6 z-30 flex flex-col gap-3">
        {/* Navigation Sidebar Toggle */}
        {!isLeftSidebarOpen && (
          <Button
            variant="outline"
            size="icon"
            className={cn(
              "h-12 w-12 rounded-full shadow-lg backdrop-blur-xl transition-all duration-300",
              "bg-slate-900/90 border-white/20 text-white hover:bg-slate-800/90",
              "hover:scale-110 hover:shadow-xl hover:border-white/30",
              "group"
            )}
            onClick={() => setIsLeftSidebarOpen(true)}
          >
            <Menu className="h-5 w-5 transition-transform group-hover:scale-110" />
          </Button>
        )}
        
        {/* AI Chat Sidebar Toggle */}
        {!isRightSidebarOpen && (
          <Button
            variant="outline"
            size="icon"
            className={cn(
              "h-12 w-12 rounded-full shadow-lg backdrop-blur-xl transition-all duration-300",
              "bg-gradient-to-r from-purple-600/90 to-pink-600/90 border-purple-400/30 text-white",
              "hover:from-purple-500/90 hover:to-pink-500/90 hover:border-purple-300/40",
              "hover:scale-110 hover:shadow-xl",
              "group"
            )}
            onClick={() => setIsRightSidebarOpen(true)}
          >
            <MessageCircle className="h-5 w-5 transition-transform group-hover:scale-110" />
          </Button>
        )}
      </div>
    </div>
  )
} 