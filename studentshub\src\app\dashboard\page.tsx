"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { auth, db } from "@/lib/supabase"
import { Loader2 } from "lucide-react"
import ProfileCompletionModal from "@/components/ProfileCompletionModal"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { NavigationSidebar } from "@/components/layout/navigation-sidebar"
import { AIChatSidebar } from "@/components/layout/ai-chat-sidebar"
import { DashboardContent } from "@/components/dashboard/dashboard-content"

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [showProfileModal, setShowProfileModal] = useState(false)

  useEffect(() => {
    const checkUser = async () => {
      const { user, error } = await auth.getUser()
      
      if (error || !user) {
        router.push("/login")
        return
      }

      setUser(user)

      // Check if profile is complete
      const { data: profileData, error: profileError } = await db.getProfile(user.id)
      
      if (profileData) {
        setProfile(profileData)
        if (!profileData.is_profile_complete) {
          setShowProfileModal(true)
        }
      } else if (profileError) {
        console.error('Profile error:', profileError)
        // If no profile exists, show the completion modal
        setShowProfileModal(true)
      }
      
      setLoading(false)
    }

    checkUser()
  }, [router])

  const handleProfileComplete = async () => {
    setShowProfileModal(false)
    // Refresh profile data
    const { data: profileData } = await db.getProfile(user.id)
    setProfile(profileData)
  }

  const handleSignOut = async () => {
    await auth.signOut()
    router.push("/")
  }

  const handleExpandAIChat = () => {
    router.push("/ai-assistant")
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-white mx-auto mb-4" />
          <p className="text-white">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <DashboardLayout
        leftSidebar={
          <NavigationSidebar 
            user={user} 
            profile={profile} 
            onSignOut={handleSignOut} 
          />
        }
        rightSidebar={
          <AIChatSidebar onExpand={handleExpandAIChat} />
        }
      >
        <DashboardContent user={user} profile={profile} />
      </DashboardLayout>

      {/* Profile Completion Modal */}
      {showProfileModal && user && (
        <ProfileCompletionModal 
          user={user} 
          onComplete={handleProfileComplete}
        />
      )}
    </>
  )
} 