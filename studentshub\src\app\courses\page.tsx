"use client"

import { use<PERSON>ffe<PERSON>, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { auth, db } from "@/lib/supabase"
import { cn } from "@/lib/utils"
import { FadeIn } from "@/components/ui/fade-in"
import { 
  BookOpen, 
  Users, 
  UserPlus, 
  UserMinus,
  Plus,
  Lock,
  Globe,
  FileText,
  Calendar,
  GraduationCap,
  AlertCircle,
  Loader2,
  Search,
  Filter,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Sparkles,
  TrendingUp,
  Clock,
  Star,
  Building,
  ChevronRight,
  Zap
} from "lucide-react"

interface Course {
  id: string
  title: string
  description: string
  university: string
  is_public: boolean
  course_code: string
  semester: string
  professor_name: string
  enrollment_count: number
  user_id: string
  created_at: string
  color: string
}

interface OnlineUser {
  user_id: string
  last_seen: string
  profiles: {
    name: string
    username: string
    is_username_public: boolean
  }
}

export default function CoursesPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [publicCourses, setPublicCourses] = useState<Course[]>([])
  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([])
  const [enrolledCourseIds, setEnrolledCourseIds] = useState<string[]>([])
  const [onlineUsers, setOnlineUsers] = useState<{[courseId: string]: OnlineUser[]}>({})
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [createLoading, setCreateLoading] = useState(false)
  const [error, setError] = useState("")
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})

  const [newCourse, setNewCourse] = useState({
    title: "",
    description: "",
    courseCode: "",
    semester: "Winter 2024",
    professorName: "",
    isPublic: true,
    color: "#8B5CF6"
  })

  const semesterOptions = [
    "Winter 2024", "Summer 2024", "Winter 2025", "Summer 2025"
  ]

  const colorOptions = [
    "#8B5CF6", "#EC4899", "#06B6D4", "#10B981", 
    "#F59E0B", "#EF4444", "#8B5A2B", "#6366F1"
  ]

  useEffect(() => {
    checkUserAndLoadData()
    
    // Check if create modal should be opened from URL params
    if (searchParams.get('create') === 'true') {
      setShowCreateModal(true)
    }
  }, [searchParams])

  const checkUserAndLoadData = async () => {
    const { user, error } = await auth.getUser()
    
    if (error || !user) {
      router.push("/login")
      return
    }

    setUser(user)
    await loadCourses(user.id)
    setLoading(false)
  }

  const loadCourses = async (userId: string) => {
    try {
      // Load public courses
      const { data: publicData } = await db.getPublicCourses()
      if (publicData) {
        setPublicCourses(publicData)
        
        // Load online users for each course
        const onlineData: {[courseId: string]: OnlineUser[]} = {}
        for (const course of publicData) {
          const { data: online } = await db.getOnlineUsers(course.id)
          if (online) {
            onlineData[course.id] = online
          }
        }
        setOnlineUsers(onlineData)
      }

      // Load user's enrolled courses
      const { data: enrolledData } = await db.getEnrolledCourses(userId)
      if (enrolledData) {
        setEnrolledCourses(enrolledData)
      }

      // Load user's enrollments
      const { enrollments } = await db.getUserEnrollments(userId)
      setEnrolledCourseIds(enrollments)
      
    } catch (err) {
      console.error('Error loading courses:', err)
    }
  }

  const handleEnroll = async (courseId: string) => {
    if (!user) return
    
    try {
      const { error } = await db.enrollInCourse(courseId, user.id)
      if (error) {
        setError(error.message)
      } else {
        setEnrolledCourseIds(prev => [...prev, courseId])
        // Update enrollment count locally
        setPublicCourses(prev => 
          prev.map(course => 
            course.id === courseId 
              ? { ...course, enrollment_count: course.enrollment_count + 1 }
              : course
          )
        )
      }
    } catch (err: any) {
      setError(err.message)
    }
  }

  const handleUnenroll = async (courseId: string) => {
    if (!user) return
    
    try {
      const { error } = await db.unenrollFromCourse(courseId, user.id)
      if (error) {
        setError(error.message)
      } else {
        setEnrolledCourseIds(prev => prev.filter(id => id !== courseId))
        // Update enrollment count locally
        setPublicCourses(prev => 
          prev.map(course => 
            course.id === courseId 
              ? { ...course, enrollment_count: Math.max(0, course.enrollment_count - 1) }
              : course
          )
        )
      }
    } catch (err: any) {
      setError(err.message)
    }
  }

  const validateCourseForm = async () => {
    const errors: {[key: string]: string} = {}
    
    if (!newCourse.title.trim()) {
      errors.title = "Course title is required"
    }
    
    // Course code is only required for public courses
    if (newCourse.isPublic && !newCourse.courseCode.trim()) {
      errors.courseCode = "Course code is required for public courses"
    }
    
    // Check for duplicates if creating a public course
    if (newCourse.isPublic && newCourse.title.trim() && newCourse.courseCode.trim()) {
      const { exists, conflictingCourses } = await db.checkCourseExists(
        newCourse.title.trim(),
        newCourse.courseCode.trim()
      )
      
      if (exists && conflictingCourses) {
        const titleConflict = conflictingCourses.find(c => c.title === newCourse.title.trim())
        const codeConflict = conflictingCourses.find(c => c.course_code === newCourse.courseCode.trim())
        
        if (titleConflict) {
          errors.title = "A public course with this title already exists"
        }
        if (codeConflict) {
          errors.courseCode = "A public course with this code already exists"
        }
      }
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleCreateCourse = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setCreateLoading(true)
    setError("")

    const isValid = await validateCourseForm()
    if (!isValid) {
      setCreateLoading(false)
      return
    }

    try {
      const courseData = {
        user_id: user.id,
        title: newCourse.title.trim(),
        description: newCourse.description.trim(),
        course_code: newCourse.courseCode.trim() || null, // Allow empty course code for private courses
        semester: newCourse.semester,
        professor_name: newCourse.professorName.trim(),
        is_public: newCourse.isPublic,
        university: 'TU-Darmstadt',
        color: newCourse.color
      }

      const { data, error } = await db.createCourse(courseData)
      
      if (error) {
        setError(error.message)
      } else if (data) {
        // Auto-enroll the creator in their own course
        await db.enrollInCourse(data.id, user.id)
        
        setShowCreateModal(false)
        setNewCourse({
          title: "",
          description: "",
          courseCode: "",
          semester: "Winter 2024",
          professorName: "",
          isPublic: true,
          color: "#8B5CF6"
        })
        setValidationErrors({})
        
        // Navigate to the new course
        router.push(`/courses/${data.id}`)
      }
    } catch (err: any) {
      setError(err.message)
    } finally {
      setCreateLoading(false)
    }
  }

  const handleCourseClick = (courseId: string) => {
    router.push(`/courses/${courseId}`)
  }

  const filteredPublicCourses = publicCourses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (course.course_code && course.course_code.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (course.professor_name && course.professor_name.toLowerCase().includes(searchTerm.toLowerCase()))
    
    if (selectedFilter === "all") return matchesSearch
    if (selectedFilter === "enrolled") return matchesSearch && enrolledCourseIds.includes(course.id)
    // Popular: courses with enrollment_count in top 25% or at least 10 students
    if (selectedFilter === "popular") {
      const maxEnrollment = Math.max(...publicCourses.map(c => c.enrollment_count))
      const popularThreshold = Math.max(10, maxEnrollment * 0.75) // Top 25% or minimum 10
      return matchesSearch && course.enrollment_count >= popularThreshold
    }
    if (selectedFilter === "new") {
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return matchesSearch && new Date(course.created_at) > weekAgo
    }
    
    return matchesSearch
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-white mx-auto mb-4" />
          <p className="text-white">Loading courses...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <FadeIn delay={0.1}>
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/dashboard")}
                className="border-white/20 text-white hover:bg-white/10 transform hover:scale-105 transition-all duration-200"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center transform hover:scale-105 transition-all duration-200">
                  <BookOpen className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-white">Course Discovery</h1>
                  <p className="text-white/70">Find existing courses or create your own</p>
                </div>
              </div>
            </div>
            
            <Button 
              onClick={() => setShowCreateModal(true)}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white transform hover:scale-105 transition-all duration-200"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Course
            </Button>
          </div>
        </FadeIn>

        {/* Search and Filters */}
        <FadeIn delay={0.2}>
          <div className="mb-8 space-y-4">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search courses, codes, professors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div className="flex items-center gap-3">
                {["all", "enrolled", "popular", "new"].map((filter) => (
                  <Button
                    key={filter}
                    variant={selectedFilter === filter ? "default" : "outline"}
                    size="sm"
                    className={cn(
                      "capitalize transition-all duration-200",
                      selectedFilter === filter
                        ? "bg-purple-500 text-white scale-105 shadow-lg"
                        : "border-white/20 text-white/80 hover:bg-white/10 hover:scale-105"
                    )}
                    onClick={() => setSelectedFilter(filter)}
                  >
                    {filter === "popular" && <TrendingUp className="h-3 w-3 mr-1" />}
                    {filter === "new" && <Sparkles className="h-3 w-3 mr-1" />}
                    {filter === "enrolled" && <CheckCircle className="h-3 w-3 mr-1" />}
                    {filter}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </FadeIn>

        {/* Course Tabs */}
        <FadeIn delay={0.3}>
          <Tabs defaultValue="discover" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-white/10 border border-white/20 mb-8">
              <TabsTrigger value="discover" className="text-white data-[state=active]:bg-purple-500 data-[state=active]:text-white">
                <Globe className="w-4 h-4 mr-2" />
                Discover Courses ({filteredPublicCourses.length})
              </TabsTrigger>
              <TabsTrigger value="my" className="text-white data-[state=active]:bg-purple-500 data-[state=active]:text-white">
                <BookOpen className="w-4 h-4 mr-2" />
                My Courses ({enrolledCourses.length})
              </TabsTrigger>
            </TabsList>

            {/* Discover Courses Tab */}
            <TabsContent value="discover">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredPublicCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onlineUsers={onlineUsers[course.id] || []}
                    isEnrolled={enrolledCourseIds.includes(course.id)}
                    isOwner={course.user_id === user?.id}
                    onEnroll={() => handleEnroll(course.id)}
                    onUnenroll={() => handleUnenroll(course.id)}
                    onClick={() => handleCourseClick(course.id)}
                  />
                ))}
                
                {filteredPublicCourses.length === 0 && (
                  <div className="col-span-full">
                    <Card className="bg-white/5 backdrop-blur-xl border-white/10 border-dashed">
                      <CardContent className="flex flex-col items-center justify-center py-16">
                        <div className="w-16 h-16 bg-purple-500/20 rounded-2xl flex items-center justify-center mb-4">
                          <Search className="h-8 w-8 text-purple-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-white mb-2">No courses found</h3>
                        <p className="text-white/70 text-center mb-6 max-w-md">
                          {searchTerm ? "Try adjusting your search terms or filters." : "No public courses available yet."}
                        </p>
                        <Button
                          onClick={() => setShowCreateModal(true)}
                          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create First Course
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            </TabsContent>

            {/* My Courses Tab */}
            <TabsContent value="my">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {enrolledCourses.map((course: Course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onlineUsers={onlineUsers[course.id] || []}
                    isEnrolled={true}
                    isOwner={course.user_id === user?.id}
                    onEnroll={() => {}}
                    onUnenroll={() => handleUnenroll(course.id)}
                    onClick={() => handleCourseClick(course.id)}
                  />
                ))}
                
                {enrolledCourses.length === 0 && (
                  <div className="col-span-full">
                    <Card className="bg-white/5 backdrop-blur-xl border-white/10 border-dashed">
                      <CardContent className="flex flex-col items-center justify-center py-16">
                        <div className="w-16 h-16 bg-purple-500/20 rounded-2xl flex items-center justify-center mb-4">
                          <BookOpen className="h-8 w-8 text-purple-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-white mb-2">No courses created yet</h3>
                        <p className="text-white/70 text-center mb-6 max-w-md">
                          Start your teaching journey by creating your first course.
                        </p>
                        <Button
                          onClick={() => setShowCreateModal(true)}
                          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create Your First Course
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </FadeIn>

        {/* Create Course Modal */}
        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
          <DialogContent className="sm:max-w-[600px] bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 border border-white/20 max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-white text-2xl">Create New Course</DialogTitle>
              <DialogDescription className="text-white/70">
                Create a new course for your university community. Public courses can be discovered and joined by other students.
              </DialogDescription>
            </DialogHeader>
            
            {error && (
              <div className="p-3 rounded-lg bg-red-500/20 border border-red-500/30 flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                <span className="text-red-300 text-sm">{error}</span>
              </div>
            )}

            <form onSubmit={handleCreateCourse} className="space-y-6">
              {/* Course Visibility */}
              <div className="space-y-3">
                <Label className="text-white text-sm font-medium">Course Visibility</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div
                    className={cn(
                      "p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105",
                      newCourse.isPublic
                        ? "border-green-400 bg-green-500/20"
                        : "border-white/20 bg-white/5"
                    )}
                    onClick={() => setNewCourse(prev => ({ ...prev, isPublic: true }))}
                  >
                    <div className="flex items-center gap-3">
                      <Globe className="w-5 h-5 text-green-400" />
                      <div>
                        <h4 className="text-white font-medium">Public Course</h4>
                        <p className="text-white/70 text-xs">Discoverable by all students</p>
                      </div>
                    </div>
                  </div>
                  
                  <div
                    className={cn(
                      "p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105",
                      !newCourse.isPublic
                        ? "border-yellow-400 bg-yellow-500/20"
                        : "border-white/20 bg-white/5"
                    )}
                    onClick={() => setNewCourse(prev => ({ ...prev, isPublic: false }))}
                  >
                    <div className="flex items-center gap-3">
                      <Lock className="w-5 h-5 text-yellow-400" />
                      <div>
                        <h4 className="text-white font-medium">Private Course</h4>
                        <p className="text-white/70 text-xs">Only visible to you</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Course Title */}
              <div className="space-y-2">
                <Label htmlFor="title" className="text-white">Course Title *</Label>
                <Input
                  id="title"
                  value={newCourse.title}
                  onChange={(e) => setNewCourse(prev => ({ ...prev, title: e.target.value }))}
                  className={cn(
                    "bg-white/10 border-white/20 text-white placeholder:text-white/50",
                    validationErrors.title && "border-red-400 focus:ring-red-400"
                  )}
                  placeholder="Advanced Machine Learning"
                  required
                />
                {validationErrors.title && (
                  <p className="text-red-300 text-sm flex items-center gap-1">
                    <XCircle className="w-3 h-3" />
                    {validationErrors.title}
                  </p>
                )}
              </div>

              {/* Course Description */}
              <div className="space-y-2">
                <Label htmlFor="description" className="text-white">Description</Label>
                <textarea
                  id="description"
                  value={newCourse.description}
                  onChange={(e) => setNewCourse(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder:text-white/50 resize-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Brief description of the course content and objectives..."
                  rows={3}
                />
              </div>

              {/* Course Code and Semester */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="courseCode" className="text-white">
                    Course Code {newCourse.isPublic ? "*" : "(Optional)"}
                  </Label>
                  <Input
                    id="courseCode"
                    value={newCourse.courseCode}
                    onChange={(e) => setNewCourse(prev => ({ ...prev, courseCode: e.target.value }))}
                    className={cn(
                      "bg-white/10 border-white/20 text-white placeholder:text-white/50",
                      validationErrors.courseCode && "border-red-400 focus:ring-red-400"
                    )}
                    placeholder={newCourse.isPublic ? "CS-401 or 20-00-0001" : "CS-401 (optional)"}
                    required={newCourse.isPublic}
                  />
                  <p className="text-white/60 text-xs">
                    {newCourse.isPublic 
                      ? "Enter the official university course code as per TU Darmstadt catalog (e.g., CS-401, MATH-205)"
                      : "Optional: Add a course code for personal organization"
                    }
                  </p>
                  {validationErrors.courseCode && (
                    <p className="text-red-300 text-sm flex items-center gap-1">
                      <XCircle className="w-3 h-3" />
                      {validationErrors.courseCode}
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="semester" className="text-white">Semester</Label>
                  <select
                    id="semester"
                    value={newCourse.semester}
                    onChange={(e) => setNewCourse(prev => ({ ...prev, semester: e.target.value }))}
                    className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:ring-2 focus:ring-purple-500"
                  >
                    {semesterOptions.map(semester => (
                      <option key={semester} value={semester} className="bg-slate-800">{semester}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Professor Name */}
              <div className="space-y-2">
                <Label htmlFor="professorName" className="text-white">Professor Name</Label>
                <Input
                  id="professorName"
                  value={newCourse.professorName}
                  onChange={(e) => setNewCourse(prev => ({ ...prev, professorName: e.target.value }))}
                  className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                  placeholder="Prof. Dr. Schmidt"
                />
              </div>

              {/* Course Color */}
              <div className="space-y-3">
                <Label className="text-white">Course Color</Label>
                <div className="grid grid-cols-4 gap-2">
                  {colorOptions.map(color => (
                    <button
                      key={color}
                      type="button"
                      className={cn(
                        "w-12 h-12 rounded-lg border-2 transition-all duration-200 hover:scale-110",
                        newCourse.color === color ? "border-white scale-110" : "border-white/20"
                      )}
                      style={{ backgroundColor: color }}
                      onClick={() => setNewCourse(prev => ({ ...prev, color }))}
                    />
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 border-white/20 text-white hover:bg-white/10"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createLoading}
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                >
                  {createLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Create Course
                    </>
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}

// Enhanced Course Card Component
interface CourseCardProps {
  course: Course
  onlineUsers: OnlineUser[]
  isEnrolled: boolean
  isOwner: boolean
  onEnroll: () => void
  onUnenroll: () => void
  onClick: () => void
}

function CourseCard({ course, onlineUsers, isEnrolled, isOwner, onEnroll, onUnenroll, onClick }: CourseCardProps) {
  return (
    <Card className="bg-white/10 backdrop-blur-xl border border-white/20 hover:border-white/30 transition-all duration-300 cursor-pointer group hover:scale-[1.02] hover:bg-white/15">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: course.color }}
                />
                {course.course_code && (
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-200 text-xs">
                    {course.course_code}
                  </Badge>
                )}
              {course.is_public ? (
                <Globe className="w-4 h-4 text-green-400" />
              ) : (
                <Lock className="w-4 h-4 text-yellow-400" />
              )}
              {isOwner && (
                <Badge variant="outline" className="border-blue-400 text-blue-400 text-xs">
                  Owner
                </Badge>
              )}
            </div>
            <CardTitle className="text-white text-lg group-hover:text-white leading-tight">
              {course.title}
            </CardTitle>
            <CardDescription className="text-white/70 mt-1 line-clamp-2">
              {course.description || "No description provided"}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Professor and Semester */}
          {(course.professor_name || course.semester) && (
            <div className="space-y-2">
              {course.professor_name && (
                <div className="flex items-center gap-2 text-sm text-white/70">
                  <GraduationCap className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{course.professor_name}</span>
                </div>
              )}
              <div className="flex items-center gap-2 text-sm text-white/70">
                <Calendar className="w-4 h-4 flex-shrink-0" />
                <span>{course.semester}</span>
              </div>
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2 text-white/70">
              <Users className="w-4 h-4" />
              <span>{course.enrollment_count} enrolled</span>
            </div>
            <div className="flex items-center gap-2 text-white/70">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>{onlineUsers.length} online</span>
            </div>
          </div>

          {/* Online Users */}
          {onlineUsers.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-white mb-2">Online Now:</h4>
              <div className="flex flex-wrap gap-1">
                {onlineUsers.slice(0, 3).map((user, index) => (
                  <Badge key={index} variant="outline" className="border-green-400/50 text-green-300 text-xs">
                    {user.profiles.is_username_public ? user.profiles.username : 'Anonymous'}
                  </Badge>
                ))}
                {onlineUsers.length > 3 && (
                  <Badge variant="outline" className="border-white/30 text-white/70 text-xs">
                    +{onlineUsers.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          <Separator className="border-white/20" />

          {/* Action Buttons */}
          <div className="flex gap-2">
            {isEnrolled ? (
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onClick()
                }}
                className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
              >
                <FileText className="w-4 h-4 mr-2" />
                Open Course
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onEnroll()
                }}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Enroll
              </Button>
            )}
            
            {isEnrolled && !isOwner && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation()
                  onUnenroll()
                }}
                className="border-red-400/50 text-red-300 hover:bg-red-500/10 transition-all duration-200"
              >
                <UserMinus className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 