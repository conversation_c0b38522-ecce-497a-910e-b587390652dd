"use client"

import { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { 
  BookOpen,
  Brain,
  FileText,
  FolderOpen,
  Briefcase,
  Settings,
  LogOut,
  Home,
  Star,
  TrendingUp,
  Users,
  Calendar,
  Bell,
  Search,
  Plus,
  GraduationCap,
  MessageCircle
} from "lucide-react"

interface NavigationItem {
  id: string
  label: string
  icon: React.ReactNode
  href: string
  badge?: string
  isActive?: boolean
  subItems?: NavigationItem[]
}

interface NavigationSidebarProps {
  user?: any
  profile?: any
  onSignOut?: () => void
}

export function NavigationSidebar({ user, profile, onSignOut }: NavigationSidebarProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const navigationItems: NavigationItem[] = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <Home className="h-5 w-5" />,
      href: "/dashboard",
      isActive: pathname === "/dashboard"
    },
    {
      id: "courses",
      label: "My Courses",
      icon: <BookOpen className="h-5 w-5" />,
      href: "/courses",
      badge: "3",
      isActive: pathname === "/courses"
    },
    {
      id: "ai-assistant",
      label: "AI Assistant",
      icon: <Brain className="h-5 w-5" />,
      href: "/ai-assistant",
      isActive: pathname === "/ai-assistant"
    },
    {
      id: "notes",
      label: "My Notes",
      icon: <FileText className="h-5 w-5" />,
      href: "/notes",
      badge: "12",
      isActive: pathname === "/notes"
    },
    {
      id: "files",
      label: "Files",
      icon: <FolderOpen className="h-5 w-5" />,
      href: "/files",
      isActive: pathname === "/files"
    },
    {
      id: "messages",
      label: "Messages",
      icon: <MessageCircle className="h-5 w-5" />,
      href: "/messages",
      badge: "2",
      isActive: pathname === "/messages"
    },
    {
      id: "career",
      label: "Career Tools",
      icon: <Briefcase className="h-5 w-5" />,
      href: "/career",
      badge: "New",
      isActive: pathname.startsWith("/career")
    }
  ]

  const quickActions = [
    {
      id: "new-course",
      label: "New Course",
      icon: <Plus className="h-4 w-4" />,
      onClick: () => router.push("/courses?create=true")
    },
    {
      id: "quick-note",
      label: "Quick Note",
      icon: <FileText className="h-4 w-4" />,
      onClick: () => router.push("/notes?create=true")
    },
    {
      id: "manage-files",
      label: "Manage Files",
      icon: <FolderOpen className="h-4 w-4" />,
      onClick: () => router.push("/files")
    }
  ]

  const handleNavigation = (href: string) => {
    router.push(href)
  }

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* User Profile Section */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profile?.avatar_url} />
            <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
              {profile?.name ? profile.name.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-white truncate">
              {profile?.name || user?.email || 'User'}
            </p>
            <p className="text-xs text-white/60 truncate">
              {profile?.university || 'University Student'}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-white/10">
        <h3 className="text-xs font-semibold text-white/80 uppercase tracking-wider mb-3">
          Quick Actions
        </h3>
        <div className="space-y-2">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className="w-full justify-start h-8 text-white/80 hover:text-white hover:bg-white/10"
              onClick={action.onClick}
            >
              {action.icon}
              <span className="ml-2 text-xs">{action.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Navigation Menu */}
      <div className="flex-1 p-4 overflow-y-auto">
        <nav className="space-y-2">
          {navigationItems.map((item) => (
            <div key={item.id}>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start h-11 transition-all duration-200",
                  item.isActive
                    ? "bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border border-purple-400/30"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                )}
                onClick={() => handleNavigation(item.href)}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    {item.icon}
                    <span className="ml-3 font-medium">{item.label}</span>
                  </div>
                  {item.badge && (
                    <Badge 
                      variant="secondary" 
                      className={cn(
                        "text-xs px-2 py-0.5",
                        item.badge === "New" 
                          ? "bg-green-500/20 text-green-300 border-green-400/30"
                          : "bg-purple-500/20 text-purple-300 border-purple-400/30"
                      )}
                    >
                      {item.badge}
                    </Badge>
                  )}
                </div>
              </Button>
            </div>
          ))}
        </nav>

        <Separator className="my-6 border-white/10" />

        {/* Study Stats */}
        <div className="space-y-4">
          <h3 className="text-xs font-semibold text-white/80 uppercase tracking-wider">
            Today's Progress
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-400" />
                <span className="text-sm text-white/80">Study Time</span>
              </div>
              <span className="text-sm font-medium text-white">2.5h</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Brain className="h-4 w-4 text-purple-400" />
                <span className="text-sm text-white/80">AI Queries</span>
              </div>
              <span className="text-sm font-medium text-white">23</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-400" />
                <span className="text-sm text-white/80">Notes Created</span>
              </div>
              <span className="text-sm font-medium text-white">5</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-white/10 space-y-2">
        <Button
          variant="ghost"
          className="w-full justify-start text-white/80 hover:text-white hover:bg-white/10"
          onClick={() => router.push("/settings")}
        >
          <Settings className="h-5 w-5" />
          <span className="ml-3">Settings</span>
        </Button>
        
        <Button
          variant="ghost"
          className="w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-500/10"
          onClick={onSignOut}
        >
          <LogOut className="h-5 w-5" />
          <span className="ml-3">Sign Out</span>
        </Button>
      </div>
    </div>
  )
} 