"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { 
  Heart, Download, Plus, Share2, 
  FileText, BookOpen, Target, GraduationCap, 
  MoreVertical, Calendar, User, Eye,
  Image as ImageIcon, X
} from "lucide-react"

interface FileRecord {
  id: string
  name: string
  type: string
  size: number
  category: string
  description?: string
  page_count?: number
  like_count: number
  download_count: number
  user_liked: boolean
  created_at: string
  uploader: {
    full_name: string | null
    email: string
  }
  storage_path: string
}

interface FilePreviewModalProps {
  file: FileRecord
  onClose: () => void
  onAddToLearning: (fileId: string) => void
  onLike: (fileId: string) => void
  onDownload: (fileId: string) => void
  isEnrolled: boolean
}

export function FilePreviewModal({ 
  file, 
  onClose, 
  onAddToLearning, 
  onLike, 
  onDownload, 
  isEnrolled 
}: FilePreviewModalProps) {
  const [isLiking, setIsLiking] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [isAddingToLearning, setIsAddingToLearning] = useState(false)

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'lecture_notes': return <BookOpen className="h-4 w-4" />
      case 'summaries': return <FileText className="h-4 w-4" />
      case 'practice_materials': return <Target className="h-4 w-4" />
      case 'exams': return <GraduationCap className="h-4 w-4" />
      default: return <MoreVertical className="h-4 w-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      lecture_notes: 'bg-blue-500/10 text-blue-400 border-blue-400/20',
      summaries: 'bg-green-500/10 text-green-400 border-green-400/20',
      practice_materials: 'bg-purple-500/10 text-purple-400 border-purple-400/20',
      exams: 'bg-red-500/10 text-red-400 border-red-400/20',
      others: 'bg-gray-500/10 text-gray-400 border-gray-400/20'
    }
    return colors[category as keyof typeof colors] || colors.others
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileTypeIcon = (type: string) => {
    if (type.includes('pdf')) return <FileText className="h-8 w-8 text-red-400" />
    if (type.includes('word') || type.includes('document')) return <FileText className="h-8 w-8 text-blue-400" />
    if (type.includes('image')) return <ImageIcon className="h-8 w-8 text-green-400" />
    return <FileText className="h-8 w-8 text-gray-400" />
  }

  const getDisplayName = (uploader: FileRecord['uploader']) => {
    return uploader.full_name || uploader.email.split('@')[0] || 'Anonymous'
  }

  const handleLike = async () => {
    setIsLiking(true)
    try {
      await onLike(file.id)
    } finally {
      setIsLiking(false)
    }
  }

  const handleDownload = async () => {
    setIsDownloading(true)
    try {
      await onDownload(file.id)
    } finally {
      setIsDownloading(false)
    }
  }

  const handleAddToLearning = async () => {
    setIsAddingToLearning(true)
    try {
      await onAddToLearning(file.id)
    } finally {
      setIsAddingToLearning(false)
    }
  }

  const renderFilePreview = () => {
    // For now, we'll show a placeholder
    // In the future, we can implement actual file previews for PDFs, images, etc.
    return (
      <div className="h-full bg-gray-50 dark:bg-gray-900 rounded-lg flex flex-col items-center justify-center p-8">
        {getFileTypeIcon(file.type)}
        <h3 className="text-lg font-semibold mt-4 mb-2 text-center">{file.name}</h3>
        <p className="text-sm text-gray-500 text-center mb-4">
          {file.type.split('/')[1]?.toUpperCase() || 'FILE'} • {formatFileSize(file.size)}
        </p>
        
        {/* Preview placeholder text */}
        <div className="text-center text-gray-400 space-y-2">
          <Eye className="h-6 w-6 mx-auto" />
          <p className="text-sm">File preview will be available soon</p>
          <p className="text-xs">Support for PDF, Word, and Image previews coming</p>
        </div>
      </div>
    )
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[85vh] p-0">
        <div className="flex h-full">
          {/* Preview Area */}
          <div className="flex-1 p-6">
            {renderFilePreview()}
          </div>
          
          {/* Sidebar with file details and actions */}
          <div className="w-80 border-l bg-gray-50 dark:bg-gray-900 p-6 space-y-6 overflow-y-auto">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h2 className="font-semibold text-lg line-clamp-2 mb-2">{file.name}</h2>
                <Badge variant="outline" className={getCategoryColor(file.category)}>
                  {getCategoryIcon(file.category)}
                  <span className="ml-1 capitalize">{file.category.replace('_', ' ')}</span>
                </Badge>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Description */}
            {file.description && (
              <div>
                <h4 className="font-medium mb-2 text-sm">Description</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">{file.description}</p>
              </div>
            )}

            <Separator />

            {/* File Details */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">File Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Type:</span>
                  <span className="font-medium">{file.type.split('/')[1]?.toUpperCase() || 'Unknown'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Size:</span>
                  <span className="font-medium">{formatFileSize(file.size)}</span>
                </div>
                {file.page_count && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Pages:</span>
                    <span className="font-medium">{file.page_count}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-500">Uploaded:</span>
                  <span className="font-medium">{new Date(file.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Uploader Info */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Uploaded By</h4>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="font-medium text-sm">{getDisplayName(file.uploader)}</p>
                  <p className="text-xs text-gray-500">
                    <Calendar className="h-3 w-3 inline mr-1" />
                    {new Date(file.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Stats */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Statistics</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <div className="text-lg font-bold text-red-500">{file.like_count}</div>
                  <div className="text-xs text-gray-500">Likes</div>
                </div>
                <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <div className="text-lg font-bold text-blue-500">{file.download_count || 0}</div>
                  <div className="text-xs text-gray-500">Downloads</div>
                </div>
              </div>
            </div>

            {/* Actions */}
            {isEnrolled && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Actions</h4>
                  <div className="space-y-2">
                    <Button 
                      onClick={handleLike}
                      disabled={isLiking}
                      variant={file.user_liked ? "default" : "outline"}
                      className={cn(
                        "w-full",
                        file.user_liked && "bg-red-500 hover:bg-red-600 text-white"
                      )}
                    >
                      <Heart className={cn("w-4 h-4 mr-2", file.user_liked && "fill-current")} />
                      {isLiking ? 'Loading...' : file.user_liked ? 'Unlike' : 'Like'}
                    </Button>
                    
                    <Button 
                      onClick={handleAddToLearning}
                      disabled={isAddingToLearning}
                      variant="outline"
                      className="w-full"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isAddingToLearning ? 'Adding...' : 'Add to My Learning'}
                    </Button>
                    
                    <Button 
                      onClick={handleDownload}
                      disabled={isDownloading}
                      variant="outline"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      {isDownloading ? 'Downloading...' : 'Download'}
                    </Button>
                    
                    <Button 
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        // Copy file URL to clipboard
                        navigator.clipboard.writeText(window.location.href)
                      }}
                    >
                      <Share2 className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              </>
            )}

            {/* Non-enrolled user message */}
            {!isEnrolled && (
              <>
                <Separator />
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Enroll in this course to like, download, and add files to your learning path.
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 