import { createClient } from '@supabase/supabase-js'

// Create a single supabase client for interacting with your database
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Auth helper functions
export const auth = {
  signUp: async (email: string, password: string, userData?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    return { data, error }
  },

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  getUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession()
    return { session, error }
  },

  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`
      }
    })
    return { data, error }
  }
}

// Database helper functions
export const db = {
  // Users
  createProfile: async (userId: string, profileData: any) => {
    const { data, error } = await supabase
      .from('profiles')
      .insert([{ id: userId, ...profileData }])
    return { data, error }
  },

  getProfile: async (userId: string) => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  updateProfile: async (userId: string, profileData: any) => {
    const { data, error } = await supabase
      .from('profiles')
      .upsert({ id: userId, ...profileData })
      .select()
      .single()
    return { data, error }
  },

  checkUsernameAvailable: async (username: string) => {
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', username)
      .single()
    
    // If no data found, username is available
    // If data is found, username is taken
    // If there's an error (usually "no rows" error), username is available
    const available = !data || (error && (error as any).code === 'PGRST116')
    return { available, error: available ? null : error }
  },

  // Courses
  createCourse: async (courseData: any) => {
    const { data, error } = await supabase
      .from('courses')
      .insert([courseData])
      .select()
      .single()
    return { data, error }
  },

  getCourses: async (userId: string) => {
    const { data, error } = await supabase
      .from('courses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    return { data, error }
  },

  getEnrolledCourses: async (userId: string) => {
    // Get user's enrollments
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('course_enrollments')
      .select('course_id')
      .eq('user_id', userId)

    if (enrollmentError || !enrollments || enrollments.length === 0) {
      return { data: [], error: enrollmentError }
    }

    // Get course details for enrolled courses
    const courseIds = enrollments.map(e => e.course_id)
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('*')
      .in('id', courseIds)
      .order('created_at', { ascending: false })

    if (coursesError || !courses) {
      return { data: [], error: coursesError }
    }

    // Get enrollment counts for these courses
    const { data: allEnrollments } = await supabase
      .from('course_enrollments')
      .select('course_id')
      .in('course_id', courseIds)

    // Count enrollments per course
    const countMap: { [key: string]: number } = {}
    allEnrollments?.forEach(enrollment => {
      countMap[enrollment.course_id] = (countMap[enrollment.course_id] || 0) + 1
    })

    // Add enrollment counts to courses
    const processedData = courses.map(course => ({
      ...course,
      enrollment_count: countMap[course.id] || 0
    }))
    
    return { 
      data: processedData,
      error: null 
    }
  },

  getCourse: async (courseId: string) => {
    // First get the course
    const { data: course, error: courseError } = await supabase
      .from('courses')
      .select('*')
      .eq('id', courseId)
      .single()
    
    if (courseError || !course) {
      return { data: null, error: courseError }
    }

    // Then get enrollments with profiles separately
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('course_enrollments')
      .select(`
        id,
        user_id,
        is_tutor,
        enrolled_at
      `)
      .eq('course_id', courseId)

    // Get profiles for enrolled users
    const userIds = enrollments?.map(e => e.user_id) || []
    let profiles: any[] = []
    if (userIds.length > 0) {
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id, name, username, is_username_public')
        .in('id', userIds)
      profiles = profileData || []
    }

    // Combine the data
    const enrollmentsWithProfiles = enrollments?.map(enrollment => ({
      ...enrollment,
      profiles: profiles.find(p => p.id === enrollment.user_id) || null
    })) || []

    const data = {
      ...course,
      course_enrollments: enrollmentsWithProfiles
    }

    return { data, error: null }
  },

  getPublicCourses: async (university: string = 'TU-Darmstadt') => {
    // Get courses
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('*')
      .eq('is_public', true)
      .eq('university', university)
      .order('created_at', { ascending: false })

    if (coursesError || !courses) {
      return { data: [], error: coursesError }
    }

    // Get enrollment counts for each course
    const courseIds = courses.map(c => c.id)
    const { data: enrollmentCounts } = await supabase
      .from('course_enrollments')
      .select('course_id')
      .in('course_id', courseIds)

    // Count enrollments per course
    const countMap: { [key: string]: number } = {}
    enrollmentCounts?.forEach(enrollment => {
      countMap[enrollment.course_id] = (countMap[enrollment.course_id] || 0) + 1
    })

    // Add enrollment counts to courses
    const processedData = courses.map(course => ({
      ...course,
      enrollment_count: countMap[course.id] || 0
    }))
    
    return { data: processedData, error: null }
  },

  checkCourseExists: async (title: string, courseCode: string, university: string = 'TU-Darmstadt') => {
    const { data, error } = await supabase
      .from('courses')
      .select('id, title, course_code')
      .eq('is_public', true)
      .eq('university', university)
      .or(`title.eq.${title},course_code.eq.${courseCode}`)
    
    return { 
      exists: data && data.length > 0, 
      conflictingCourses: data || [],
      error 
    }
  },

  searchCourses: async (searchTerm: string, university: string = 'TU-Darmstadt') => {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        course_enrollments(count)
      `)
      .eq('is_public', true)
      .eq('university', university)
      .or(`title.ilike.%${searchTerm}%,course_code.ilike.%${searchTerm}%,professor_name.ilike.%${searchTerm}%`)
      .order('enrollment_count', { ascending: false })
    return { data, error }
  },

  enrollInCourse: async (courseId: string, userId: string) => {
    const { data, error } = await supabase
      .from('course_enrollments')
      .insert([{ course_id: courseId, user_id: userId }])
      .select()
    return { data, error }
  },

  unenrollFromCourse: async (courseId: string, userId: string) => {
    const { error } = await supabase
      .from('course_enrollments')
      .delete()
      .eq('course_id', courseId)
      .eq('user_id', userId)
    return { error }
  },

  isUserEnrolled: async (courseId: string, userId: string) => {
    const { data, error } = await supabase
      .from('course_enrollments')
      .select('id')
      .eq('course_id', courseId)
      .eq('user_id', userId)
      .single()
    
    return { 
      enrolled: !error && data !== null,
      error: error?.code === 'PGRST116' ? null : error // Ignore "no rows" error
    }
  },

  getUserEnrollments: async (userId: string) => {
    const { data, error } = await supabase
      .from('course_enrollments')
      .select('course_id')
      .eq('user_id', userId)
    
    return { 
      enrollments: data?.map(e => e.course_id) || [],
      error 
    }
  },

  getCourseEnrollments: async (courseId: string) => {
    // Get enrollments
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('course_enrollments')
      .select('*')
      .eq('course_id', courseId)

    if (enrollmentError || !enrollments || enrollments.length === 0) {
      return { data: [], error: enrollmentError }
    }

    // Get profiles for enrolled users
    const userIds = enrollments.map(e => e.user_id)
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, name, username, is_username_public')
      .in('id', userIds)

    // Combine data
    const enrollmentsWithProfiles = enrollments.map(enrollment => ({
      ...enrollment,
      profiles: profiles?.find(p => p.id === enrollment.user_id) || null
    }))

    return { data: enrollmentsWithProfiles, error: null }
  },

  getOnlineUsers: async (courseId: string) => {
    // Get online sessions
    const { data: sessions, error: sessionError } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('course_id', courseId)
      .eq('is_online', true)
      .gte('last_seen', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes

    if (sessionError || !sessions || sessions.length === 0) {
      return { data: [], error: sessionError }
    }

    // Get profiles for online users
    const userIds = sessions.map(s => s.user_id)
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, name, username, is_username_public')
      .in('id', userIds)

    // Combine data
    const sessionsWithProfiles = sessions.map(session => ({
      ...session,
      profiles: profiles?.find(p => p.id === session.user_id) || null
    }))

    return { data: sessionsWithProfiles, error: null }
  },

  updateUserSession: async (userId: string, courseId?: string, isOnline: boolean = true) => {
    const { data, error } = await supabase
      .from('user_sessions')
      .upsert({
        user_id: userId,
        course_id: courseId,
        last_seen: new Date().toISOString(),
        is_online: isOnline
      })
    return { data, error }
  },

  // Files
  uploadFile: async (file: File, bucket: string, path: string) => {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file)
    return { data, error }
  },

  createFileRecord: async (fileData: any) => {
    const { data, error } = await supabase
      .from('files')
      .insert([fileData])
    return { data, error }
  },

  getFiles: async (courseId: string) => {
    const { data, error } = await supabase
      .from('files')
      .select('*')
      .eq('course_id', courseId)
      .order('created_at', { ascending: false })
    return { data, error }
  },

  // Notes
  createNote: async (noteData: any) => {
    const { data, error } = await supabase
      .from('notes')
      .insert([noteData])
    return { data, error }
  },

  getNotes: async (userId: string, courseId?: string) => {
    let query = supabase
      .from('notes')
      .select('*')
      .eq('user_id', userId)

    if (courseId) {
      query = query.eq('course_id', courseId)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }
}

// Enhanced file operations for lecture notes
export const fileOperations = {
  // Upload file with category and metadata using proper folder structure
  uploadFile: async (courseId: string, file: File, metadata: {
    category: string;
    description?: string;
    page_count?: number;
    displayName?: string;
  }) => {
    try {
      // Get current user
      const { user } = await auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Get user profile for university
      const { data: profile } = await db.getProfile(user.id)
      if (!profile) throw new Error('Profile not found')

      // Get course details for folder structure
      const { data: course } = await db.getCourse(courseId)
      if (!course) throw new Error('Course not found')

      // Create folder structure: course-files/{university}/{course-title}/{filename}
      const sanitizeName = (name: string) => name.replace(/[^a-zA-Z0-9-_]/g, '_')
      const university = sanitizeName(profile.university || 'Unknown')
      const courseTitle = sanitizeName(course.title)
      const timestamp = Date.now()
      const fileName = `${timestamp}-${sanitizeName(file.name)}`
      const filePath = `${university}/${courseTitle}/${fileName}`
      
      // Upload to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('course-files')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })
      
      if (uploadError) throw uploadError

      // Get file URL
      const { data: { publicUrl } } = supabase.storage
        .from('course-files')
        .getPublicUrl(filePath)

      // Create database record
      const { data, error } = await supabase
        .from('files')
        .insert([{
          course_id: courseId,
          user_id: user.id,
          name: metadata.displayName || file.name,
          type: file.type,
          size: file.size,
          storage_path: filePath,
          category: metadata.category,
          description: metadata.description,
          page_count: metadata.page_count,
          processed: false,
          processing_status: 'pending'
        }])
        .select()
        .single()
      
      return { data, error, publicUrl }
    } catch (error) {
      return { data: null, error, publicUrl: null }
    }
  },

  // Get files with like status for user
  getCourseFiles: async (courseId: string, category?: string) => {
    try {
      const { user } = await auth.getUser()
      const userId = user?.id
      
      let query = supabase
        .from('files')
        .select(`
          *,
          uploader:profiles!user_id(full_name, email),
          user_liked:file_likes!left(user_id)
        `)
        .eq('course_id', courseId)
        .order('created_at', { ascending: false })

      // Apply category filter
      if (category && category !== 'all') {
        if (category === 'top_rated') {
          query = query.gte('like_count', 1).order('like_count', { ascending: false })
        } else {
          query = query.eq('category', category)
        }
      }

      const { data, error } = await query
      
      if (error) return { data: null, error }

      // Process data to include user_liked boolean and filter user's own likes
      const processedData = data?.map(file => ({
        ...file,
        user_liked: userId ? file.user_liked?.some((like: any) => like.user_id === userId) : false
      }))

      return { data: processedData, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Toggle file like
  toggleFileLike: async (fileId: string) => {
    try {
      const { user } = await auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Check if already liked
      const { data: existingLike } = await supabase
        .from('file_likes')
        .select('id')
        .eq('file_id', fileId)
        .eq('user_id', user.id)
        .single()

      if (existingLike) {
        // Unlike - delete the like
        const { error } = await supabase
          .from('file_likes')
          .delete()
          .eq('id', existingLike.id)
        
        return { data: { liked: false }, error }
      } else {
        // Like - insert new like
        const { error } = await supabase
          .from('file_likes')
          .insert([{ file_id: fileId, user_id: user.id }])
        
        return { data: { liked: true }, error }
      }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Download file and increment download count
  downloadFile: async (fileId: string) => {
    try {
      // Get file details
      const { data: file, error: fileError } = await supabase
        .from('files')
        .select('storage_path, name')
        .eq('id', fileId)
        .single()

      if (fileError || !file) throw fileError || new Error('File not found')

      // Get download URL
      const { data: { publicUrl } } = supabase.storage
        .from('course-files')
        .getPublicUrl(file.storage_path)

      // Increment download count
      await supabase.rpc('increment_download_count', { file_id: fileId })

      return { data: { url: publicUrl, fileName: file.name }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Add to learning path
  addToLearning: async (fileId: string, learningPathId?: string) => {
    try {
      const { user } = await auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // If no learning path specified, use default
      if (!learningPathId) {
        // Get file to determine course
        const { data: file } = await supabase
          .from('files')
          .select('course_id')
          .eq('id', fileId)
          .single()

        if (!file) throw new Error('File not found')

        // Get or create default learning path for this course
        let { data: learningPath } = await supabase
          .from('learning_paths')
          .select('id')
          .eq('user_id', user.id)
          .eq('course_id', file.course_id)
          .eq('is_default', true)
          .single()

        if (!learningPath) {
          // Create default learning path
          const { data: newPath } = await supabase
            .from('learning_paths')
            .insert([{
              user_id: user.id,
              course_id: file.course_id,
              title: 'My Learning Path',
              is_default: true
            }])
            .select('id')
            .single()
          
          learningPath = newPath
        }

        learningPathId = learningPath?.id
      }

      if (!learningPathId) throw new Error('Could not determine learning path')

      // Get current max order index
      const { data: maxOrder } = await supabase
        .from('learning_path_items')
        .select('order_index')
        .eq('learning_path_id', learningPathId)
        .order('order_index', { ascending: false })
        .limit(1)
        .single()

      const nextOrderIndex = (maxOrder?.order_index || 0) + 1

      // Add to learning path
      const { data, error } = await supabase
        .from('learning_path_items')
        .insert([{
          learning_path_id: learningPathId,
          item_type: 'file',
          item_id: fileId,
          order_index: nextOrderIndex
        }])
        .select()
        .single()

      return { data, error }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get file details with full metadata
  getFileDetails: async (fileId: string) => {
    try {
      const { user } = await auth.getUser()
      const userId = user?.id

      const { data, error } = await supabase
        .from('files')
        .select(`
          *,
          uploader:profiles!user_id(full_name, email),
          user_liked:file_likes!left(user_id),
          course:courses(title)
        `)
        .eq('id', fileId)
        .single()

      if (error) return { data: null, error }

      // Process user_liked status
      const processedData = {
        ...data,
        user_liked: userId ? data.user_liked?.some((like: any) => like.user_id === userId) : false
      }

      return { data: processedData, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

// Types for our database schema
export interface Profile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  university: string | null
  year_of_study: number | null
  major: string | null
  bio: string | null
  created_at: string
  updated_at: string
}

export interface Course {
  id: string
  user_id: string
  title: string
  description: string | null
  color: string
  is_public: boolean
  created_at: string
  updated_at: string
}

export interface FileRecord {
  id: string
  course_id: string
  name: string
  original_name: string
  file_type: string
  file_size: number
  storage_path: string
  processed: boolean
  processing_status: string
  processing_error: string | null
  upload_date: string
}

export interface Note {
  id: string
  user_id: string
  course_id: string
  title: string
  content: string | null
  tags: string[]
  is_ai_generated: boolean
  source_files: string[]
  created_at: string
  updated_at: string
} 