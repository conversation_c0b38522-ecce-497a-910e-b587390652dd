"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Header } from "@/components/layout/header"
import {
  Brain,
  Send,
  Upload,
  FileText,
  Image,
  Mic,
  Copy,
  ThumbsUp,
  ThumbsDown,
  BookOpen,
  Lightbulb,
  Zap,
  MessageSquare,
  Search,
  Filter,
  MoreVertical,
  RefreshCw,
  Download,
  Share
} from "lucide-react"

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  sources?: string[]
  attachments?: string[]
}

interface Suggestion {
  id: string
  text: string
  category: 'explain' | 'summarize' | 'question' | 'practice'
  icon: React.ReactNode
}

export default function AIAssistantPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "assistant",
      content: "Hello! I'm your AI Study Assistant. I can help you understand concepts, generate summaries, create practice questions, and much more based on your uploaded course materials. What would you like to explore today?",
      timestamp: "10:30 AM",
    }
  ])
  
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const suggestions: Suggestion[] = [
    {
      id: "1",
      text: "Explain neural networks in simple terms",
      category: "explain",
      icon: <Brain className="h-4 w-4" />
    },
    {
      id: "2", 
      text: "Summarize my marketing strategy notes",
      category: "summarize",
      icon: <FileText className="h-4 w-4" />
    },
    {
      id: "3",
      text: "Create practice questions from Chapter 5",
      category: "practice", 
      icon: <Lightbulb className="h-4 w-4" />
    },
    {
      id: "4",
      text: "What are the key differences between supervised and unsupervised learning?",
      category: "question",
      icon: <MessageSquare className="h-4 w-4" />
    }
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: `I understand you're asking about "${inputValue}". Based on your course materials, here's a comprehensive explanation:\n\nThis is a simulated AI response that would normally be generated using the Gemini API and your uploaded documents. The actual implementation would:\n\n• Process your query using natural language understanding\n• Search through your uploaded course materials using vector similarity\n• Generate a contextual response with source citations\n• Provide additional learning suggestions\n\nWould you like me to dive deeper into any specific aspect?`,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        sources: ["Computer Science Fundamentals - Chapter 3.pdf", "Neural Networks Lecture.pptx"]
      }
      
      setMessages(prev => [...prev, aiResponse])
      setIsLoading(false)
    }, 2000)
  }

  const handleSuggestionClick = (suggestion: Suggestion) => {
    setInputValue(suggestion.text)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container px-4 py-8">
        <div className="grid gap-8 lg:grid-cols-4">
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Document
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Summary
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Create Flashcards
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Practice Quiz
                </Button>
              </CardContent>
            </Card>

            {/* Active Courses */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Active Courses
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted cursor-pointer">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <BookOpen className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">CS Fundamentals</p>
                    <p className="text-xs text-muted-foreground">12 files</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted cursor-pointer">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <BookOpen className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Marketing Strategy</p>
                    <p className="text-xs text-muted-foreground">7 files</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted cursor-pointer">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <BookOpen className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">ML Basics</p>
                    <p className="text-xs text-muted-foreground">15 files</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Usage Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Today's Usage</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Queries</span>
                  <span className="font-medium">23 / 50</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Files Processed</span>
                  <span className="font-medium">7</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Study Time</span>
                  <span className="font-medium">2.5h</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Chat Area */}
          <div className="lg:col-span-3">
            <Card className="h-[calc(100vh-200px)] flex flex-col">
              <CardHeader className="border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-purple-600 p-2">
                      <Brain className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle>AI Study Assistant</CardTitle>
                      <CardDescription>
                        Powered by Gemini API • Context-aware • Source citations
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Clear Chat
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 overflow-y-auto p-6">
                <div className="space-y-6">
                  {messages.map((message) => (
                    <div key={message.id} className={`flex gap-4 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                      {message.type === 'assistant' && (
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="bg-purple-600 text-white">
                            AI
                          </AvatarFallback>
                        </Avatar>
                      )}
                      
                      <div className={`max-w-[80%] ${message.type === 'user' ? 'order-1' : ''}`}>
                        <div className={`rounded-lg p-4 ${
                          message.type === 'user' 
                            ? 'bg-primary text-primary-foreground ml-12' 
                            : 'bg-muted'
                        }`}>
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                          
                          {/* Sources */}
                          {message.sources && message.sources.length > 0 && (
                            <div className="mt-3 pt-3 border-t border-muted-foreground/20">
                              <p className="text-xs text-muted-foreground mb-2">Sources:</p>
                              <div className="flex flex-wrap gap-2">
                                {message.sources.map((source, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    <FileText className="h-3 w-3 mr-1" />
                                    {source}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs text-muted-foreground">{message.timestamp}</span>
                          {message.type === 'assistant' && (
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <ThumbsUp className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <ThumbsDown className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>

                      {message.type === 'user' && (
                        <Avatar className="w-8 h-8">
                          <AvatarFallback>JD</AvatarFallback>
                        </Avatar>
                      )}
                    </div>
                  ))}
                  
                  {isLoading && (
                    <div className="flex gap-4">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="bg-purple-600 text-white">
                          AI
                        </AvatarFallback>
                      </Avatar>
                      <div className="bg-muted rounded-lg p-4 max-w-[80%]">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          <span className="text-sm text-muted-foreground ml-2">AI is thinking...</span>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </CardContent>

              {/* Input Area */}
              <div className="border-t p-4">
                {/* Suggestions */}
                {messages.length <= 1 && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground mb-3">Try these suggestions:</p>
                    <div className="grid gap-2 md:grid-cols-2">
                      {suggestions.map((suggestion) => (
                        <Button
                          key={suggestion.id}
                          variant="outline"
                          className="justify-start h-auto p-3 text-left"
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          <div className="flex items-center gap-2">
                            {suggestion.icon}
                            <span className="text-sm">{suggestion.text}</span>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input */}
                <div className="flex gap-3">
                  <div className="flex-1 relative">
                    <Textarea
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyDown={handleKeyPress}
                      placeholder="Ask anything about your course materials..."
                      className="min-h-[50px] pr-32 resize-none"
                    />
                    <div className="absolute right-2 bottom-2 flex items-center gap-1">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Upload className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Mic className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Button 
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim() || isLoading}
                    className="self-end"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                
                <p className="text-xs text-muted-foreground mt-2">
                  AI responses are generated based on your uploaded course materials. Always verify important information.
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 