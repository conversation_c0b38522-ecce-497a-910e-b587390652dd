"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Upload,
  File,
  FileText,
  Image,
  Video,
  Music,
  X,
  CheckCircle,
  AlertCircle,
  Clock,
  Brain,
  Loader2
} from "lucide-react"

interface FileWithPreview extends File {
  preview?: string
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
}

interface FileUploadProps {
  onFilesAdded?: (files: FileWithPreview[]) => void
  onFileRemoved?: (fileId: string) => void
  maxFiles?: number
  maxFileSize?: number // in bytes
  acceptedFileTypes?: string[]
  courseId?: string
}

export function FileUpload({ 
  onFilesAdded,
  onFileRemoved,
  maxFiles = 10,
  maxFileSize = 50 * 1024 * 1024, // 50MB
  acceptedFileTypes = [
    '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.txt', '.md',
    '.jpg', '.jpeg', '.png', '.gif', '.webp',
    '.mp3', '.wav', '.mp4', '.mov', '.avi'
  ]
}: FileUploadProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [isDragActive, setIsDragActive] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      console.warn('Some files were rejected:', rejectedFiles)
      // You could show toast notifications here
    }

    // Convert accepted files to our format
    const newFiles: FileWithPreview[] = acceptedFiles.map((file) => ({
      ...file,
      id: Math.random().toString(36).substring(7),
      progress: 0,
      status: 'pending' as const,
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
    }))

    setFiles(prev => {
      const updated = [...prev, ...newFiles].slice(0, maxFiles)
      onFilesAdded?.(updated)
      return updated
    })

    // Simulate upload process
    newFiles.forEach(file => simulateUpload(file))
  }, [maxFiles, onFilesAdded])

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    maxFiles,
    maxSize: maxFileSize,
    multiple: true
  })

  const simulateUpload = async (file: FileWithPreview) => {
    // Update file status to uploading
    setFiles(prev => prev.map(f => 
      f.id === file.id ? { ...f, status: 'uploading' } : f
    ))

    // Simulate upload progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 200))
      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, progress } : f
      ))
    }

    // Simulate processing
    setFiles(prev => prev.map(f => 
      f.id === file.id ? { ...f, status: 'processing', progress: 100 } : f
    ))

    await new Promise(resolve => setTimeout(resolve, 2000))

    // Mark as completed
    setFiles(prev => prev.map(f => 
      f.id === file.id ? { ...f, status: 'completed' } : f
    ))
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId)
      onFileRemoved?.(fileId)
      return updated
    })
  }

  const getFileIcon = (file: FileWithPreview) => {
    if (file.type.startsWith('image/')) return <Image className="h-5 w-5" />
    if (file.type.startsWith('video/')) return <Video className="h-5 w-5" />
    if (file.type.startsWith('audio/')) return <Music className="h-5 w-5" />
    if (file.type.includes('pdf')) return <FileText className="h-5 w-5 text-red-500" />
    if (file.type.includes('word') || file.type.includes('document')) return <FileText className="h-5 w-5 text-blue-500" />
    if (file.type.includes('presentation') || file.type.includes('powerpoint')) return <FileText className="h-5 w-5 text-orange-500" />
    return <File className="h-5 w-5" />
  }

  const getStatusIcon = (status: FileWithPreview['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'uploading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'processing':
        return <Brain className="h-4 w-4 text-purple-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getStatusText = (status: FileWithPreview['status']) => {
    switch (status) {
      case 'pending': return 'Pending'
      case 'uploading': return 'Uploading'
      case 'processing': return 'Processing with AI'
      case 'completed': return 'Ready for AI queries'
      case 'error': return 'Error'
      default: return ''
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card className={`border-2 border-dashed transition-colors ${
        isDragActive || dropzoneActive 
          ? 'border-primary bg-primary/5' 
          : 'border-muted-foreground/25 hover:border-primary/50'
      }`}>
        <CardContent className="p-8">
          <div {...getRootProps()} className="cursor-pointer">
            <input {...getInputProps()} />
            <div className="flex flex-col items-center justify-center text-center">
              <div className={`rounded-full p-6 mb-4 ${
                isDragActive || dropzoneActive ? 'bg-primary/10' : 'bg-muted'
              }`}>
                <Upload className={`h-12 w-12 ${
                  isDragActive || dropzoneActive ? 'text-primary' : 'text-muted-foreground'
                }`} />
              </div>
              
              <h3 className="text-2xl font-semibold mb-2">
                {isDragActive || dropzoneActive ? 'Drop files here' : 'Upload Course Materials'}
              </h3>
              
              <p className="text-muted-foreground mb-4 max-w-md">
                Drag and drop your files here, or click to browse. 
                PDFs, documents, presentations, images, and audio files are supported.
              </p>
              
              <div className="flex flex-wrap justify-center gap-2 mb-4">
                <Badge variant="secondary">PDF</Badge>
                <Badge variant="secondary">Word</Badge>
                <Badge variant="secondary">PowerPoint</Badge>
                <Badge variant="secondary">Images</Badge>
                <Badge variant="secondary">Audio</Badge>
                <Badge variant="secondary">Video</Badge>
              </div>
              
              <Button size="lg">
                <Upload className="h-5 w-5 mr-2" />
                Choose Files
              </Button>
              
              <p className="text-xs text-muted-foreground mt-3">
                Max file size: {formatFileSize(maxFileSize)} • Max {maxFiles} files
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Uploaded Files ({files.length})
            </CardTitle>
            <CardDescription>
              Files are automatically processed for AI analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((file) => (
                <div key={file.id} className="flex items-center gap-4 p-4 rounded-lg border bg-card">
                  {/* File Icon */}
                  <div className="flex-shrink-0">
                    {file.preview ? (
                      <img 
                        src={file.preview} 
                        alt={file.name}
                        className="h-12 w-12 object-cover rounded"
                      />
                    ) : (
                      <div className="h-12 w-12 rounded bg-muted flex items-center justify-center">
                        {getFileIcon(file)}
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium truncate">{file.name}</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => removeFile(file.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                      <span>{formatFileSize(file.size)}</span>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(file.status)}
                        <span>{getStatusText(file.status)}</span>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {(file.status === 'uploading' || file.status === 'processing') && (
                      <div className="space-y-1">
                        <Progress value={file.progress} className="h-2" />
                        <p className="text-xs text-muted-foreground">
                          {file.status === 'uploading' 
                            ? `Uploading... ${file.progress}%`
                            : 'Processing with AI...'
                          }
                        </p>
                      </div>
                    )}

                    {/* Completed State */}
                    {file.status === 'completed' && (
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          <Brain className="h-3 w-3 mr-1" />
                          AI Ready
                        </Badge>
                        <span className="text-xs text-green-600">
                          File processed and ready for AI queries
                        </span>
                      </div>
                    )}

                    {/* Error State */}
                    {file.status === 'error' && (
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive" className="text-xs">
                          Error
                        </Badge>
                        <span className="text-xs text-red-600">
                          {file.error || 'Failed to process file'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Upload Summary */}
            <div className="mt-6 p-4 rounded-lg bg-muted/50">
              <div className="flex items-center justify-between text-sm">
                <span>Total files: {files.length}</span>
                <span>
                  Completed: {files.filter(f => f.status === 'completed').length} / {files.length}
                </span>
              </div>
              <Progress 
                value={(files.filter(f => f.status === 'completed').length / files.length) * 100} 
                className="mt-2 h-2"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Processing Info */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="rounded-lg bg-purple-600 p-3">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold mb-2">AI Processing</h3>
              <p className="text-sm text-muted-foreground mb-3">
                Your files are automatically processed using advanced AI to extract key information, 
                create embeddings, and enable intelligent search and Q&A capabilities.
              </p>
              <ul className="text-sm space-y-1">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Text extraction and chunking</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Vector embedding generation</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Semantic search optimization</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Ready for AI Q&A and analysis</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 