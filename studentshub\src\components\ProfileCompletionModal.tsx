"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { db } from "@/lib/supabase"
import { 
  Brain, 
  User, 
  School, 
  Calendar,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react"

interface ProfileCompletionModalProps {
  user: any
  onComplete: () => void
}

export default function ProfileCompletionModal({ user, onComplete }: ProfileCompletionModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null)
  const [checkingUsername, setCheckingUsername] = useState(false)
  
  const [formData, setFormData] = useState({
    username: "",
    university: "TU-Darmstadt",
    currentSemester: "",
    isUsernamePublic: false
  })

  const semesters = [
    "1st Semester", "2nd Semester", "3rd Semester", "4th Semester",
    "5th Semester", "6th Semester", "7th Semester", "8th Semester",
    "9th Semester", "10th Semester", "Master 1st Semester", "Master 2nd Semester",
    "Master 3rd Semester", "Master 4th Semester", "PhD Candidate"
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))

    if (name === 'username' && value.length > 0) {
      checkUsernameAvailability(value)
    }
  }

  const checkUsernameAvailability = async (username: string) => {
    if (username.length < 3) {
      setUsernameAvailable(null)
      return
    }

    setCheckingUsername(true)
    try {
      const { available } = await db.checkUsernameAvailable(username)
      setUsernameAvailable(available)
    } catch (err) {
      console.error('Error checking username:', err)
      // If there's an error checking, assume it's available for now
      setUsernameAvailable(true)
    } finally {
      setCheckingUsername(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // Validation
    if (!formData.username || formData.username.length < 3) {
      setError("Username must be at least 3 characters long")
      setIsLoading(false)
      return
    }

    if (usernameAvailable === false) {
      setError("Username is not available")
      setIsLoading(false)
      return
    }

    if (!formData.currentSemester) {
      setError("Please select your current semester")
      setIsLoading(false)
      return
    }

    try {
      const updateData = {
        name: user.user_metadata?.full_name || user.user_metadata?.name || user.email || "Unknown User",
        email: user.email,
        username: formData.username,
        university: formData.university,
        current_semester: formData.currentSemester,
        is_username_public: formData.isUsernamePublic,
        is_profile_complete: true
      }

      const { error: updateError } = await db.updateProfile(user.id, updateData)

      if (updateError) {
        setError(updateError.message)
      } else {
        onComplete()
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-purple-400/10 animate-pulse"
              style={{
                width: Math.random() * 3 + 1 + "px",
                height: Math.random() * 3 + 1 + "px",
                left: Math.random() * 100 + "%",
                top: Math.random() * 100 + "%",
                animationDelay: Math.random() * 5 + "s",
                animationDuration: Math.random() * 3 + 2 + "s"
              }}
            />
          ))}
        </div>
      </div>

      <Card className="w-full max-w-lg bg-slate-900/95 backdrop-blur-xl border border-purple-500/20 shadow-2xl relative z-10">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center animate-glow">
              <Brain className="w-7 h-7 text-white" />
            </div>
            <span className="text-2xl font-bold text-gradient-ultra">StudentsHub</span>
          </div>
          <CardTitle className="text-2xl font-bold text-white">Complete Your Profile</CardTitle>
          <CardDescription className="text-white/70">
            Help us personalize your StudentsHub experience
          </CardDescription>
        </CardHeader>

        <CardContent>
          {error && (
            <div className="mb-4 p-3 rounded-lg bg-red-500/20 border border-red-500/30 flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-400" />
              <span className="text-red-300 text-sm">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Username */}
            <div>
              <Label htmlFor="username" className="text-white flex items-center gap-2">
                <User className="w-4 h-4" />
                Username
              </Label>
              <div className="relative">
                <Input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={formData.username}
                  onChange={handleInputChange}
                  className="mt-1 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="johndoe"
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {checkingUsername && <Loader2 className="w-4 h-4 animate-spin text-white/50" />}
                  {!checkingUsername && usernameAvailable === true && (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  )}
                  {!checkingUsername && usernameAvailable === false && (
                    <AlertCircle className="w-4 h-4 text-red-400" />
                  )}
                </div>
              </div>
              <div className="flex items-center mt-2 cursor-pointer" onClick={() => setFormData(prev => ({ ...prev, isUsernamePublic: !prev.isUsernamePublic }))}>
                <div className="relative">
                  <input
                    id="isUsernamePublic"
                    name="isUsernamePublic"
                    type="checkbox"
                    checked={formData.isUsernamePublic}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`w-5 h-5 rounded-lg border-2 transition-all duration-200 flex items-center justify-center ${
                    formData.isUsernamePublic 
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 border-purple-500' 
                      : 'bg-white/10 border-white/30 hover:border-white/50'
                  }`}>
                    {formData.isUsernamePublic && (
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    )}
                  </div>
                </div>
                <label htmlFor="isUsernamePublic" className="ml-3 block text-sm text-white cursor-pointer">
                  Make username visible to other users
                </label>
              </div>
            </div>

            {/* University */}
            <div>
              <Label htmlFor="university" className="text-white flex items-center gap-2">
                <School className="w-4 h-4" />
                University
              </Label>
              <select
                id="university"
                name="university"
                value={formData.university}
                onChange={handleInputChange}
                className="mt-1 w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
              >
                <option value="TU-Darmstadt" className="bg-slate-800 text-white">TU-Darmstadt</option>
                <option value="Other" className="bg-slate-800 text-white">Other University</option>
              </select>
            </div>

            {/* Current Semester */}
            <div>
              <Label htmlFor="currentSemester" className="text-white flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Current Semester
              </Label>
              <select
                id="currentSemester"
                name="currentSemester"
                value={formData.currentSemester}
                onChange={handleInputChange}
                required
                className="mt-1 w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
              >
                <option value="" className="bg-slate-800 text-white">Select your semester</option>
                {semesters.map((semester) => (
                  <option key={semester} value={semester} className="bg-slate-800 text-white">{semester}</option>
                ))}
              </select>
            </div>

            <Separator className="border-white/20" />

            <div className="bg-purple-500/20 p-4 rounded-lg border border-purple-500/30">
              <h4 className="font-medium text-white mb-2">What's Next?</h4>
              <div className="space-y-2 text-sm text-white/70">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
                  <span>Access all public TU-Darmstadt courses</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-pink-400 rounded-full"></div>
                  <span>Create and share your own courses</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                  <span>Connect with fellow students</span>
                </div>
              </div>
            </div>

                          <Button 
                type="submit" 
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white py-3 rounded-lg shadow-ultra btn-ultra"
                disabled={isLoading || usernameAvailable === false}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Completing Profile...
                </>
              ) : (
                "Complete Profile & Continue"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 