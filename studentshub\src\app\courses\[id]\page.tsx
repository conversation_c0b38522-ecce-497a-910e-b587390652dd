"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { auth, db } from "@/lib/supabase"
import { Loader2 } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { NavigationSidebar } from "@/components/layout/navigation-sidebar"
import { AIChatSidebar } from "@/components/layout/ai-chat-sidebar"
import { CourseContent } from "@/components/course/course-content"

interface Course {
  id: string
  title: string
  description: string
  university: string
  is_public: boolean
  course_code: string
  semester: string
  professor_name: string
  enrollment_count: number
  user_id: string
  created_at: string
  color: string
  course_enrollments: {
    id: string
    user_id: string
    is_tutor: boolean
    profiles: {
      name: string
      username: string
      is_username_public: boolean
    }
  }[]
}

export default function CoursePage() {
  const router = useRouter()
  const params = useParams()
  const courseId = params.id as string
  
  const [user, setUser] = useState<any>(null)
  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEnrolled, setIsEnrolled] = useState(false)
  const [isOwner, setIsOwner] = useState(false)

  useEffect(() => {
    checkUserAndLoadCourse()
  }, [courseId])

  // Track user session when they're on the course page
  useEffect(() => {
    if (user && courseId && isEnrolled) {
      // Update session immediately
      db.updateUserSession(user.id, courseId)
      
      // Update session every 30 seconds while on the page
      const interval = setInterval(() => {
        db.updateUserSession(user.id, courseId)
      }, 30000) // 30 seconds

      // Cleanup function to mark user as offline when leaving
      return () => {
        clearInterval(interval)
        // Mark user as offline when leaving the course page
        db.updateUserSession(user.id, courseId, false) // false = offline
      }
    }
  }, [user, courseId, isEnrolled])

  const checkUserAndLoadCourse = async () => {
    const { user, error } = await auth.getUser()
    
    if (error || !user) {
      router.push("/login")
      return
    }

    setUser(user)
    await loadCourse(user.id)
    setLoading(false)
  }

  const loadCourse = async (userId: string) => {
    try {
      // Load course details
      const { data: courseData, error: courseError } = await db.getCourse(courseId)
      
      if (courseError || !courseData) {
        console.error('Course not found:', courseError)
        router.push("/courses")
        return
      }

      setCourse(courseData)
      setIsOwner(courseData.user_id === userId)

      // Check if user is enrolled
      const { enrolled } = await db.isUserEnrolled(courseId, userId)
      setIsEnrolled(enrolled || courseData.user_id === userId) // Owner is always "enrolled"
      
    } catch (err) {
      console.error('Error loading course:', err)
      router.push("/courses")
    }
  }

  const handleSignOut = async () => {
    await auth.signOut()
    router.push("/")
  }

  const handleExpandAIChat = () => {
    router.push("/ai-assistant")
  }

  const handleEnroll = async () => {
    if (!user || !course) return
    
    try {
      const { error } = await db.enrollInCourse(course.id, user.id)
      if (!error) {
        setIsEnrolled(true)
        // Reload course to update enrollment count
        await loadCourse(user.id)
      }
    } catch (err) {
      console.error('Error enrolling:', err)
    }
  }

  const handleUnenroll = async () => {
    if (!user || !course || isOwner) return
    
    try {
      const { error } = await db.unenrollFromCourse(course.id, user.id)
      if (!error) {
        setIsEnrolled(false)
        // Reload course to update enrollment count
        await loadCourse(user.id)
      }
    } catch (err) {
      console.error('Error unenrolling:', err)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-white mx-auto mb-4" />
          <p className="text-white">Loading course...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-800 via-purple-800 to-pink-800 flex items-center justify-center">
        <div className="text-center">
          <p className="text-white">Course not found</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout
      leftSidebar={
        <NavigationSidebar 
          user={user} 
          profile={null} 
          onSignOut={handleSignOut}
        />
      }
      rightSidebar={
        <AIChatSidebar 
          onExpand={handleExpandAIChat}
        />
      }
    >
      <CourseContent 
        course={course}
        user={user}
        isEnrolled={isEnrolled}
        isOwner={isOwner}
        onEnroll={handleEnroll}
        onUnenroll={handleUnenroll}
      />
    </DashboardLayout>
  )
} 